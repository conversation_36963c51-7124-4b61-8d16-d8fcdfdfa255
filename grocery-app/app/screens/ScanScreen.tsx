import React, { useState, useEffect, useContext } from 'react';
import { Text, View, StyleSheet, TouchableOpacity, Alert, Platform, TextInput } from 'react-native';
import { ItemContext } from '../context/ItemContext';
import { router } from 'expo-router';

// Conditional imports for different platforms
let BarCodeScanner: any;
let BarCodeScannerResult: any;

if (Platform.OS !== 'web') {
  try {
    const barcodeModule = require('expo-barcode-scanner');
    BarCodeScanner = barcodeModule.BarCodeScanner;
    BarCodeScannerResult = barcodeModule.BarCodeScannerResult;
  } catch (error) {
    console.warn('Barcode scanner not available on this platform');
  }
}

function ScanScreen() {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [manualBarcode, setManualBarcode] = useState('');
  const { items, getItemsByBarcode } = useContext(ItemContext);

  useEffect(() => {
    if (Platform.OS !== 'web' && BarCodeScanner) {
      const getBarCodeScannerPermissions = async () => {
        try {
          const { status } = await BarCodeScanner.requestPermissionsAsync();
          setHasPermission(status === 'granted');
        } catch (error) {
          console.error('Error requesting camera permissions:', error);
          setHasPermission(false);
        }
      };

      getBarCodeScannerPermissions();
    } else {
      // For web, we'll use manual input
      setHasPermission(true);
    }
  }, []);

  const handleBarCodeScanned = ({ type, data }: any) => {
    setScanned(true);
    processBarcode(data);
  };

  const processBarcode = (barcode: string) => {
    if (!barcode.trim()) return;

    // Check if item with this barcode already exists
    const existingItem = getItemsByBarcode(barcode);

    if (existingItem) {
      Alert.alert(
        "Barcode Found",
        `Barcode ${barcode} belongs to "${existingItem.name}"`,
        [
          {
            text: "View Item",
            onPress: () => {
              router.push({
                pathname: "/items",
              });
            }
          },
          {
            text: "Scan Again",
            onPress: () => {
              setScanned(false);
              setManualBarcode('');
            }
          }
        ]
      );
    } else {
      Alert.alert(
        "New Barcode",
        `Would you like to create a new item with barcode ${barcode}?`,
        [
          {
            text: "Cancel",
            style: "cancel",
            onPress: () => {
              setScanned(false);
              setManualBarcode('');
            }
          },
          {
            text: "Create Item",
            onPress: () => {
              router.push({
                pathname: "/add-item",
                params: { barcode: barcode }
              });
            }
          }
        ]
      );
    }
  };

  const handleManualSubmit = () => {
    if (manualBarcode.trim()) {
      processBarcode(manualBarcode.trim());
    }
  };

  if (hasPermission === null) {
    return <Text style={styles.permissionText}>Requesting camera permission...</Text>;
  }

  if (hasPermission === false && Platform.OS !== 'web') {
    return <Text style={styles.permissionText}>No access to camera. Please enable camera permissions in your device settings.</Text>;
  }

  // Web version with manual barcode input
  if (Platform.OS === 'web') {
    return (
      <View style={styles.container}>
        <View style={styles.webContainer}>
          <Text style={styles.webTitle}>Barcode Scanner</Text>
          <Text style={styles.webSubtitle}>Camera scanning is not available on web. Please enter the barcode manually:</Text>

          <TextInput
            style={styles.barcodeInput}
            placeholder="Enter barcode number"
            value={manualBarcode}
            onChangeText={setManualBarcode}
            keyboardType="numeric"
          />

          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleManualSubmit}
          >
            <Text style={styles.submitButtonText}>Process Barcode</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.itemsButton}
            onPress={() => router.push('/items')}
          >
            <Text style={styles.itemsButtonText}>View Items</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Native version with camera scanner
  return (
    <View style={styles.container}>
      {BarCodeScanner && (
        <BarCodeScanner
          onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
          style={StyleSheet.absoluteFillObject}
        />
      )}

      {scanned && (
        <TouchableOpacity
          style={styles.scanAgainButton}
          onPress={() => setScanned(false)}
        >
          <Text style={styles.scanAgainText}>Tap to Scan Again</Text>
        </TouchableOpacity>
      )}

      <View style={styles.overlay}>
        <View style={styles.scanWindow} />
      </View>

      <View style={styles.helpTextContainer}>
        <Text style={styles.helpText}>Position a barcode within the frame to scan</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  permissionText: {
    flex: 1,
    textAlign: 'center',
    marginTop: 100,
    padding: 20,
    fontSize: 16,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  scanWindow: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: 'white',
    backgroundColor: 'transparent',
  },
  scanAgainButton: {
    position: 'absolute',
    bottom: 100,
    alignSelf: 'center',
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 30,
  },
  scanAgainText: {
    fontSize: 18,
    color: 'white',
    fontWeight: 'bold',
  },
  helpTextContainer: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  helpText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 10,
    borderRadius: 5,
  },
  // Web-specific styles
  webContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  webTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  webSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
    lineHeight: 22,
  },
  barcodeInput: {
    width: '100%',
    maxWidth: 300,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    padding: 15,
    fontSize: 18,
    backgroundColor: 'white',
    marginBottom: 20,
    textAlign: 'center',
  },
  submitButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingHorizontal: 30,
    paddingVertical: 15,
    marginBottom: 15,
    width: '100%',
    maxWidth: 300,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  itemsButton: {
    backgroundColor: '#f2f2f2',
    borderRadius: 12,
    paddingHorizontal: 30,
    paddingVertical: 15,
    width: '100%',
    maxWidth: 300,
    alignItems: 'center',
  },
  itemsButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ScanScreen;
