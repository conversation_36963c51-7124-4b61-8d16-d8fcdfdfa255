{"version": 3, "sources": ["../../../../../src/run/ios/appleDevice/installOnDeviceAsync.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { Ora } from 'ora';\nimport os from 'os';\nimport path from 'path';\n\nimport * as AppleDevice from './AppleDevice';\nimport * as devicectl from '../../../start/platforms/ios/devicectl';\nimport { ensureDirectory } from '../../../utils/dir';\nimport { CommandError } from '../../../utils/errors';\nimport { isInteractive } from '../../../utils/interactive';\nimport { ora } from '../../../utils/ora';\nimport { confirmAsync } from '../../../utils/prompts';\n\n/** Get the app_delta folder for faster subsequent rebuilds on devices. */\nexport function getAppDeltaDirectory(bundleId: string): string {\n  // TODO: Maybe use .expo folder instead for debugging\n  // TODO: Reuse existing folder from xcode?\n  const deltaFolder = path.join(os.tmpdir(), 'ios', 'app-delta', bundleId);\n  ensureDirectory(deltaFolder);\n  return deltaFolder;\n}\n\n/**\n * Wraps the apple device method for installing and running an app,\n * adds indicator and retry loop for when the device is locked.\n */\nexport async function installOnDeviceAsync(props: {\n  bundle: string;\n  bundleIdentifier: string;\n  appDeltaDirectory: string;\n  udid: string;\n  deviceName: string;\n}): Promise<void> {\n  const { bundle, bundleIdentifier, appDeltaDirectory, udid, deviceName } = props;\n  let indicator: Ora | undefined;\n\n  try {\n    // TODO: Connect for logs\n    await AppleDevice.runOnDevice({\n      udid,\n      appPath: bundle,\n      bundleId: bundleIdentifier,\n      waitForApp: false,\n      deltaPath: appDeltaDirectory,\n      onProgress({\n        status,\n        isComplete,\n        progress,\n      }: {\n        status: string;\n        isComplete: boolean;\n        progress: number;\n      }) {\n        if (!indicator) {\n          indicator = ora(status).start();\n        }\n        indicator.text = `${chalk.bold(status)} ${progress}%`;\n        if (isComplete) {\n          indicator.succeed();\n        }\n      },\n    });\n  } catch (error: any) {\n    if (error instanceof CommandError) {\n      if (error.code === 'APPLE_DEVICE_USBMUXD') {\n        // Couldn't find device, could be OTA...\n        // Fallback on much slower devicectl method which supports OTA installs.\n        if (devicectl.hasDevicectlEverBeenInstalled()) {\n          // This should never happen.\n          if (indicator) {\n            indicator.clear();\n          }\n          return await devicectl.installAndLaunchAppAsync(props);\n        }\n      }\n    }\n\n    if (indicator) {\n      indicator.fail();\n    }\n    if (error.code === 'APPLE_DEVICE_LOCKED') {\n      // Get the app name from the binary path.\n      const appName = path.basename(bundle).split('.')[0] ?? 'app';\n      if (\n        isInteractive() &&\n        (await confirmAsync({\n          message: `Cannot launch ${appName} because the device is locked. Unlock ${deviceName} to continue...`,\n          initial: true,\n        }))\n      ) {\n        return installOnDeviceAsync(props);\n      }\n      throw new CommandError(\n        `Cannot launch ${appName} on ${deviceName} because the device is locked.`\n      );\n    }\n    throw error;\n  }\n}\n"], "names": ["getAppDeltaDirectory", "installOnDeviceAsync", "bundleId", "deltaFolder", "path", "join", "os", "tmpdir", "ensureDirectory", "props", "bundle", "bundleIdentifier", "appDeltaDirectory", "udid", "deviceName", "indicator", "AppleDevice", "runOnDevice", "appPath", "waitForApp", "deltaPath", "onProgress", "status", "isComplete", "progress", "ora", "start", "text", "chalk", "bold", "succeed", "error", "CommandError", "code", "devicectl", "hasDevicectlEverBeenInstalled", "clear", "installAndLaunchAppAsync", "fail", "appName", "basename", "split", "isInteractive", "<PERSON><PERSON><PERSON>", "message", "initial"], "mappings": ";;;;;;;;;;;IAcgBA,oBAAoB;eAApBA;;IAYMC,oBAAoB;eAApBA;;;;gEA1BJ;;;;;;;gEAEH;;;;;;;gEACE;;;;;;qEAEY;mEACF;qBACK;wBACH;6BACC;qBACV;yBACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGtB,SAASD,qBAAqBE,QAAgB;IACnD,qDAAqD;IACrD,0CAA0C;IAC1C,MAAMC,cAAcC,eAAI,CAACC,IAAI,CAACC,aAAE,CAACC,MAAM,IAAI,OAAO,aAAaL;IAC/DM,IAAAA,oBAAe,EAACL;IAChB,OAAOA;AACT;AAMO,eAAeF,qBAAqBQ,KAM1C;IACC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,IAAI,EAAEC,UAAU,EAAE,GAAGL;IAC1E,IAAIM;IAEJ,IAAI;QACF,yBAAyB;QACzB,MAAMC,aAAYC,WAAW,CAAC;YAC5BJ;YACAK,SAASR;YACTR,UAAUS;YACVQ,YAAY;YACZC,WAAWR;YACXS,YAAW,EACTC,MAAM,EACNC,UAAU,EACVC,QAAQ,EAKT;gBACC,IAAI,CAACT,WAAW;oBACdA,YAAYU,IAAAA,QAAG,EAACH,QAAQI,KAAK;gBAC/B;gBACAX,UAAUY,IAAI,GAAG,GAAGC,gBAAK,CAACC,IAAI,CAACP,QAAQ,CAAC,EAAEE,SAAS,CAAC,CAAC;gBACrD,IAAID,YAAY;oBACdR,UAAUe,OAAO;gBACnB;YACF;QACF;IACF,EAAE,OAAOC,OAAY;QACnB,IAAIA,iBAAiBC,oBAAY,EAAE;YACjC,IAAID,MAAME,IAAI,KAAK,wBAAwB;gBACzC,wCAAwC;gBACxC,wEAAwE;gBACxE,IAAIC,WAAUC,6BAA6B,IAAI;oBAC7C,4BAA4B;oBAC5B,IAAIpB,WAAW;wBACbA,UAAUqB,KAAK;oBACjB;oBACA,OAAO,MAAMF,WAAUG,wBAAwB,CAAC5B;gBAClD;YACF;QACF;QAEA,IAAIM,WAAW;YACbA,UAAUuB,IAAI;QAChB;QACA,IAAIP,MAAME,IAAI,KAAK,uBAAuB;YACxC,yCAAyC;YACzC,MAAMM,UAAUnC,eAAI,CAACoC,QAAQ,CAAC9B,QAAQ+B,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;YACvD,IACEC,IAAAA,0BAAa,OACZ,MAAMC,IAAAA,qBAAY,EAAC;gBAClBC,SAAS,CAAC,cAAc,EAAEL,QAAQ,sCAAsC,EAAEzB,WAAW,eAAe,CAAC;gBACrG+B,SAAS;YACX,IACA;gBACA,OAAO5C,qBAAqBQ;YAC9B;YACA,MAAM,IAAIuB,oBAAY,CACpB,CAAC,cAAc,EAAEO,QAAQ,IAAI,EAAEzB,WAAW,8BAA8B,CAAC;QAE7E;QACA,MAAMiB;IACR;AACF"}