{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/client/AFCClient.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Debug from 'debug';\nimport * as fs from 'fs';\nimport { Socket } from 'net';\nimport * as path from 'path';\nimport { promisify } from 'util';\n\nimport { ServiceClient } from './ServiceClient';\nimport { CommandError } from '../../../../utils/errors';\nimport {\n  AFC_FILE_OPEN_FLAGS,\n  AFC_OPS,\n  AFC_STATUS,\n  AFCError,\n  AFCProtocolClient,\n  AFCResponse,\n} from '../protocol/AFCProtocol';\n\nconst debug = Debug('expo:apple-device:client:afc');\nconst MAX_OPEN_FILES = 240;\n\nexport class AFCClient extends ServiceClient<AFCProtocolClient> {\n  constructor(public socket: Socket) {\n    super(socket, new AFCProtocolClient(socket));\n  }\n\n  async getFileInfo(path: string): Promise<string[]> {\n    debug(`getFileInfo: ${path}`);\n\n    const response = await this.protocolClient.sendMessage({\n      operation: AFC_OPS.GET_FILE_INFO,\n      data: toCString(path),\n    });\n    debug(`getFileInfo:response: %O`, response);\n\n    const strings: string[] = [];\n    let currentString = '';\n    const tokens = response.data;\n    tokens.forEach((token) => {\n      if (token === 0) {\n        strings.push(currentString);\n        currentString = '';\n      } else {\n        currentString += String.fromCharCode(token);\n      }\n    });\n    return strings;\n  }\n\n  async writeFile(fd: Buffer, data: Buffer): Promise<AFCResponse> {\n    debug(`writeFile: ${Array.prototype.toString.call(fd)} data size: ${data.length}`);\n\n    const response = await this.protocolClient.sendMessage({\n      operation: AFC_OPS.FILE_WRITE,\n      data: fd,\n      payload: data,\n    });\n\n    debug(`writeFile:response:`, response);\n    return response;\n  }\n\n  protected async openFile(path: string): Promise<Buffer> {\n    debug(`openFile: ${path}`);\n    // mode + path + null terminator\n    const data = Buffer.alloc(8 + path.length + 1);\n    // write mode\n    data.writeUInt32LE(AFC_FILE_OPEN_FLAGS.WRONLY, 0);\n    // then path to file\n    toCString(path).copy(data, 8);\n\n    const response = await this.protocolClient.sendMessage({\n      operation: AFC_OPS.FILE_OPEN,\n      data,\n    });\n\n    // debug(`openFile:response:`, response);\n\n    if (response.operation === AFC_OPS.FILE_OPEN_RES) {\n      return response.data;\n    }\n\n    throw new CommandError(\n      'APPLE_DEVICE_AFC',\n      `There was an unknown error opening file ${path}, response: ${Array.prototype.toString.call(\n        response.data\n      )}`\n    );\n  }\n\n  protected async closeFile(fd: Buffer): Promise<AFCResponse> {\n    debug(`closeFile fd: ${Array.prototype.toString.call(fd)}`);\n    const response = await this.protocolClient.sendMessage({\n      operation: AFC_OPS.FILE_CLOSE,\n      data: fd,\n    });\n\n    debug(`closeFile:response:`, response);\n    return response;\n  }\n\n  protected async uploadFile(srcPath: string, destPath: string): Promise<void> {\n    debug(`uploadFile: ${srcPath}, ${destPath}`);\n\n    // read local file and get fd of destination\n    const [srcFile, destFile] = await Promise.all([\n      await promisify(fs.readFile)(srcPath),\n      await this.openFile(destPath),\n    ]);\n\n    try {\n      await this.writeFile(destFile, srcFile);\n      await this.closeFile(destFile);\n    } catch (err) {\n      await this.closeFile(destFile);\n      throw err;\n    }\n  }\n\n  async makeDirectory(path: string): Promise<AFCResponse> {\n    debug(`makeDirectory: ${path}`);\n\n    const response = await this.protocolClient.sendMessage({\n      operation: AFC_OPS.MAKE_DIR,\n      data: toCString(path),\n    });\n\n    debug(`makeDirectory:response:`, response);\n    return response;\n  }\n\n  async uploadDirectory(srcPath: string, destPath: string): Promise<void> {\n    debug(`uploadDirectory: ${srcPath}`);\n    await this.makeDirectory(destPath);\n\n    // AFC doesn't seem to give out more than 240 file handles,\n    // so we delay any requests that would push us over until more open up\n    let numOpenFiles = 0;\n    const pendingFileUploads: (() => void)[] = [];\n    const _this = this;\n    return uploadDir(srcPath);\n\n    async function uploadDir(dirPath: string): Promise<void> {\n      const promises: Promise<void>[] = [];\n      for (const file of fs.readdirSync(dirPath)) {\n        const filePath = path.join(dirPath, file);\n        const remotePath = path.join(destPath, path.relative(srcPath, filePath));\n        if (fs.lstatSync(filePath).isDirectory()) {\n          promises.push(_this.makeDirectory(remotePath).then(() => uploadDir(filePath)));\n        } else {\n          // Create promise to add to promises array\n          // this way it can be resolved once a pending upload has finished\n          let resolve: (val?: any) => void;\n          let reject: (err: AFCError) => void;\n          const promise = new Promise<void>((res, rej) => {\n            resolve = res;\n            reject = rej;\n          });\n          promises.push(promise);\n\n          // wrap upload in a function in case we need to save it for later\n          const uploadFile = (tries = 0) => {\n            numOpenFiles++;\n            _this\n              .uploadFile(filePath, remotePath)\n              .then(() => {\n                resolve();\n                numOpenFiles--;\n                const fn = pendingFileUploads.pop();\n                if (fn) {\n                  fn();\n                }\n              })\n              .catch((err: AFCError) => {\n                // Couldn't get fd for whatever reason, try again\n                // # of retries is arbitrary and can be adjusted\n                if (err.status === AFC_STATUS.NO_RESOURCES && tries < 10) {\n                  debug(`Received NO_RESOURCES from AFC, retrying ${filePath} upload. ${tries}`);\n                  uploadFile(tries++);\n                } else {\n                  numOpenFiles--;\n                  reject(err);\n                }\n              });\n          };\n\n          if (numOpenFiles < MAX_OPEN_FILES) {\n            uploadFile();\n          } else {\n            debug(\n              `numOpenFiles >= ${MAX_OPEN_FILES}, adding to pending queue. Length: ${pendingFileUploads.length}`\n            );\n            pendingFileUploads.push(uploadFile);\n          }\n        }\n      }\n      await Promise.all(promises);\n    }\n  }\n}\n\nfunction toCString(s: string) {\n  const buf = Buffer.alloc(s.length + 1);\n  const len = buf.write(s);\n  buf.writeUInt8(0, len);\n  return buf;\n}\n"], "names": ["AFCClient", "debug", "Debug", "MAX_OPEN_FILES", "ServiceClient", "constructor", "socket", "AFCProtocolClient", "getFileInfo", "path", "response", "protocolClient", "sendMessage", "operation", "AFC_OPS", "GET_FILE_INFO", "data", "toCString", "strings", "currentString", "tokens", "for<PERSON>ach", "token", "push", "String", "fromCharCode", "writeFile", "fd", "Array", "prototype", "toString", "call", "length", "FILE_WRITE", "payload", "openFile", "<PERSON><PERSON><PERSON>", "alloc", "writeUInt32LE", "AFC_FILE_OPEN_FLAGS", "WRONLY", "copy", "FILE_OPEN", "FILE_OPEN_RES", "CommandError", "closeFile", "FILE_CLOSE", "uploadFile", "srcPath", "destPath", "srcFile", "destFile", "Promise", "all", "promisify", "fs", "readFile", "err", "makeDirectory", "MAKE_DIR", "uploadDirectory", "numOpenFiles", "pendingFileUploads", "_this", "uploadDir", "<PERSON><PERSON><PERSON>", "promises", "file", "readdirSync", "filePath", "join", "remotePath", "relative", "lstatSync", "isDirectory", "then", "resolve", "reject", "promise", "res", "rej", "tries", "fn", "pop", "catch", "status", "AFC_STATUS", "NO_RESOURCES", "s", "buf", "len", "write", "writeUInt8"], "mappings": "AAAA;;;;;;CAMC;;;;+BAqBYA;;;eAAAA;;;;gEApBK;;;;;;;iEACE;;;;;;;iEAEE;;;;;;;yBACI;;;;;;+BAEI;wBACD;6BAQtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AACpB,MAAMC,iBAAiB;AAEhB,MAAMH,kBAAkBI,4BAAa;IAC1CC,YAAY,AAAOC,MAAc,CAAE;QACjC,KAAK,CAACA,QAAQ,IAAIC,8BAAiB,CAACD,eADnBA,SAAAA;IAEnB;IAEA,MAAME,YAAYC,IAAY,EAAqB;QACjDR,MAAM,CAAC,aAAa,EAAEQ,MAAM;QAE5B,MAAMC,WAAW,MAAM,IAAI,CAACC,cAAc,CAACC,WAAW,CAAC;YACrDC,WAAWC,oBAAO,CAACC,aAAa;YAChCC,MAAMC,UAAUR;QAClB;QACAR,MAAM,CAAC,wBAAwB,CAAC,EAAES;QAElC,MAAMQ,UAAoB,EAAE;QAC5B,IAAIC,gBAAgB;QACpB,MAAMC,SAASV,SAASM,IAAI;QAC5BI,OAAOC,OAAO,CAAC,CAACC;YACd,IAAIA,UAAU,GAAG;gBACfJ,QAAQK,IAAI,CAACJ;gBACbA,gBAAgB;YAClB,OAAO;gBACLA,iBAAiBK,OAAOC,YAAY,CAACH;YACvC;QACF;QACA,OAAOJ;IACT;IAEA,MAAMQ,UAAUC,EAAU,EAAEX,IAAY,EAAwB;QAC9Df,MAAM,CAAC,WAAW,EAAE2B,MAAMC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,IAAI,YAAY,EAAEX,KAAKgB,MAAM,EAAE;QAEjF,MAAMtB,WAAW,MAAM,IAAI,CAACC,cAAc,CAACC,WAAW,CAAC;YACrDC,WAAWC,oBAAO,CAACmB,UAAU;YAC7BjB,MAAMW;YACNO,SAASlB;QACX;QAEAf,MAAM,CAAC,mBAAmB,CAAC,EAAES;QAC7B,OAAOA;IACT;IAEA,MAAgByB,SAAS1B,IAAY,EAAmB;QACtDR,MAAM,CAAC,UAAU,EAAEQ,MAAM;QACzB,gCAAgC;QAChC,MAAMO,OAAOoB,OAAOC,KAAK,CAAC,IAAI5B,KAAKuB,MAAM,GAAG;QAC5C,aAAa;QACbhB,KAAKsB,aAAa,CAACC,gCAAmB,CAACC,MAAM,EAAE;QAC/C,oBAAoB;QACpBvB,UAAUR,MAAMgC,IAAI,CAACzB,MAAM;QAE3B,MAAMN,WAAW,MAAM,IAAI,CAACC,cAAc,CAACC,WAAW,CAAC;YACrDC,WAAWC,oBAAO,CAAC4B,SAAS;YAC5B1B;QACF;QAEA,yCAAyC;QAEzC,IAAIN,SAASG,SAAS,KAAKC,oBAAO,CAAC6B,aAAa,EAAE;YAChD,OAAOjC,SAASM,IAAI;QACtB;QAEA,MAAM,IAAI4B,oBAAY,CACpB,oBACA,CAAC,wCAAwC,EAAEnC,KAAK,YAAY,EAAEmB,MAAMC,SAAS,CAACC,QAAQ,CAACC,IAAI,CACzFrB,SAASM,IAAI,GACZ;IAEP;IAEA,MAAgB6B,UAAUlB,EAAU,EAAwB;QAC1D1B,MAAM,CAAC,cAAc,EAAE2B,MAAMC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,KAAK;QAC1D,MAAMjB,WAAW,MAAM,IAAI,CAACC,cAAc,CAACC,WAAW,CAAC;YACrDC,WAAWC,oBAAO,CAACgC,UAAU;YAC7B9B,MAAMW;QACR;QAEA1B,MAAM,CAAC,mBAAmB,CAAC,EAAES;QAC7B,OAAOA;IACT;IAEA,MAAgBqC,WAAWC,OAAe,EAAEC,QAAgB,EAAiB;QAC3EhD,MAAM,CAAC,YAAY,EAAE+C,QAAQ,EAAE,EAAEC,UAAU;QAE3C,4CAA4C;QAC5C,MAAM,CAACC,SAASC,SAAS,GAAG,MAAMC,QAAQC,GAAG,CAAC;YAC5C,MAAMC,IAAAA,iBAAS,EAACC,MAAGC,QAAQ,EAAER;YAC7B,MAAM,IAAI,CAACb,QAAQ,CAACc;SACrB;QAED,IAAI;YACF,MAAM,IAAI,CAACvB,SAAS,CAACyB,UAAUD;YAC/B,MAAM,IAAI,CAACL,SAAS,CAACM;QACvB,EAAE,OAAOM,KAAK;YACZ,MAAM,IAAI,CAACZ,SAAS,CAACM;YACrB,MAAMM;QACR;IACF;IAEA,MAAMC,cAAcjD,IAAY,EAAwB;QACtDR,MAAM,CAAC,eAAe,EAAEQ,MAAM;QAE9B,MAAMC,WAAW,MAAM,IAAI,CAACC,cAAc,CAACC,WAAW,CAAC;YACrDC,WAAWC,oBAAO,CAAC6C,QAAQ;YAC3B3C,MAAMC,UAAUR;QAClB;QAEAR,MAAM,CAAC,uBAAuB,CAAC,EAAES;QACjC,OAAOA;IACT;IAEA,MAAMkD,gBAAgBZ,OAAe,EAAEC,QAAgB,EAAiB;QACtEhD,MAAM,CAAC,iBAAiB,EAAE+C,SAAS;QACnC,MAAM,IAAI,CAACU,aAAa,CAACT;QAEzB,2DAA2D;QAC3D,sEAAsE;QACtE,IAAIY,eAAe;QACnB,MAAMC,qBAAqC,EAAE;QAC7C,MAAMC,QAAQ,IAAI;QAClB,OAAOC,UAAUhB;QAEjB,eAAegB,UAAUC,OAAe;YACtC,MAAMC,WAA4B,EAAE;YACpC,KAAK,MAAMC,QAAQZ,MAAGa,WAAW,CAACH,SAAU;gBAC1C,MAAMI,WAAW5D,QAAK6D,IAAI,CAACL,SAASE;gBACpC,MAAMI,aAAa9D,QAAK6D,IAAI,CAACrB,UAAUxC,QAAK+D,QAAQ,CAACxB,SAASqB;gBAC9D,IAAId,MAAGkB,SAAS,CAACJ,UAAUK,WAAW,IAAI;oBACxCR,SAAS3C,IAAI,CAACwC,MAAML,aAAa,CAACa,YAAYI,IAAI,CAAC,IAAMX,UAAUK;gBACrE,OAAO;oBACL,0CAA0C;oBAC1C,iEAAiE;oBACjE,IAAIO;oBACJ,IAAIC;oBACJ,MAAMC,UAAU,IAAI1B,QAAc,CAAC2B,KAAKC;wBACtCJ,UAAUG;wBACVF,SAASG;oBACX;oBACAd,SAAS3C,IAAI,CAACuD;oBAEd,iEAAiE;oBACjE,MAAM/B,aAAa,CAACkC,QAAQ,CAAC;wBAC3BpB;wBACAE,MACGhB,UAAU,CAACsB,UAAUE,YACrBI,IAAI,CAAC;4BACJC;4BACAf;4BACA,MAAMqB,KAAKpB,mBAAmBqB,GAAG;4BACjC,IAAID,IAAI;gCACNA;4BACF;wBACF,GACCE,KAAK,CAAC,CAAC3B;4BACN,iDAAiD;4BACjD,gDAAgD;4BAChD,IAAIA,IAAI4B,MAAM,KAAKC,uBAAU,CAACC,YAAY,IAAIN,QAAQ,IAAI;gCACxDhF,MAAM,CAAC,yCAAyC,EAAEoE,SAAS,SAAS,EAAEY,OAAO;gCAC7ElC,WAAWkC;4BACb,OAAO;gCACLpB;gCACAgB,OAAOpB;4BACT;wBACF;oBACJ;oBAEA,IAAII,eAAe1D,gBAAgB;wBACjC4C;oBACF,OAAO;wBACL9C,MACE,CAAC,gBAAgB,EAAEE,eAAe,mCAAmC,EAAE2D,mBAAmB9B,MAAM,EAAE;wBAEpG8B,mBAAmBvC,IAAI,CAACwB;oBAC1B;gBACF;YACF;YACA,MAAMK,QAAQC,GAAG,CAACa;QACpB;IACF;AACF;AAEA,SAASjD,UAAUuE,CAAS;IAC1B,MAAMC,MAAMrD,OAAOC,KAAK,CAACmD,EAAExD,MAAM,GAAG;IACpC,MAAM0D,MAAMD,IAAIE,KAAK,CAACH;IACtBC,IAAIG,UAAU,CAAC,GAAGF;IAClB,OAAOD;AACT"}