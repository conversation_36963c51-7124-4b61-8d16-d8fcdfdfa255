{"version": 3, "sources": ["../../../../../src/run/ios/appleDevice/AppleDevice.ts"], "sourcesContent": ["import Debug from 'debug';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { ClientManager } from './ClientManager';\nimport { IPLookupResult, OnInstallProgressCallback } from './client/InstallationProxyClient';\nimport { LockdowndClient } from './client/LockdowndClient';\nimport { UsbmuxdClient } from './client/UsbmuxdClient';\nimport { AFC_STATUS, AFCError } from './protocol/AFCProtocol';\nimport { Log } from '../../../log';\nimport { XcodeDeveloperDiskImagePrerequisite } from '../../../start/doctor/apple/XcodeDeveloperDiskImagePrerequisite';\nimport * as devicectl from '../../../start/platforms/ios/devicectl';\nimport { launchAppWithDeviceCtl } from '../../../start/platforms/ios/devicectl';\nimport { OSType } from '../../../start/platforms/ios/simctl';\nimport { uniqBy } from '../../../utils/array';\nimport { delayAsync } from '../../../utils/delay';\nimport { CommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\nimport { profile } from '../../../utils/profile';\n\nconst debug = Debug('expo:apple-device');\n\n// NOTE(EvanBacon): I have a feeling this shape will change with new iOS versions (tested against iOS 15).\nexport interface ConnectedDevice {\n  /** @example `00008101-001964A22629003A` */\n  udid: string;\n  /** @example `Evan's phone` */\n  name: string;\n  /** @example `iPhone13,4` */\n  model: string;\n  /** @example `device` */\n  deviceType: 'device' | 'catalyst';\n  /** @example `USB` */\n  connectionType: 'USB' | 'Network';\n  /** @example `15.4.1` */\n  osVersion: string;\n\n  osType: OSType;\n}\n\nasync function getConnectedDevicesUsingNativeToolsAsync(): Promise<ConnectedDevice[]> {\n  return (\n    (await devicectl.getConnectedAppleDevicesAsync())\n      // Filter out unpaired and unavailable devices.\n      // TODO: We could improve this logic in the future to attempt pairing if specified.\n      .filter(\n        (device) =>\n          device.connectionProperties.pairingState === 'paired' &&\n          device.connectionProperties.tunnelState !== 'unavailable'\n      )\n      .map((device) => {\n        return {\n          name: device.deviceProperties.name,\n          model: device.hardwareProperties.productType,\n          osVersion: device.deviceProperties.osVersionNumber,\n          udid: device.hardwareProperties.udid,\n          deviceType: 'device',\n          connectionType:\n            device.connectionProperties.transportType === 'localNetwork' ? 'Network' : 'USB',\n          osType: coercePlatformToOsType(device.hardwareProperties.platform),\n        };\n      })\n  );\n}\n\nfunction coercePlatformToOsType(platform: string): OSType {\n  // The only two devices I have to test against...\n  switch (platform) {\n    case 'iOS':\n      return 'iOS';\n    case 'xrOS':\n      return 'xrOS';\n    default:\n      debug('Unknown devicectl platform (needs to be added to Expo CLI):', platform);\n      return platform as OSType;\n  }\n}\nfunction coerceUsbmuxdPlatformToOsType(platform: string): OSType {\n  // The only connectable device I have to test against...\n  switch (platform) {\n    case 'iPhone':\n    case 'iPad':\n    case 'iPhone OS':\n      return 'iOS';\n    default:\n      debug('Unknown usbmuxd platform (needs to be added to Expo CLI):', platform);\n      return platform as OSType;\n  }\n}\n\n/** @returns a list of connected Apple devices. */\nexport async function getConnectedDevicesAsync(): Promise<ConnectedDevice[]> {\n  const devices = await Promise.all([\n    // Prioritize native tools since they can provide more accurate information.\n    // NOTE: xcrun is substantially slower than custom tooling. +1.5s vs 9ms.\n    profile(getConnectedDevicesUsingNativeToolsAsync)(),\n    profile(getConnectedDevicesUsingCustomToolingAsync)(),\n  ]);\n\n  return uniqBy(devices.flat(), (device) => device.udid);\n}\n\n/**\n * This supports devices that are running OS versions older than iOS 17.\n *\n * @returns a list of connected Apple devices.\n */\nasync function getConnectedDevicesUsingCustomToolingAsync(): Promise<ConnectedDevice[]> {\n  const client = new UsbmuxdClient(UsbmuxdClient.connectUsbmuxdSocket());\n  const devices = await client.getDevices();\n  client.socket.end();\n\n  return Promise.all(\n    devices.map(async (device): Promise<ConnectedDevice> => {\n      const socket = await new UsbmuxdClient(UsbmuxdClient.connectUsbmuxdSocket()).connect(\n        device,\n        62078\n      );\n      const deviceValues = await new LockdowndClient(socket).getAllValues();\n      socket.end();\n      // TODO(EvanBacon): Add support for osType (ipad, watchos, etc)\n      return {\n        // TODO(EvanBacon): Better name\n        name: deviceValues.DeviceName ?? deviceValues.ProductType ?? 'unknown iOS device',\n        model: deviceValues.ProductType,\n        osVersion: deviceValues.ProductVersion,\n        deviceType: 'device',\n        connectionType: device.Properties.ConnectionType,\n        udid: device.Properties.SerialNumber,\n        osType: coerceUsbmuxdPlatformToOsType(deviceValues.DeviceClass),\n      };\n    })\n  );\n}\n\n/** Install and run an Apple app binary on a connected Apple device. */\nexport async function runOnDevice({\n  udid,\n  appPath,\n  bundleId,\n  waitForApp,\n  deltaPath,\n  onProgress,\n}: {\n  /** Apple device UDID */\n  udid: string;\n  /** File path to the app binary (ipa) */\n  appPath: string;\n  /** Bundle identifier for the app at `appPath` */\n  bundleId: string;\n  /** Wait for the app to launch before returning */\n  waitForApp: boolean;\n  /** File path to the app deltas folder to use for faster subsequent installs */\n  deltaPath: string;\n  /** Callback to be called with progress updates */\n  onProgress: OnInstallProgressCallback;\n}) {\n  debug('Running on device:', { udid, appPath, bundleId, waitForApp, deltaPath });\n\n  const clientManager = await ClientManager.create(udid);\n\n  try {\n    await mountDeveloperDiskImage(clientManager);\n\n    const packageName = path.basename(appPath);\n    const destPackagePath = path.join('PublicStaging', packageName);\n\n    await uploadApp(clientManager, { appBinaryPath: appPath, destinationPath: destPackagePath });\n\n    const installer = await clientManager.getInstallationProxyClient();\n    await installer.installApp(\n      destPackagePath,\n      bundleId,\n      {\n        // https://github.com/ios-control/ios-deploy/blob/0f2ffb1e564aa67a2dfca7cdf13de47ce489d835/src/ios-deploy/ios-deploy.m#L2491-L2508\n        ApplicationsType: 'Any',\n\n        CFBundleIdentifier: bundleId,\n        CloseOnInvalidate: '1',\n        InvalidateOnDetach: '1',\n        IsUserInitiated: '1',\n        // Disable checking for wifi devices, this is nominally faster.\n        PreferWifi: '0',\n        // Only info I could find on these:\n        // https://github.com/wwxxyx/Quectel_BG96/blob/310876f90fc1093a59e45e381160eddcc31697d0/Apple_Homekit/homekit_certification_tools/ATS%206/ATS%206/ATS.app/Contents/Frameworks/CaptureKit.framework/Versions/A/Resources/MobileDevice/MobileInstallation.h#L112-L121\n        PackageType: 'Developer',\n        ShadowParentKey: deltaPath,\n        // SkipUninstall: '1'\n      },\n      onProgress\n    );\n\n    const {\n      // TODO(EvanBacon): This can be undefined when querying App Clips.\n      [bundleId]: appInfo,\n    } = await installer.lookupApp([bundleId]);\n\n    if (appInfo) {\n      // launch fails with EBusy or ENotFound if you try to launch immediately after install\n      await delayAsync(200);\n      const debugServerClient = await launchApp(clientManager, {\n        bundleId,\n        appInfo,\n        detach: !waitForApp,\n      });\n\n      if (waitForApp && debugServerClient) {\n        installExitHooks(async () => {\n          // causes continue() to return\n          debugServerClient.halt();\n          // give continue() time to return response\n          await delayAsync(64);\n        });\n\n        debug(`Waiting for app to close...\\n`);\n        const result = await debugServerClient.continue();\n        // TODO: I have no idea what this packet means yet (successful close?)\n        // if not a close (ie, most likely due to halt from onBeforeExit), then kill the app\n        if (result !== 'W00') {\n          await debugServerClient.kill();\n        }\n      }\n    } else {\n      Log.warn(`App \"${bundleId}\" installed but couldn't be launched. Open on device manually.`);\n    }\n  } finally {\n    clientManager.end();\n  }\n}\n\n/** Mount the developer disk image for Xcode. */\nasync function mountDeveloperDiskImage(clientManager: ClientManager) {\n  const imageMounter = await clientManager.getMobileImageMounterClient();\n  // Check if already mounted. If not, mount.\n  if (!(await imageMounter.lookupImage()).ImageSignature) {\n    // verify DeveloperDiskImage exists (TODO: how does this work on Windows/Linux?)\n    // TODO: if windows/linux, download?\n    const version = await (await clientManager.getLockdowndClient()).getValue('ProductVersion');\n    const developerDiskImagePath = await XcodeDeveloperDiskImagePrerequisite.instance.assertAsync({\n      version,\n    });\n    const developerDiskImageSig = fs.readFileSync(`${developerDiskImagePath}.signature`);\n    await imageMounter.uploadImage(developerDiskImagePath, developerDiskImageSig);\n    await imageMounter.mountImage(developerDiskImagePath, developerDiskImageSig);\n  }\n}\n\nasync function uploadApp(\n  clientManager: ClientManager,\n  { appBinaryPath, destinationPath }: { appBinaryPath: string; destinationPath: string }\n) {\n  const afcClient = await clientManager.getAFCClient();\n  try {\n    await afcClient.getFileInfo('PublicStaging');\n  } catch (err: any) {\n    if (err instanceof AFCError && err.status === AFC_STATUS.OBJECT_NOT_FOUND) {\n      await afcClient.makeDirectory('PublicStaging');\n    } else {\n      throw err;\n    }\n  }\n  await afcClient.uploadDirectory(appBinaryPath, destinationPath);\n}\n\nasync function launchAppWithUsbmux(\n  clientManager: ClientManager,\n  { appInfo, detach }: { appInfo: IPLookupResult[string]; detach?: boolean }\n) {\n  let tries = 0;\n  while (tries < 3) {\n    const debugServerClient = await clientManager.getDebugserverClient();\n    await debugServerClient.setMaxPacketSize(1024);\n    await debugServerClient.setWorkingDir(appInfo.Container);\n    await debugServerClient.launchApp(appInfo.Path, appInfo.CFBundleExecutable);\n\n    const result = await debugServerClient.checkLaunchSuccess();\n    if (result === 'OK') {\n      if (detach) {\n        // https://github.com/libimobiledevice/libimobiledevice/blob/25059d4c7d75e03aab516af2929d7c6e6d4c17de/tools/idevicedebug.c#L455-L464\n        const res = await debugServerClient.sendCommand('D', []);\n        debug('Disconnect from debug server request:', res);\n        if (res !== 'OK') {\n          console.warn(\n            'Something went wrong while attempting to disconnect from iOS debug server, you may need to reopen the app manually.'\n          );\n        }\n      }\n\n      return debugServerClient;\n    } else if (result === 'EBusy' || result === 'ENotFound') {\n      debug('Device busy or app not found, trying to launch again in .5s...');\n      tries++;\n      debugServerClient.socket.end();\n      await delayAsync(500);\n    } else {\n      throw new CommandError(`There was an error launching app: ${result}`);\n    }\n  }\n  throw new CommandError('Unable to launch app, number of tries exceeded');\n}\n\n/**\n * iOS 17 introduces a new protocol called RemoteXPC.\n * This is not yet implemented, so we fallback to devicectl.\n *\n * @see https://github.com/doronz88/pymobiledevice3/blob/master/misc/RemoteXPC.md#process-remoted\n */\nasync function launchApp(\n  clientManager: ClientManager,\n  {\n    bundleId,\n    appInfo,\n    detach,\n  }: { bundleId: string; appInfo: IPLookupResult[string]; detach?: boolean }\n) {\n  try {\n    return await launchAppWithUsbmux(clientManager, { appInfo, detach });\n  } catch (error) {\n    debug('Failed to launch app with Usbmuxd, falling back to xcrun...', error);\n\n    // Get the device UDID and close the connection, to allow `xcrun devicectl` to connect\n    const deviceId = clientManager.device.Properties.SerialNumber;\n    clientManager.end();\n\n    // Fallback to devicectl for iOS 17 support\n    return await launchAppWithDeviceCtl(deviceId, bundleId);\n  }\n}\n"], "names": ["getConnectedDevicesAsync", "runOnDevice", "debug", "Debug", "getConnectedDevicesUsingNativeToolsAsync", "devicectl", "getConnectedAppleDevicesAsync", "filter", "device", "connectionProperties", "pairingState", "tunnelState", "map", "name", "deviceProperties", "model", "hardwareProperties", "productType", "osVersion", "osVersionNumber", "udid", "deviceType", "connectionType", "transportType", "osType", "coercePlatformToOsType", "platform", "coerceUsbmuxdPlatformToOsType", "devices", "Promise", "all", "profile", "getConnectedDevicesUsingCustomToolingAsync", "uniqBy", "flat", "client", "UsbmuxdClient", "connectUsbmuxdSocket", "getDevices", "socket", "end", "connect", "deviceValues", "LockdowndClient", "getAllValues", "DeviceName", "ProductType", "ProductVersion", "Properties", "ConnectionType", "SerialNumber", "DeviceClass", "appPath", "bundleId", "waitForApp", "deltaPath", "onProgress", "clientManager", "ClientManager", "create", "mountDeveloperDiskImage", "packageName", "path", "basename", "destPackagePath", "join", "uploadApp", "appBinaryPath", "destinationPath", "installer", "getInstallationProxyClient", "installApp", "ApplicationsType", "CFBundleIdentifier", "CloseOnInvalidate", "InvalidateOnDetach", "IsUserInitiated", "PreferWifi", "PackageType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appInfo", "lookupApp", "delayAsync", "debugServerClient", "launchApp", "detach", "installExitHooks", "halt", "result", "continue", "kill", "Log", "warn", "imageMounter", "getMobileImageMounterClient", "lookupImage", "ImageSignature", "version", "getLockdowndClient", "getValue", "developerDiskImagePath", "XcodeDeveloperDiskImagePrerequisite", "instance", "assertAsync", "developerDiskImageSig", "fs", "readFileSync", "uploadImage", "mountImage", "afcClient", "getAFCClient", "getFileInfo", "err", "AFCError", "status", "AFC_STATUS", "OBJECT_NOT_FOUND", "makeDirectory", "uploadDirectory", "launchAppWithUsbmux", "tries", "getDebugserverClient", "setMaxPacketSize", "setWorkingDir", "Container", "Path", "CFBundleExecutable", "checkLaunchSuccess", "res", "sendCommand", "console", "CommandError", "error", "deviceId", "launchAppWithDeviceCtl"], "mappings": ";;;;;;;;;;;IA2FsBA,wBAAwB;eAAxBA;;IA6CAC,WAAW;eAAXA;;;;gEAxIJ;;;;;;;gEACH;;;;;;;gEACE;;;;;;+BAEa;iCAEE;+BACF;6BACO;qBACjB;qDACgC;mEACzB;uBAGJ;uBACI;wBACE;sBACI;yBACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExB,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AAoBpB,eAAeC;IACb,OACE,AAAC,CAAA,MAAMC,WAAUC,6BAA6B,EAAC,CAC7C,+CAA+C;IAC/C,mFAAmF;KAClFC,MAAM,CACL,CAACC,SACCA,OAAOC,oBAAoB,CAACC,YAAY,KAAK,YAC7CF,OAAOC,oBAAoB,CAACE,WAAW,KAAK,eAE/CC,GAAG,CAAC,CAACJ;QACJ,OAAO;YACLK,MAAML,OAAOM,gBAAgB,CAACD,IAAI;YAClCE,OAAOP,OAAOQ,kBAAkB,CAACC,WAAW;YAC5CC,WAAWV,OAAOM,gBAAgB,CAACK,eAAe;YAClDC,MAAMZ,OAAOQ,kBAAkB,CAACI,IAAI;YACpCC,YAAY;YACZC,gBACEd,OAAOC,oBAAoB,CAACc,aAAa,KAAK,iBAAiB,YAAY;YAC7EC,QAAQC,uBAAuBjB,OAAOQ,kBAAkB,CAACU,QAAQ;QACnE;IACF;AAEN;AAEA,SAASD,uBAAuBC,QAAgB;IAC9C,iDAAiD;IACjD,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACExB,MAAM,+DAA+DwB;YACrE,OAAOA;IACX;AACF;AACA,SAASC,8BAA8BD,QAAgB;IACrD,wDAAwD;IACxD,OAAQA;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACExB,MAAM,6DAA6DwB;YACnE,OAAOA;IACX;AACF;AAGO,eAAe1B;IACpB,MAAM4B,UAAU,MAAMC,QAAQC,GAAG,CAAC;QAChC,4EAA4E;QAC5E,yEAAyE;QACzEC,IAAAA,gBAAO,EAAC3B;QACR2B,IAAAA,gBAAO,EAACC;KACT;IAED,OAAOC,IAAAA,aAAM,EAACL,QAAQM,IAAI,IAAI,CAAC1B,SAAWA,OAAOY,IAAI;AACvD;AAEA;;;;CAIC,GACD,eAAeY;IACb,MAAMG,SAAS,IAAIC,4BAAa,CAACA,4BAAa,CAACC,oBAAoB;IACnE,MAAMT,UAAU,MAAMO,OAAOG,UAAU;IACvCH,OAAOI,MAAM,CAACC,GAAG;IAEjB,OAAOX,QAAQC,GAAG,CAChBF,QAAQhB,GAAG,CAAC,OAAOJ;QACjB,MAAM+B,SAAS,MAAM,IAAIH,4BAAa,CAACA,4BAAa,CAACC,oBAAoB,IAAII,OAAO,CAClFjC,QACA;QAEF,MAAMkC,eAAe,MAAM,IAAIC,gCAAe,CAACJ,QAAQK,YAAY;QACnEL,OAAOC,GAAG;QACV,+DAA+D;QAC/D,OAAO;YACL,+BAA+B;YAC/B3B,MAAM6B,aAAaG,UAAU,IAAIH,aAAaI,WAAW,IAAI;YAC7D/B,OAAO2B,aAAaI,WAAW;YAC/B5B,WAAWwB,aAAaK,cAAc;YACtC1B,YAAY;YACZC,gBAAgBd,OAAOwC,UAAU,CAACC,cAAc;YAChD7B,MAAMZ,OAAOwC,UAAU,CAACE,YAAY;YACpC1B,QAAQG,8BAA8Be,aAAaS,WAAW;QAChE;IACF;AAEJ;AAGO,eAAelD,YAAY,EAChCmB,IAAI,EACJgC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,UAAU,EAcX;IACCtD,MAAM,sBAAsB;QAAEkB;QAAMgC;QAASC;QAAUC;QAAYC;IAAU;IAE7E,MAAME,gBAAgB,MAAMC,4BAAa,CAACC,MAAM,CAACvC;IAEjD,IAAI;QACF,MAAMwC,wBAAwBH;QAE9B,MAAMI,cAAcC,eAAI,CAACC,QAAQ,CAACX;QAClC,MAAMY,kBAAkBF,eAAI,CAACG,IAAI,CAAC,iBAAiBJ;QAEnD,MAAMK,UAAUT,eAAe;YAAEU,eAAef;YAASgB,iBAAiBJ;QAAgB;QAE1F,MAAMK,YAAY,MAAMZ,cAAca,0BAA0B;QAChE,MAAMD,UAAUE,UAAU,CACxBP,iBACAX,UACA;YACE,kIAAkI;YAClImB,kBAAkB;YAElBC,oBAAoBpB;YACpBqB,mBAAmB;YACnBC,oBAAoB;YACpBC,iBAAiB;YACjB,+DAA+D;YAC/DC,YAAY;YACZ,mCAAmC;YACnC,mQAAmQ;YACnQC,aAAa;YACbC,iBAAiBxB;QAEnB,GACAC;QAGF,MAAM,EACJ,kEAAkE;QAClE,CAACH,SAAS,EAAE2B,OAAO,EACpB,GAAG,MAAMX,UAAUY,SAAS,CAAC;YAAC5B;SAAS;QAExC,IAAI2B,SAAS;YACX,sFAAsF;YACtF,MAAME,IAAAA,iBAAU,EAAC;YACjB,MAAMC,oBAAoB,MAAMC,UAAU3B,eAAe;gBACvDJ;gBACA2B;gBACAK,QAAQ,CAAC/B;YACX;YAEA,IAAIA,cAAc6B,mBAAmB;gBACnCG,IAAAA,sBAAgB,EAAC;oBACf,8BAA8B;oBAC9BH,kBAAkBI,IAAI;oBACtB,0CAA0C;oBAC1C,MAAML,IAAAA,iBAAU,EAAC;gBACnB;gBAEAhF,MAAM,CAAC,6BAA6B,CAAC;gBACrC,MAAMsF,SAAS,MAAML,kBAAkBM,QAAQ;gBAC/C,sEAAsE;gBACtE,oFAAoF;gBACpF,IAAID,WAAW,OAAO;oBACpB,MAAML,kBAAkBO,IAAI;gBAC9B;YACF;QACF,OAAO;YACLC,QAAG,CAACC,IAAI,CAAC,CAAC,KAAK,EAAEvC,SAAS,8DAA8D,CAAC;QAC3F;IACF,SAAU;QACRI,cAAcjB,GAAG;IACnB;AACF;AAEA,8CAA8C,GAC9C,eAAeoB,wBAAwBH,aAA4B;IACjE,MAAMoC,eAAe,MAAMpC,cAAcqC,2BAA2B;IACpE,2CAA2C;IAC3C,IAAI,CAAC,AAAC,CAAA,MAAMD,aAAaE,WAAW,EAAC,EAAGC,cAAc,EAAE;QACtD,gFAAgF;QAChF,oCAAoC;QACpC,MAAMC,UAAU,MAAM,AAAC,CAAA,MAAMxC,cAAcyC,kBAAkB,EAAC,EAAGC,QAAQ,CAAC;QAC1E,MAAMC,yBAAyB,MAAMC,wEAAmC,CAACC,QAAQ,CAACC,WAAW,CAAC;YAC5FN;QACF;QACA,MAAMO,wBAAwBC,aAAE,CAACC,YAAY,CAAC,GAAGN,uBAAuB,UAAU,CAAC;QACnF,MAAMP,aAAac,WAAW,CAACP,wBAAwBI;QACvD,MAAMX,aAAae,UAAU,CAACR,wBAAwBI;IACxD;AACF;AAEA,eAAetC,UACbT,aAA4B,EAC5B,EAAEU,aAAa,EAAEC,eAAe,EAAsD;IAEtF,MAAMyC,YAAY,MAAMpD,cAAcqD,YAAY;IAClD,IAAI;QACF,MAAMD,UAAUE,WAAW,CAAC;IAC9B,EAAE,OAAOC,KAAU;QACjB,IAAIA,eAAeC,qBAAQ,IAAID,IAAIE,MAAM,KAAKC,uBAAU,CAACC,gBAAgB,EAAE;YACzE,MAAMP,UAAUQ,aAAa,CAAC;QAChC,OAAO;YACL,MAAML;QACR;IACF;IACA,MAAMH,UAAUS,eAAe,CAACnD,eAAeC;AACjD;AAEA,eAAemD,oBACb9D,aAA4B,EAC5B,EAAEuB,OAAO,EAAEK,MAAM,EAAyD;IAE1E,IAAImC,QAAQ;IACZ,MAAOA,QAAQ,EAAG;QAChB,MAAMrC,oBAAoB,MAAM1B,cAAcgE,oBAAoB;QAClE,MAAMtC,kBAAkBuC,gBAAgB,CAAC;QACzC,MAAMvC,kBAAkBwC,aAAa,CAAC3C,QAAQ4C,SAAS;QACvD,MAAMzC,kBAAkBC,SAAS,CAACJ,QAAQ6C,IAAI,EAAE7C,QAAQ8C,kBAAkB;QAE1E,MAAMtC,SAAS,MAAML,kBAAkB4C,kBAAkB;QACzD,IAAIvC,WAAW,MAAM;YACnB,IAAIH,QAAQ;gBACV,oIAAoI;gBACpI,MAAM2C,MAAM,MAAM7C,kBAAkB8C,WAAW,CAAC,KAAK,EAAE;gBACvD/H,MAAM,yCAAyC8H;gBAC/C,IAAIA,QAAQ,MAAM;oBAChBE,QAAQtC,IAAI,CACV;gBAEJ;YACF;YAEA,OAAOT;QACT,OAAO,IAAIK,WAAW,WAAWA,WAAW,aAAa;YACvDtF,MAAM;YACNsH;YACArC,kBAAkB5C,MAAM,CAACC,GAAG;YAC5B,MAAM0C,IAAAA,iBAAU,EAAC;QACnB,OAAO;YACL,MAAM,IAAIiD,oBAAY,CAAC,CAAC,kCAAkC,EAAE3C,QAAQ;QACtE;IACF;IACA,MAAM,IAAI2C,oBAAY,CAAC;AACzB;AAEA;;;;;CAKC,GACD,eAAe/C,UACb3B,aAA4B,EAC5B,EACEJ,QAAQ,EACR2B,OAAO,EACPK,MAAM,EACkE;IAE1E,IAAI;QACF,OAAO,MAAMkC,oBAAoB9D,eAAe;YAAEuB;YAASK;QAAO;IACpE,EAAE,OAAO+C,OAAO;QACdlI,MAAM,+DAA+DkI;QAErE,sFAAsF;QACtF,MAAMC,WAAW5E,cAAcjD,MAAM,CAACwC,UAAU,CAACE,YAAY;QAC7DO,cAAcjB,GAAG;QAEjB,2CAA2C;QAC3C,OAAO,MAAM8F,IAAAA,iCAAsB,EAACD,UAAUhF;IAChD;AACF"}