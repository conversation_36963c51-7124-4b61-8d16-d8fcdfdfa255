{"version": 3, "sources": ["../../../../../src/run/ios/options/appleDestinations.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\n\nimport { Log } from '../../../log';\nimport { OSType } from '../../../start/platforms/ios/simctl';\nimport * as SimControl from '../../../start/platforms/ios/simctl';\nimport { BuildProps } from '../XcodeBuild.types';\nimport * as AppleDevice from '../appleDevice/AppleDevice';\n\nconst debug = require('debug')('expo:apple-destination') as typeof console.log;\n\ninterface Destination {\n  // 'visionOS'\n  platform: string;\n  // 'arm64'\n  arch?: string;\n  // 'Designed for [iPad,iPhone]'\n  variant?: string;\n  // '00008112-001A20EC1E78A01E'\n  id: string;\n  // 'Apple Vision Pro'\n  name: string;\n  // Available in simulators\n  OS?: string;\n}\n\nfunction coerceDestinationPlatformToOsType(platform: string): OSType {\n  // The only two devices I have to test against...\n  switch (platform) {\n    case 'iOS':\n      return 'iOS';\n    case 'xrOS':\n    case 'visionOS':\n      return 'xrOS';\n    case 'macOS':\n      return 'macOS';\n    default:\n      debug('Unknown destination platform (needs to be added to Expo CLI):', platform);\n      return platform as OSType;\n  }\n}\n\n// Runs `.filter(Boolean)` on the array with correct types.\nfunction filterBoolean<T>(array: (T | null | undefined)[]): T[] {\n  return array.filter(Boolean) as T[];\n}\n\nfunction warnDestinationObject(obj: any): Destination | null {\n  if (!obj || typeof obj !== 'object') {\n    return null;\n  }\n\n  if ('platform' in obj && 'id' in obj && 'name' in obj) {\n    return obj;\n  }\n  Log.warn('Unexpected xcode destination object:', obj);\n  return null;\n}\n\nfunction parseXcodeDestinationString(str: string): Destination[] {\n  const parsedLines = filterBoolean(\n    str\n      .trim()\n      .split('\\n')\n      .map((line: string) => {\n        line = line.trim();\n        return line.startsWith('{') ? line : null;\n      })\n  ).map((line) => {\n    const inner = line.match(/{(.*)}/)?.[1];\n\n    if (!inner) return null;\n\n    return Object.fromEntries(\n      filterBoolean(\n        inner\n          .trim()\n          .split(', ')\n          .map((item) => item.trim().match(/(?<key>[^:]+):(?<value>.+)/)?.groups)\n      ).map((item) => [item!.key, item!.value])\n    );\n  });\n\n  return filterBoolean(parsedLines.map(warnDestinationObject));\n}\n\nfunction coercePhysicalDevice(\n  device: Destination\n): Pick<AppleDevice.ConnectedDevice, 'udid' | 'name' | 'osType' | 'deviceType' | 'osVersion'> {\n  // physical device\n  return {\n    /** @example `00008101-001964A22629003A` */\n    udid: device.id,\n    /** @example `Evan's phone` */\n    name: device.name,\n    /** @example `iPhone13,4` */\n    // model: 'UNKNOWN',\n    /** @example `device` */\n    deviceType: 'device',\n    osType: coerceDestinationPlatformToOsType(device.platform),\n\n    osVersion: '',\n  };\n}\n\nfunction coerceSimulatorDevice(\n  device: Destination\n): Pick<\n  SimControl.Device,\n  | 'udid'\n  | 'name'\n  | 'osType'\n  | 'osVersion'\n  | 'runtime'\n  | 'isAvailable'\n  | 'deviceTypeIdentifier'\n  | 'state'\n  | 'windowName'\n> {\n  // simulator\n  return {\n    /** '00E55DC0-0364-49DF-9EC6-77BE587137D4' */\n    udid: device.id,\n    /** 'com.apple.CoreSimulator.SimRuntime.iOS-15-1' */\n    runtime: '',\n    /** If the device is \"available\" which generally means that the OS files haven't been deleted (this can happen when Xcode updates).  */\n    isAvailable: true,\n\n    deviceTypeIdentifier: '',\n\n    state: 'Shutdown',\n    /** 'iPhone 13 Pro' */\n    name: device.name,\n    /** Type of OS the device uses. */\n    osType: device.platform === 'visionOS Simulator' ? 'xrOS' : 'iOS',\n    /** '15.1' */\n    osVersion: device.OS!,\n    /** 'iPhone 13 Pro (15.1)' */\n    windowName: `${device.name} (${device.OS})`,\n  };\n}\n\nfunction coerceDestinationObjectToKnownDeviceType(device: Destination) {\n  if (device.arch) {\n    // physical device\n    return coercePhysicalDevice(device);\n  } else if (device.OS) {\n    // simulator\n    return coerceSimulatorDevice(device);\n  } else {\n    // \"Any device\"\n    return null;\n  }\n}\n\nexport async function resolveDestinationsAsync(\n  props: Pick<BuildProps, 'configuration' | 'scheme' | 'xcodeProject'>\n): Promise<{ name: string; osType: OSType; osVersion: string; udid: string }[]> {\n  // xcodebuild -workspace /Users/<USER>/Documents/GitHub/lab/apr23/ios/apr23.xcworkspace -configuration Debug -scheme apr23 -showdestinations -json\n\n  const { stdout } = await spawnAsync('xcodebuild', [\n    props.xcodeProject.isWorkspace ? '-workspace' : '-project',\n    props.xcodeProject.name,\n    '-configuration',\n    props.configuration,\n    '-scheme',\n    props.scheme,\n    '-showdestinations',\n    '-quiet',\n  ]);\n\n  //   console.log(JSON.stringify(stdout, null, 2));\n\n  const destinationObjects = parseXcodeDestinationString(stdout);\n\n  return filterBoolean(destinationObjects.map(coerceDestinationObjectToKnownDeviceType));\n}\n"], "names": ["resolveDestinationsAsync", "debug", "require", "coerceDestinationPlatformToOsType", "platform", "filterBoolean", "array", "filter", "Boolean", "warnDestinationObject", "obj", "Log", "warn", "parseXcodeDestinationString", "str", "parsedLines", "trim", "split", "map", "line", "startsWith", "inner", "match", "Object", "fromEntries", "item", "groups", "key", "value", "coercePhysicalDevice", "device", "udid", "id", "name", "deviceType", "osType", "osVersion", "coerceSimulatorDevice", "runtime", "isAvailable", "deviceTypeIdentifier", "state", "OS", "windowName", "coerceDestinationObjectToKnownDeviceType", "arch", "props", "stdout", "spawnAsync", "xcodeProject", "isWorkspace", "configuration", "scheme", "destinationObjects"], "mappings": ";;;;+BA0JsBA;;;eAAAA;;;;gEA1JC;;;;;;qBAEH;;;;;;AAMpB,MAAMC,QAAQC,QAAQ,SAAS;AAiB/B,SAASC,kCAAkCC,QAAgB;IACzD,iDAAiD;IACjD,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACEH,MAAM,iEAAiEG;YACvE,OAAOA;IACX;AACF;AAEA,2DAA2D;AAC3D,SAASC,cAAiBC,KAA+B;IACvD,OAAOA,MAAMC,MAAM,CAACC;AACtB;AAEA,SAASC,sBAAsBC,GAAQ;IACrC,IAAI,CAACA,OAAO,OAAOA,QAAQ,UAAU;QACnC,OAAO;IACT;IAEA,IAAI,cAAcA,OAAO,QAAQA,OAAO,UAAUA,KAAK;QACrD,OAAOA;IACT;IACAC,QAAG,CAACC,IAAI,CAAC,wCAAwCF;IACjD,OAAO;AACT;AAEA,SAASG,4BAA4BC,GAAW;IAC9C,MAAMC,cAAcV,cAClBS,IACGE,IAAI,GACJC,KAAK,CAAC,MACNC,GAAG,CAAC,CAACC;QACJA,OAAOA,KAAKH,IAAI;QAChB,OAAOG,KAAKC,UAAU,CAAC,OAAOD,OAAO;IACvC,IACFD,GAAG,CAAC,CAACC;YACSA;QAAd,MAAME,SAAQF,cAAAA,KAAKG,KAAK,CAAC,8BAAXH,WAAsB,CAAC,EAAE;QAEvC,IAAI,CAACE,OAAO,OAAO;QAEnB,OAAOE,OAAOC,WAAW,CACvBnB,cACEgB,MACGL,IAAI,GACJC,KAAK,CAAC,MACNC,GAAG,CAAC,CAACO;gBAASA;oBAAAA,mBAAAA,KAAKT,IAAI,GAAGM,KAAK,CAAC,kDAAlBG,iBAAiDC,MAAM;YACxER,GAAG,CAAC,CAACO,OAAS;gBAACA,KAAME,GAAG;gBAAEF,KAAMG,KAAK;aAAC;IAE5C;IAEA,OAAOvB,cAAcU,YAAYG,GAAG,CAACT;AACvC;AAEA,SAASoB,qBACPC,MAAmB;IAEnB,kBAAkB;IAClB,OAAO;QACL,yCAAyC,GACzCC,MAAMD,OAAOE,EAAE;QACf,4BAA4B,GAC5BC,MAAMH,OAAOG,IAAI;QACjB,0BAA0B,GAC1B,oBAAoB;QACpB,sBAAsB,GACtBC,YAAY;QACZC,QAAQhC,kCAAkC2B,OAAO1B,QAAQ;QAEzDgC,WAAW;IACb;AACF;AAEA,SAASC,sBACPP,MAAmB;IAanB,YAAY;IACZ,OAAO;QACL,2CAA2C,GAC3CC,MAAMD,OAAOE,EAAE;QACf,kDAAkD,GAClDM,SAAS;QACT,qIAAqI,GACrIC,aAAa;QAEbC,sBAAsB;QAEtBC,OAAO;QACP,oBAAoB,GACpBR,MAAMH,OAAOG,IAAI;QACjB,gCAAgC,GAChCE,QAAQL,OAAO1B,QAAQ,KAAK,uBAAuB,SAAS;QAC5D,WAAW,GACXgC,WAAWN,OAAOY,EAAE;QACpB,2BAA2B,GAC3BC,YAAY,GAAGb,OAAOG,IAAI,CAAC,EAAE,EAAEH,OAAOY,EAAE,CAAC,CAAC,CAAC;IAC7C;AACF;AAEA,SAASE,yCAAyCd,MAAmB;IACnE,IAAIA,OAAOe,IAAI,EAAE;QACf,kBAAkB;QAClB,OAAOhB,qBAAqBC;IAC9B,OAAO,IAAIA,OAAOY,EAAE,EAAE;QACpB,YAAY;QACZ,OAAOL,sBAAsBP;IAC/B,OAAO;QACL,eAAe;QACf,OAAO;IACT;AACF;AAEO,eAAe9B,yBACpB8C,KAAoE;IAEpE,qJAAqJ;IAErJ,MAAM,EAAEC,MAAM,EAAE,GAAG,MAAMC,IAAAA,qBAAU,EAAC,cAAc;QAChDF,MAAMG,YAAY,CAACC,WAAW,GAAG,eAAe;QAChDJ,MAAMG,YAAY,CAAChB,IAAI;QACvB;QACAa,MAAMK,aAAa;QACnB;QACAL,MAAMM,MAAM;QACZ;QACA;KACD;IAED,kDAAkD;IAElD,MAAMC,qBAAqBxC,4BAA4BkC;IAEvD,OAAO1C,cAAcgD,mBAAmBnC,GAAG,CAAC0B;AAC9C"}