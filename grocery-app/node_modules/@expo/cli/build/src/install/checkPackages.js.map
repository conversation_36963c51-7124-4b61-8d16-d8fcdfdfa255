{"version": 3, "sources": ["../../../src/install/checkPackages.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport * as PackageManager from '@expo/package-manager';\nimport chalk from 'chalk';\n\nimport { fixPackagesAsync } from './fixPackages';\nimport { Options } from './resolveOptions';\nimport * as Log from '../log';\nimport {\n  getVersionedDependenciesAsync,\n  logIncorrectDependencies,\n} from '../start/doctor/dependencies/validateDependenciesVersions';\nimport { isInteractive } from '../utils/interactive';\nimport { learnMore } from '../utils/link';\nimport { confirmAsync } from '../utils/prompts';\nimport { joinWithCommasAnd } from '../utils/strings';\n\nconst debug = require('debug')('expo:install:check') as typeof console.log;\n\n/**\n * Handles `expo install --fix|check'.\n * Checks installed dependencies against bundledNativeModules and versions endpoints to find any incompatibilities.\n * If `--fix` is passed, it will install the correct versions of the dependencies.\n * If `--check` is passed, it will prompt the user to install the correct versions of the dependencies (on interactive terminal).\n */\nexport async function checkPackagesAsync(\n  projectRoot: string,\n  {\n    packages,\n    packageManager,\n    options: { fix },\n    packageManagerArguments,\n  }: {\n    /**\n     * List of packages to version\n     * @example ['uuid', 'react-native-reanimated@latest']\n     */\n    packages: string[];\n    /** Package manager to use when installing the versioned packages. */\n    packageManager: PackageManager.NodePackageManager;\n\n    /** How the check should resolve */\n    options: Pick<Options, 'fix'>;\n    /**\n     * Extra parameters to pass to the `packageManager` when installing versioned packages.\n     * @example ['--no-save']\n     */\n    packageManagerArguments: string[];\n  }\n) {\n  // Read the project Expo config without plugins.\n  const { exp, pkg } = getConfig(projectRoot, {\n    // Sometimes users will add a plugin to the config before installing the library,\n    // this wouldn't work unless we dangerously disable plugin serialization.\n    skipPlugins: true,\n  });\n\n  if (pkg.expo?.install?.exclude?.length) {\n    Log.log(\n      chalk`Skipped ${fix ? 'fixing' : 'checking'} dependencies: ${joinWithCommasAnd(\n        pkg.expo.install.exclude\n      )}. These dependencies are listed in {bold expo.install.exclude} in package.json. ${learnMore(\n        'https://docs.expo.dev/more/expo-cli/#configuring-dependency-validation'\n      )}`\n    );\n  }\n\n  const dependencies = await getVersionedDependenciesAsync(projectRoot, exp, pkg, packages);\n\n  /*\n   * Expo Router projects will do this additional check\n   * Note: The e2e tests use nexpo which will always resolve 'expo-router/doctor.js'\n   *       For that reason, you cannot use nexpo to test for the sub-dependency check,\n   *       and you cannot replace this guard with a try/catch around the import('expo-router')\n   */\n  if (pkg.dependencies?.['expo-router']) {\n    try {\n      const { doctor: routerDoctor } = await import('expo-router/doctor.js');\n      dependencies.push(\n        ...routerDoctor(pkg, require.resolve('@react-navigation/native'), {\n          bold: chalk.bold,\n          learnMore,\n        })\n      );\n    } catch (error) {\n      Log.log(`Skipped checking expo-router dependencies: expo-router/doctor.js not found.`);\n      debug('expo-router/doctor error:', error);\n    }\n  }\n\n  if (!dependencies.length) {\n    Log.exit(chalk.greenBright('Dependencies are up to date'), 0);\n  }\n\n  logIncorrectDependencies(dependencies);\n\n  const value =\n    // If `--fix` then always fix.\n    fix ||\n    // Otherwise prompt to fix when not running in CI.\n    (isInteractive() && (await confirmAsync({ message: 'Fix dependencies?' }).catch(() => false)));\n\n  if (value) {\n    debug('Installing fixed dependencies:', dependencies);\n    // Install the corrected dependencies.\n    return fixPackagesAsync(projectRoot, {\n      packageManager,\n      packages: dependencies,\n      packageManagerArguments,\n      sdkVersion: exp.sdkVersion!,\n    });\n  }\n\n  // Exit with non-zero exit code if any of the dependencies are out of date.\n  Log.exit(chalk.red('Found outdated dependencies'), 1);\n}\n"], "names": ["checkPackagesAsync", "debug", "require", "projectRoot", "packages", "packageManager", "options", "fix", "packageManagerArguments", "pkg", "exp", "getConfig", "skip<PERSON>lug<PERSON>", "expo", "install", "exclude", "length", "Log", "log", "chalk", "joinWithCommasAnd", "learnMore", "dependencies", "getVersionedDependenciesAsync", "doctor", "routerDoctor", "push", "resolve", "bold", "error", "exit", "<PERSON><PERSON><PERSON>", "logIncorrectDependencies", "value", "isInteractive", "<PERSON><PERSON><PERSON>", "message", "catch", "fixPackagesAsync", "sdkVersion", "red"], "mappings": ";;;;+BAwBsBA;;;eAAAA;;;;yBAxBI;;;;;;;gEAER;;;;;;6BAEe;6DAEZ;8CAId;6BACuB;sBACJ;yBACG;yBACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,MAAMC,QAAQC,QAAQ,SAAS;AAQxB,eAAeF,mBACpBG,WAAmB,EACnB,EACEC,QAAQ,EACRC,cAAc,EACdC,SAAS,EAAEC,GAAG,EAAE,EAChBC,uBAAuB,EAiBxB;QASGC,2BAAAA,mBAAAA,WAkBAA;IAzBJ,gDAAgD;IAChD,MAAM,EAAEC,GAAG,EAAED,GAAG,EAAE,GAAGE,IAAAA,mBAAS,EAACR,aAAa;QAC1C,iFAAiF;QACjF,yEAAyE;QACzES,aAAa;IACf;IAEA,KAAIH,YAAAA,IAAII,IAAI,sBAARJ,oBAAAA,UAAUK,OAAO,sBAAjBL,4BAAAA,kBAAmBM,OAAO,qBAA1BN,0BAA4BO,MAAM,EAAE;QACtCC,KAAIC,GAAG,CACLC,IAAAA,gBAAK,CAAA,CAAC,QAAQ,EAAEZ,MAAM,WAAW,WAAW,eAAe,EAAEa,IAAAA,0BAAiB,EAC5EX,IAAII,IAAI,CAACC,OAAO,CAACC,OAAO,EACxB,gFAAgF,EAAEM,IAAAA,eAAS,EAC3F,0EACA,CAAC;IAEP;IAEA,MAAMC,eAAe,MAAMC,IAAAA,2DAA6B,EAACpB,aAAaO,KAAKD,KAAKL;IAEhF;;;;;GAKC,GACD,KAAIK,oBAAAA,IAAIa,YAAY,qBAAhBb,iBAAkB,CAAC,cAAc,EAAE;QACrC,IAAI;YACF,MAAM,EAAEe,QAAQC,YAAY,EAAE,GAAG,MAAM,mEAAA,QAAO;YAC9CH,aAAaI,IAAI,IACZD,aAAahB,KAAKP,QAAQyB,OAAO,CAAC,6BAA6B;gBAChEC,MAAMT,gBAAK,CAACS,IAAI;gBAChBP,WAAAA,eAAS;YACX;QAEJ,EAAE,OAAOQ,OAAO;YACdZ,KAAIC,GAAG,CAAC,CAAC,2EAA2E,CAAC;YACrFjB,MAAM,6BAA6B4B;QACrC;IACF;IAEA,IAAI,CAACP,aAAaN,MAAM,EAAE;QACxBC,KAAIa,IAAI,CAACX,gBAAK,CAACY,WAAW,CAAC,gCAAgC;IAC7D;IAEAC,IAAAA,sDAAwB,EAACV;IAEzB,MAAMW,QACJ,8BAA8B;IAC9B1B,OACA,kDAAkD;IACjD2B,IAAAA,0BAAa,OAAO,MAAMC,IAAAA,qBAAY,EAAC;QAAEC,SAAS;IAAoB,GAAGC,KAAK,CAAC,IAAM;IAExF,IAAIJ,OAAO;QACThC,MAAM,kCAAkCqB;QACxC,sCAAsC;QACtC,OAAOgB,IAAAA,6BAAgB,EAACnC,aAAa;YACnCE;YACAD,UAAUkB;YACVd;YACA+B,YAAY7B,IAAI6B,UAAU;QAC5B;IACF;IAEA,2EAA2E;IAC3EtB,KAAIa,IAAI,CAACX,gBAAK,CAACqB,GAAG,CAAC,gCAAgC;AACrD"}