{"version": 3, "sources": ["../../../src/start/detectDevClient.ts"], "sourcesContent": ["import { getPackage<PERSON>son } from '@expo/config';\nimport resolveFrom from 'resolve-from';\n\nexport function hasDirectDevClientDependency(projectRoot: string): boolean {\n  const { dependencies = {}, devDependencies = {} } = getPackageJson(projectRoot);\n  return !!dependencies['expo-dev-client'] || !!devDependencies['expo-dev-client'];\n}\n\nexport function canResolveDevClient(projectRoot: string): boolean {\n  try {\n    // we check if `expo-dev-launcher` is installed instead of `expo-dev-client`\n    // because someone could install only launcher.\n    resolveFrom(projectRoot, 'expo-dev-launcher');\n    return true;\n  } catch {\n    return false;\n  }\n}\n"], "names": ["canResolveDevClient", "hasDirectDevClientDependency", "projectRoot", "dependencies", "devDependencies", "getPackageJson", "resolveFrom"], "mappings": ";;;;;;;;;;;IAQgBA,mBAAmB;eAAnBA;;IALAC,4BAA4B;eAA5BA;;;;yBAHe;;;;;;;gEACP;;;;;;;;;;;AAEjB,SAASA,6BAA6BC,WAAmB;IAC9D,MAAM,EAAEC,eAAe,CAAC,CAAC,EAAEC,kBAAkB,CAAC,CAAC,EAAE,GAAGC,IAAAA,wBAAc,EAACH;IACnE,OAAO,CAAC,CAACC,YAAY,CAAC,kBAAkB,IAAI,CAAC,CAACC,eAAe,CAAC,kBAAkB;AAClF;AAEO,SAASJ,oBAAoBE,WAAmB;IACrD,IAAI;QACF,4EAA4E;QAC5E,+CAA+C;QAC/CI,IAAAA,sBAAW,EAACJ,aAAa;QACzB,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF"}