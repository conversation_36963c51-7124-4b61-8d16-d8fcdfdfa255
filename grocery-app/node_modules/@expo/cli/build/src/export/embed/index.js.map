{"version": 3, "sources": ["../../../../src/export/embed/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport arg from 'arg';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport { Command } from '../../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../../utils/args';\n\nexport const expoExportEmbed: Command = async (argv) => {\n  const rawArgsMap: arg.Spec = {\n    // Types\n    '--entry-file': String,\n    '--platform': String,\n    '--transformer': String,\n    '--bundle-output': String,\n    '--bundle-encoding': String,\n    '--max-workers': Number,\n    '--sourcemap-output': String,\n    '--sourcemap-sources-root': String,\n    '--assets-dest': String,\n    '--asset-catalog-dest': String,\n    '--unstable-transform-profile': String,\n    '--config': String,\n\n    // Hack: This is added because react-native-xcode.sh script always includes this value.\n    // If supplied, we'll do nothing with the value, but at least the process won't crash.\n    // Note that we also don't show this value in the `--help` prompt since we don't want people to use it.\n    '--config-cmd': String,\n\n    // New flag to guess the other flags based on the environment.\n    '--eager': Boolean,\n    // Export the bundle as Hermes bytecode bundle\n    '--bytecode': Boolean,\n\n    // This is here for compatibility with the `npx react-native bundle` command.\n    // devs should use `DEBUG=expo:*` instead.\n    '--verbose': Boolean,\n    '--help': Boolean,\n    // Aliases\n    '-h': '--help',\n    '-v': '--verbose',\n  };\n  const args = assertWithOptionsArgs(rawArgsMap, {\n    argv,\n    permissive: true,\n  });\n\n  if (args['--help']) {\n    printHelp(\n      `(Internal) Export the JavaScript bundle during a native build script for embedding in a native binary`,\n      chalk`npx expo export:embed {dim <dir>}`,\n      [\n        chalk`<dir>                                  Directory of the Expo project. {dim Default: Current working directory}`,\n        `--entry-file <path>                    Path to the root JS file, either absolute or relative to JS root`,\n        `--platform <string>                    Either \"ios\" or \"android\" (default: \"ios\")`,\n        `--transformer <string>                 Specify a custom transformer to be used`,\n        `--dev [boolean]                        If false, warnings are disabled and the bundle is minified (default: true)`,\n        `--minify [boolean]                     Allows overriding whether bundle is minified. This defaults to false if dev is true, and true if dev is false. Disabling minification can be useful for speeding up production builds for testing purposes.`,\n        `--bundle-output <string>               File name where to store the resulting bundle, ex. /tmp/groups.bundle`,\n        `--bundle-encoding <string>             Encoding the bundle should be written in (https://nodejs.org/api/buffer.html#buffer_buffer). (default: \"utf8\")`,\n        `--max-workers <number>                 Specifies the maximum number of workers the worker-pool will spawn for transforming files. This defaults to the number of the cores available on your machine.`,\n        `--sourcemap-output <string>            File name where to store the sourcemap file for resulting bundle, ex. /tmp/groups.map`,\n        `--sourcemap-sources-root <string>      Path to make sourcemap's sources entries relative to, ex. /root/dir`,\n        `--sourcemap-use-absolute-path          Report SourceMapURL using its full path`,\n        `--assets-dest <string>                 Directory name where to store assets referenced in the bundle`,\n        `--asset-catalog-dest <string>          Directory to create an iOS Asset Catalog for images`,\n        `--unstable-transform-profile <string>  Experimental, transform JS for a specific JS engine. Currently supported: hermes, hermes-canary, default`,\n        `--reset-cache                          Removes cached files`,\n        `--eager                                Eagerly export the bundle with default options`,\n        `--bytecode                             Export the bundle as Hermes bytecode bundle`,\n        `-v, --verbose                          Enables debug logging`,\n\n        `--config <string>                      Path to the CLI configuration file`,\n        // This is seemingly unused.\n        `--read-global-cache                    Try to fetch transformed JS code from the global cache, if configured.`,\n\n        `-h, --help                             Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  const [\n    { exportEmbedAsync },\n    { resolveOptions },\n    { logCmdError },\n    { resolveCustomBooleanArgsAsync },\n  ] = await Promise.all([\n    import('./exportEmbedAsync.js'),\n    import('./resolveOptions.js'),\n    import('../../utils/errors.js'),\n    import('../../utils/resolveArgs.js'),\n  ]);\n\n  return (async () => {\n    const parsed = await resolveCustomBooleanArgsAsync(argv ?? [], rawArgsMap, {\n      '--eager': Boolean,\n      '--bytecode': Boolean,\n      '--dev': Boolean,\n      '--minify': Boolean,\n      '--sourcemap-use-absolute-path': Boolean,\n      '--reset-cache': Boolean,\n      '--read-global-cache': Boolean,\n    });\n\n    const projectRoot = path.resolve(parsed.projectRoot);\n    return exportEmbedAsync(projectRoot, resolveOptions(projectRoot, args, parsed));\n  })().catch(logCmdError);\n};\n"], "names": ["expoExportEmbed", "argv", "rawArgsMap", "String", "Number", "Boolean", "args", "assertWithOptionsArgs", "permissive", "printHelp", "chalk", "join", "exportEmbedAsync", "resolveOptions", "logCmdError", "resolveCustomBooleanArgsAsync", "Promise", "all", "parsed", "projectRoot", "path", "resolve", "catch"], "mappings": ";;;;;+BAQaA;;;eAAAA;;;;gEANK;;;;;;;gEACD;;;;;;sBAGgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1C,MAAMA,kBAA2B,OAAOC;IAC7C,MAAMC,aAAuB;QAC3B,QAAQ;QACR,gBAAgBC;QAChB,cAAcA;QACd,iBAAiBA;QACjB,mBAAmBA;QACnB,qBAAqBA;QACrB,iBAAiBC;QACjB,sBAAsBD;QACtB,4BAA4BA;QAC5B,iBAAiBA;QACjB,wBAAwBA;QACxB,gCAAgCA;QAChC,YAAYA;QAEZ,uFAAuF;QACvF,sFAAsF;QACtF,uGAAuG;QACvG,gBAAgBA;QAEhB,8DAA8D;QAC9D,WAAWE;QACX,8CAA8C;QAC9C,cAAcA;QAEd,6EAA6E;QAC7E,0CAA0C;QAC1C,aAAaA;QACb,UAAUA;QACV,UAAU;QACV,MAAM;QACN,MAAM;IACR;IACA,MAAMC,OAAOC,IAAAA,2BAAqB,EAACL,YAAY;QAC7CD;QACAO,YAAY;IACd;IAEA,IAAIF,IAAI,CAAC,SAAS,EAAE;QAClBG,IAAAA,eAAS,EACP,CAAC,qGAAqG,CAAC,EACvGC,IAAAA,gBAAK,CAAA,CAAC,iCAAiC,CAAC,EACxC;YACEA,IAAAA,gBAAK,CAAA,CAAC,8GAA8G,CAAC;YACrH,CAAC,uGAAuG,CAAC;YACzG,CAAC,iFAAiF,CAAC;YACnF,CAAC,8EAA8E,CAAC;YAChF,CAAC,iHAAiH,CAAC;YACnH,CAAC,kPAAkP,CAAC;YACpP,CAAC,4GAA4G,CAAC;YAC9G,CAAC,qJAAqJ,CAAC;YACvJ,CAAC,qMAAqM,CAAC;YACvM,CAAC,4HAA4H,CAAC;YAC9H,CAAC,0GAA0G,CAAC;YAC5G,CAAC,8EAA8E,CAAC;YAChF,CAAC,oGAAoG,CAAC;YACtG,CAAC,0FAA0F,CAAC;YAC5F,CAAC,+IAA+I,CAAC;YACjJ,CAAC,2DAA2D,CAAC;YAC7D,CAAC,qFAAqF,CAAC;YACvF,CAAC,kFAAkF,CAAC;YACpF,CAAC,4DAA4D,CAAC;YAE9D,CAAC,yEAAyE,CAAC;YAC3E,4BAA4B;YAC5B,CAAC,6GAA6G,CAAC;YAE/G,CAAC,iDAAiD,CAAC;SACpD,CAACC,IAAI,CAAC;IAEX;IAEA,MAAM,CACJ,EAAEC,gBAAgB,EAAE,EACpB,EAAEC,cAAc,EAAE,EAClB,EAAEC,WAAW,EAAE,EACf,EAAEC,6BAA6B,EAAE,CAClC,GAAG,MAAMC,QAAQC,GAAG,CAAC;QACpB,mEAAA,QAAO;QACP,mEAAA,QAAO;QACP,mEAAA,QAAO;QACP,mEAAA,QAAO;KACR;IAED,OAAO,AAAC,CAAA;QACN,MAAMC,SAAS,MAAMH,8BAA8Bd,QAAQ,EAAE,EAAEC,YAAY;YACzE,WAAWG;YACX,cAAcA;YACd,SAASA;YACT,YAAYA;YACZ,iCAAiCA;YACjC,iBAAiBA;YACjB,uBAAuBA;QACzB;QAEA,MAAMc,cAAcC,eAAI,CAACC,OAAO,CAACH,OAAOC,WAAW;QACnD,OAAOP,iBAAiBO,aAAaN,eAAeM,aAAab,MAAMY;IACzE,CAAA,IAAKI,KAAK,CAACR;AACb"}