{"version": 3, "sources": ["../../../src/export/exportStaticAsync.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { ExpoConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport { RouteNode } from 'expo-router/build/Route';\nimport { stripGroupSegmentsFromPath } from 'expo-router/build/matchers';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport { inspect } from 'util';\n\nimport { getVirtualFaviconAssetsAsync } from './favicon';\nimport { persistMetroAssetsAsync } from './persistMetroAssets';\nimport { ExportAssetMap, getFilesFromSerialAssets } from './saveAssets';\nimport { Log } from '../log';\nimport {\n  ExpoRouterRuntimeManifest,\n  MetroBundlerDevServer,\n} from '../start/server/metro/MetroBundlerDevServer';\nimport { ExpoRouterServerManifestV1 } from '../start/server/metro/fetchRouterManifest';\nimport { logMetroErrorAsync } from '../start/server/metro/metroErrorInterface';\nimport { getApiRoutesForDirectory } from '../start/server/metro/router';\nimport { serializeHtmlWithAssets } from '../start/server/metro/serializeHtml';\nimport { learnMore } from '../utils/link';\n\nconst debug = require('debug')('expo:export:generateStaticRoutes') as typeof console.log;\n\ntype Options = {\n  mode: 'production' | 'development';\n  files?: ExportAssetMap;\n  outputDir: string;\n  minify: boolean;\n  exportServer: boolean;\n  baseUrl: string;\n  includeSourceMaps: boolean;\n  entryPoint?: string;\n  clear: boolean;\n  routerRoot: string;\n  reactCompiler: boolean;\n  maxWorkers?: number;\n  isExporting: boolean;\n  exp?: ExpoConfig;\n};\n\ntype HtmlRequestLocation = {\n  /** The output file path name to use relative to the static folder. */\n  filePath: string;\n  /** The pathname to make requests to in order to fetch the HTML. */\n  pathname: string;\n  /** The runtime route node object, used to associate async modules with the static HTML. */\n  route: RouteNode;\n};\n\n/** Match `(page)` -> `page` */\nfunction matchGroupName(name: string): string | undefined {\n  return name.match(/^\\(([^/]+?)\\)$/)?.[1];\n}\n\nexport async function getFilesToExportFromServerAsync(\n  projectRoot: string,\n  {\n    manifest,\n    renderAsync,\n    // Servers can handle group routes automatically and therefore\n    // don't require the build-time generation of every possible group\n    // variation.\n    exportServer,\n    // name : contents\n    files = new Map(),\n  }: {\n    manifest: ExpoRouterRuntimeManifest;\n    renderAsync: (requestLocation: HtmlRequestLocation) => Promise<string>;\n    exportServer?: boolean;\n    files?: ExportAssetMap;\n  }\n): Promise<ExportAssetMap> {\n  await Promise.all(\n    getHtmlFiles({ manifest, includeGroupVariations: !exportServer }).map(\n      async ({ route, filePath, pathname }) => {\n        try {\n          const targetDomain = exportServer ? 'server' : 'client';\n          files.set(filePath, { contents: '', targetDomain });\n          const data = await renderAsync({ route, filePath, pathname });\n          files.set(filePath, {\n            contents: data,\n            routeId: pathname,\n            targetDomain,\n          });\n        } catch (e: any) {\n          await logMetroErrorAsync({ error: e, projectRoot });\n          throw new Error('Failed to statically export route: ' + pathname);\n        }\n      }\n    )\n  );\n\n  return files;\n}\n\nfunction modifyRouteNodeInRuntimeManifest(\n  manifest: ExpoRouterRuntimeManifest,\n  callback: (route: RouteNode) => any\n) {\n  const iterateScreens = (screens: ExpoRouterRuntimeManifest['screens']) => {\n    Object.values(screens).map((value) => {\n      if (typeof value !== 'string') {\n        if (value._route) callback(value._route);\n        iterateScreens(value.screens);\n      }\n    });\n  };\n\n  iterateScreens(manifest.screens);\n}\n\n// TODO: Do this earlier in the process.\nfunction makeRuntimeEntryPointsAbsolute(manifest: ExpoRouterRuntimeManifest, appDir: string) {\n  modifyRouteNodeInRuntimeManifest(manifest, (route) => {\n    if (Array.isArray(route.entryPoints)) {\n      route.entryPoints = route.entryPoints.map((entryPoint) => {\n        if (entryPoint.startsWith('.')) {\n          return path.resolve(appDir, entryPoint);\n        } else if (!path.isAbsolute(entryPoint)) {\n          return resolveFrom(appDir, entryPoint);\n        }\n        return entryPoint;\n      });\n    }\n  });\n}\n\n/** Perform all fs commits */\nexport async function exportFromServerAsync(\n  projectRoot: string,\n  devServer: MetroBundlerDevServer,\n  {\n    outputDir,\n    baseUrl,\n    exportServer,\n    includeSourceMaps,\n    routerRoot,\n    files = new Map(),\n    exp,\n  }: Options\n): Promise<ExportAssetMap> {\n  Log.log(\n    `Static rendering is enabled. ` +\n      learnMore('https://docs.expo.dev/router/reference/static-rendering/')\n  );\n\n  const platform = 'web';\n  const isExporting = true;\n  const appDir = path.join(projectRoot, routerRoot);\n  const injectFaviconTag = await getVirtualFaviconAssetsAsync(projectRoot, {\n    outputDir,\n    baseUrl,\n    files,\n    exp,\n  });\n\n  const [resources, { manifest, serverManifest, renderAsync }] = await Promise.all([\n    devServer.getStaticResourcesAsync({\n      includeSourceMaps,\n    }),\n    devServer.getStaticRenderFunctionAsync(),\n  ]);\n\n  makeRuntimeEntryPointsAbsolute(manifest, appDir);\n\n  debug('Routes:\\n', inspect(manifest, { colors: true, depth: null }));\n\n  await getFilesToExportFromServerAsync(projectRoot, {\n    files,\n    manifest,\n    exportServer,\n    async renderAsync({ pathname, route }) {\n      const template = await renderAsync(pathname);\n      let html = await serializeHtmlWithAssets({\n        isExporting,\n        resources: resources.artifacts,\n        template,\n        baseUrl,\n        route,\n        hydrate: true,\n      });\n\n      if (injectFaviconTag) {\n        html = injectFaviconTag(html);\n      }\n\n      return html;\n    },\n  });\n\n  getFilesFromSerialAssets(resources.artifacts, {\n    platform,\n    includeSourceMaps,\n    files,\n    isServerHosted: true,\n  });\n\n  if (resources.assets) {\n    // TODO: Collect files without writing to disk.\n    // NOTE(kitten): Re. above, this is now using `files` except for iOS catalog output, which isn't used here\n    await persistMetroAssetsAsync(projectRoot, resources.assets, {\n      files,\n      platform,\n      outputDirectory: outputDir,\n      baseUrl,\n    });\n  }\n\n  if (exportServer) {\n    const apiRoutes = await exportApiRoutesAsync({\n      platform: 'web',\n      server: devServer,\n      manifest: serverManifest,\n      // NOTE(kitten): For now, we always output source maps for API route exports\n      includeSourceMaps: true,\n    });\n\n    // Add the api routes to the files to export.\n    for (const [route, contents] of apiRoutes) {\n      files.set(route, contents);\n    }\n  } else {\n    warnPossibleInvalidExportType(appDir);\n  }\n\n  return files;\n}\n\nexport function getHtmlFiles({\n  manifest,\n  includeGroupVariations,\n}: {\n  manifest: ExpoRouterRuntimeManifest;\n  includeGroupVariations?: boolean;\n}): HtmlRequestLocation[] {\n  const htmlFiles = new Set<Omit<HtmlRequestLocation, 'pathname'>>();\n\n  function traverseScreens(\n    screens: ExpoRouterRuntimeManifest['screens'],\n    route: RouteNode | null,\n    baseUrl = ''\n  ) {\n    for (const [key, value] of Object.entries(screens)) {\n      let leaf: string | null = null;\n      if (typeof value === 'string') {\n        leaf = value;\n      } else if (Object.keys(value.screens).length === 0) {\n        // Ensure the trailing index is accounted for.\n        if (key === value.path + '/index') {\n          leaf = key;\n        } else {\n          leaf = value.path;\n        }\n\n        route = value._route ?? null;\n      }\n\n      if (leaf != null) {\n        let filePath = baseUrl + leaf;\n\n        if (leaf === '') {\n          filePath =\n            baseUrl === ''\n              ? 'index'\n              : baseUrl.endsWith('/')\n                ? baseUrl + 'index'\n                : baseUrl.slice(0, -1);\n        } else if (\n          // If the path is a collection of group segments leading to an index route, append `/index`.\n          stripGroupSegmentsFromPath(filePath) === ''\n        ) {\n          filePath += '/index';\n        }\n\n        // This should never happen, the type of `string | object` originally comes from React Navigation.\n        if (!route) {\n          throw new Error(\n            `Internal error: Route not found for \"${filePath}\" while collecting static export paths.`\n          );\n        }\n\n        if (includeGroupVariations) {\n          // TODO: Dedupe requests for alias routes.\n          addOptionalGroups(filePath, route);\n        } else {\n          htmlFiles.add({\n            filePath,\n            route,\n          });\n        }\n      } else if (typeof value === 'object' && value?.screens) {\n        // The __root slot has no path.\n        const newPath = value.path ? baseUrl + value.path + '/' : baseUrl;\n        traverseScreens(value.screens, value._route ?? null, newPath);\n      }\n    }\n  }\n\n  function addOptionalGroups(path: string, route: RouteNode) {\n    const variations = getPathVariations(path);\n    for (const variation of variations) {\n      htmlFiles.add({ filePath: variation, route });\n    }\n  }\n\n  traverseScreens(manifest.screens, null);\n\n  return uniqueBy(Array.from(htmlFiles), (value) => value.filePath).map((value) => {\n    const parts = value.filePath.split('/');\n    // Replace `:foo` with `[foo]` and `*foo` with `[...foo]`\n    const partsWithGroups = parts.map((part) => {\n      if (part === '*not-found') {\n        return `+not-found`;\n      } else if (part.startsWith(':')) {\n        return `[${part.slice(1)}]`;\n      } else if (part.startsWith('*')) {\n        return `[...${part.slice(1)}]`;\n      }\n      return part;\n    });\n    const filePathLocation = partsWithGroups.join('/');\n    const filePath = filePathLocation + '.html';\n    return {\n      ...value,\n      filePath,\n      pathname: filePathLocation.replace(/(\\/?index)?$/, ''),\n    };\n  });\n}\n\nfunction uniqueBy<T>(array: T[], key: (value: T) => string): T[] {\n  const seen = new Set<string>();\n  const result: T[] = [];\n  for (const value of array) {\n    const id = key(value);\n    if (!seen.has(id)) {\n      seen.add(id);\n      result.push(value);\n    }\n  }\n  return result;\n}\n\n// Given a route like `(foo)/bar/(baz)`, return all possible variations of the route.\n// e.g. `(foo)/bar/(baz)`, `(foo)/bar/baz`, `foo/bar/(baz)`, `foo/bar/baz`,\nexport function getPathVariations(routePath: string): string[] {\n  const variations = new Set<string>();\n  const segments = routePath.split('/');\n\n  function generateVariations(segments: string[], current = ''): void {\n    if (segments.length === 0) {\n      if (current) variations.add(current);\n      return;\n    }\n\n    const [head, ...rest] = segments;\n\n    if (matchGroupName(head)) {\n      const groups = head.slice(1, -1).split(',');\n\n      if (groups.length > 1) {\n        for (const group of groups) {\n          // If there are multiple groups, recurse on each group.\n          generateVariations([`(${group.trim()})`, ...rest], current);\n        }\n        return;\n      } else {\n        // Start a fork where this group is included\n        generateVariations(rest, current ? `${current}/(${groups[0]})` : `(${groups[0]})`);\n        // This code will continue and add paths without this group included`\n      }\n    } else if (current) {\n      current = `${current}/${head}`;\n    } else {\n      current = head;\n    }\n\n    generateVariations(rest, current);\n  }\n\n  generateVariations(segments);\n\n  return Array.from(variations);\n}\n\nexport async function exportApiRoutesStandaloneAsync(\n  devServer: MetroBundlerDevServer,\n  {\n    files = new Map(),\n    platform,\n    apiRoutesOnly,\n    templateHtml,\n  }: {\n    files?: ExportAssetMap;\n    platform: string;\n    apiRoutesOnly: boolean;\n    templateHtml?: string;\n  }\n) {\n  const { serverManifest, htmlManifest } = await devServer.getServerManifestAsync();\n\n  const apiRoutes = await exportApiRoutesAsync({\n    server: devServer,\n    manifest: serverManifest,\n    // NOTE(kitten): For now, we always output source maps for API route exports\n    includeSourceMaps: true,\n    platform,\n    apiRoutesOnly,\n  });\n\n  // Add the api routes to the files to export.\n  for (const [route, contents] of apiRoutes) {\n    files.set(route, contents);\n  }\n\n  if (templateHtml && devServer.isReactServerComponentsEnabled) {\n    // TODO: Export an HTML entry for each file. This is a temporary solution until we have SSR/SSG for RSC.\n    await getFilesToExportFromServerAsync(devServer.projectRoot, {\n      manifest: htmlManifest,\n      exportServer: true,\n      files,\n      renderAsync: async ({ pathname, filePath }) => {\n        files.set(filePath, {\n          contents: templateHtml!,\n          routeId: pathname,\n          targetDomain: 'server',\n        });\n        return templateHtml!;\n      },\n    });\n  }\n\n  return files;\n}\n\nasync function exportApiRoutesAsync({\n  includeSourceMaps,\n  server,\n  platform,\n  apiRoutesOnly,\n  ...props\n}: Pick<Options, 'includeSourceMaps'> & {\n  server: MetroBundlerDevServer;\n  manifest: ExpoRouterServerManifestV1;\n  platform: string;\n  apiRoutesOnly?: boolean;\n}): Promise<ExportAssetMap> {\n  const { manifest, files } = await server.exportExpoRouterApiRoutesAsync({\n    outputDir: '_expo/functions',\n    prerenderManifest: props.manifest,\n    includeSourceMaps,\n    platform,\n  });\n\n  // HACK: Clear out the HTML and 404 routes if we're only exporting API routes. This is used for native apps that are using API routes but haven't implemented web support yet.\n  if (apiRoutesOnly) {\n    manifest.htmlRoutes = [];\n    manifest.notFoundRoutes = [];\n  }\n\n  files.set('_expo/routes.json', {\n    contents: JSON.stringify(manifest, null, 2),\n    targetDomain: 'server',\n  });\n\n  return files;\n}\n\nfunction warnPossibleInvalidExportType(appDir: string) {\n  const apiRoutes = getApiRoutesForDirectory(appDir);\n  if (apiRoutes.length) {\n    // TODO: Allow API Routes for native-only.\n    Log.warn(\n      chalk.yellow`Skipping export for API routes because \\`web.output\\` is not \"server\". You may want to remove the routes: ${apiRoutes\n        .map((v) => path.relative(appDir, v))\n        .join(', ')}`\n    );\n  }\n}\n"], "names": ["exportApiRoutesStandaloneAsync", "exportFromServerAsync", "getFilesToExportFromServerAsync", "getHtmlFiles", "getPathVariations", "debug", "require", "matchGroupName", "name", "match", "projectRoot", "manifest", "renderAsync", "exportServer", "files", "Map", "Promise", "all", "includeGroupVariations", "map", "route", "filePath", "pathname", "targetDomain", "set", "contents", "data", "routeId", "e", "logMetroErrorAsync", "error", "Error", "modifyRouteNodeInRuntimeManifest", "callback", "iterateScreens", "screens", "Object", "values", "value", "_route", "makeRuntimeEntryPointsAbsolute", "appDir", "Array", "isArray", "entryPoints", "entryPoint", "startsWith", "path", "resolve", "isAbsolute", "resolveFrom", "devServer", "outputDir", "baseUrl", "includeSourceMaps", "routerRoot", "exp", "Log", "log", "learnMore", "platform", "isExporting", "join", "injectFaviconTag", "getVirtualFaviconAssetsAsync", "resources", "serverManifest", "getStaticResourcesAsync", "getStaticRenderFunctionAsync", "inspect", "colors", "depth", "template", "html", "serializeHtmlWithAssets", "artifacts", "hydrate", "getFilesFromSerialAssets", "isServerHosted", "assets", "persistMetroAssetsAsync", "outputDirectory", "apiRoutes", "exportApiRoutesAsync", "server", "warnPossibleInvalidExportType", "htmlFiles", "Set", "traverseScreens", "key", "entries", "leaf", "keys", "length", "endsWith", "slice", "stripGroupSegmentsFromPath", "addOptionalGroups", "add", "newPath", "variations", "variation", "uniqueBy", "from", "parts", "split", "partsWithGroups", "part", "filePathLocation", "replace", "array", "seen", "result", "id", "has", "push", "routePath", "segments", "generateVariations", "current", "head", "rest", "groups", "group", "trim", "apiRoutesOnly", "templateHtml", "htmlManifest", "getServerManifestAsync", "isReactServerComponentsEnabled", "props", "exportExpoRouterApiRoutesAsync", "prerenderManifest", "htmlRoutes", "notFoundRoutes", "JSON", "stringify", "getApiRoutesForDirectory", "warn", "chalk", "yellow", "v", "relative"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAmYqBA,8BAA8B;eAA9BA;;IAjQAC,qBAAqB;eAArBA;;IA1EAC,+BAA+B;eAA/BA;;IA8KNC,YAAY;eAAZA;;IAqHAC,iBAAiB;eAAjBA;;;;gEAzVE;;;;;;;yBAEyB;;;;;;;gEAC1B;;;;;;;gEACO;;;;;;;yBACA;;;;;;yBAEqB;oCACL;4BACiB;qBACrC;qCAMe;wBACM;+BACD;sBACd;;;;;;AAE1B,MAAMC,QAAQC,QAAQ,SAAS;AA4B/B,6BAA6B,GAC7B,SAASC,eAAeC,IAAY;QAC3BA;IAAP,QAAOA,cAAAA,KAAKC,KAAK,CAAC,sCAAXD,WAA8B,CAAC,EAAE;AAC1C;AAEO,eAAeN,gCACpBQ,WAAmB,EACnB,EACEC,QAAQ,EACRC,WAAW,EACX,8DAA8D;AAC9D,kEAAkE;AAClE,aAAa;AACbC,YAAY,EACZ,kBAAkB;AAClBC,QAAQ,IAAIC,KAAK,EAMlB;IAED,MAAMC,QAAQC,GAAG,CACfd,aAAa;QAAEQ;QAAUO,wBAAwB,CAACL;IAAa,GAAGM,GAAG,CACnE,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;QAClC,IAAI;YACF,MAAMC,eAAeV,eAAe,WAAW;YAC/CC,MAAMU,GAAG,CAACH,UAAU;gBAAEI,UAAU;gBAAIF;YAAa;YACjD,MAAMG,OAAO,MAAMd,YAAY;gBAAEQ;gBAAOC;gBAAUC;YAAS;YAC3DR,MAAMU,GAAG,CAACH,UAAU;gBAClBI,UAAUC;gBACVC,SAASL;gBACTC;YACF;QACF,EAAE,OAAOK,GAAQ;YACf,MAAMC,IAAAA,uCAAkB,EAAC;gBAAEC,OAAOF;gBAAGlB;YAAY;YACjD,MAAM,IAAIqB,MAAM,wCAAwCT;QAC1D;IACF;IAIJ,OAAOR;AACT;AAEA,SAASkB,iCACPrB,QAAmC,EACnCsB,QAAmC;IAEnC,MAAMC,iBAAiB,CAACC;QACtBC,OAAOC,MAAM,CAACF,SAAShB,GAAG,CAAC,CAACmB;YAC1B,IAAI,OAAOA,UAAU,UAAU;gBAC7B,IAAIA,MAAMC,MAAM,EAAEN,SAASK,MAAMC,MAAM;gBACvCL,eAAeI,MAAMH,OAAO;YAC9B;QACF;IACF;IAEAD,eAAevB,SAASwB,OAAO;AACjC;AAEA,wCAAwC;AACxC,SAASK,+BAA+B7B,QAAmC,EAAE8B,MAAc;IACzFT,iCAAiCrB,UAAU,CAACS;QAC1C,IAAIsB,MAAMC,OAAO,CAACvB,MAAMwB,WAAW,GAAG;YACpCxB,MAAMwB,WAAW,GAAGxB,MAAMwB,WAAW,CAACzB,GAAG,CAAC,CAAC0B;gBACzC,IAAIA,WAAWC,UAAU,CAAC,MAAM;oBAC9B,OAAOC,eAAI,CAACC,OAAO,CAACP,QAAQI;gBAC9B,OAAO,IAAI,CAACE,eAAI,CAACE,UAAU,CAACJ,aAAa;oBACvC,OAAOK,IAAAA,sBAAW,EAACT,QAAQI;gBAC7B;gBACA,OAAOA;YACT;QACF;IACF;AACF;AAGO,eAAe5C,sBACpBS,WAAmB,EACnByC,SAAgC,EAChC,EACEC,SAAS,EACTC,OAAO,EACPxC,YAAY,EACZyC,iBAAiB,EACjBC,UAAU,EACVzC,QAAQ,IAAIC,KAAK,EACjByC,GAAG,EACK;IAEVC,QAAG,CAACC,GAAG,CACL,CAAC,6BAA6B,CAAC,GAC7BC,IAAAA,eAAS,EAAC;IAGd,MAAMC,WAAW;IACjB,MAAMC,cAAc;IACpB,MAAMpB,SAASM,eAAI,CAACe,IAAI,CAACpD,aAAa6C;IACtC,MAAMQ,mBAAmB,MAAMC,IAAAA,qCAA4B,EAACtD,aAAa;QACvE0C;QACAC;QACAvC;QACA0C;IACF;IAEA,MAAM,CAACS,WAAW,EAAEtD,QAAQ,EAAEuD,cAAc,EAAEtD,WAAW,EAAE,CAAC,GAAG,MAAMI,QAAQC,GAAG,CAAC;QAC/EkC,UAAUgB,uBAAuB,CAAC;YAChCb;QACF;QACAH,UAAUiB,4BAA4B;KACvC;IAED5B,+BAA+B7B,UAAU8B;IAEzCpC,MAAM,aAAagE,IAAAA,eAAO,EAAC1D,UAAU;QAAE2D,QAAQ;QAAMC,OAAO;IAAK;IAEjE,MAAMrE,gCAAgCQ,aAAa;QACjDI;QACAH;QACAE;QACA,MAAMD,aAAY,EAAEU,QAAQ,EAAEF,KAAK,EAAE;YACnC,MAAMoD,WAAW,MAAM5D,YAAYU;YACnC,IAAImD,OAAO,MAAMC,IAAAA,sCAAuB,EAAC;gBACvCb;gBACAI,WAAWA,UAAUU,SAAS;gBAC9BH;gBACAnB;gBACAjC;gBACAwD,SAAS;YACX;YAEA,IAAIb,kBAAkB;gBACpBU,OAAOV,iBAAiBU;YAC1B;YAEA,OAAOA;QACT;IACF;IAEAI,IAAAA,oCAAwB,EAACZ,UAAUU,SAAS,EAAE;QAC5Cf;QACAN;QACAxC;QACAgE,gBAAgB;IAClB;IAEA,IAAIb,UAAUc,MAAM,EAAE;QACpB,+CAA+C;QAC/C,0GAA0G;QAC1G,MAAMC,IAAAA,2CAAuB,EAACtE,aAAauD,UAAUc,MAAM,EAAE;YAC3DjE;YACA8C;YACAqB,iBAAiB7B;YACjBC;QACF;IACF;IAEA,IAAIxC,cAAc;QAChB,MAAMqE,YAAY,MAAMC,qBAAqB;YAC3CvB,UAAU;YACVwB,QAAQjC;YACRxC,UAAUuD;YACV,4EAA4E;YAC5EZ,mBAAmB;QACrB;QAEA,6CAA6C;QAC7C,KAAK,MAAM,CAAClC,OAAOK,SAAS,IAAIyD,UAAW;YACzCpE,MAAMU,GAAG,CAACJ,OAAOK;QACnB;IACF,OAAO;QACL4D,8BAA8B5C;IAChC;IAEA,OAAO3B;AACT;AAEO,SAASX,aAAa,EAC3BQ,QAAQ,EACRO,sBAAsB,EAIvB;IACC,MAAMoE,YAAY,IAAIC;IAEtB,SAASC,gBACPrD,OAA6C,EAC7Cf,KAAuB,EACvBiC,UAAU,EAAE;QAEZ,KAAK,MAAM,CAACoC,KAAKnD,MAAM,IAAIF,OAAOsD,OAAO,CAACvD,SAAU;YAClD,IAAIwD,OAAsB;YAC1B,IAAI,OAAOrD,UAAU,UAAU;gBAC7BqD,OAAOrD;YACT,OAAO,IAAIF,OAAOwD,IAAI,CAACtD,MAAMH,OAAO,EAAE0D,MAAM,KAAK,GAAG;gBAClD,8CAA8C;gBAC9C,IAAIJ,QAAQnD,MAAMS,IAAI,GAAG,UAAU;oBACjC4C,OAAOF;gBACT,OAAO;oBACLE,OAAOrD,MAAMS,IAAI;gBACnB;gBAEA3B,QAAQkB,MAAMC,MAAM,IAAI;YAC1B;YAEA,IAAIoD,QAAQ,MAAM;gBAChB,IAAItE,WAAWgC,UAAUsC;gBAEzB,IAAIA,SAAS,IAAI;oBACftE,WACEgC,YAAY,KACR,UACAA,QAAQyC,QAAQ,CAAC,OACfzC,UAAU,UACVA,QAAQ0C,KAAK,CAAC,GAAG,CAAC;gBAC5B,OAAO,IACL,4FAA4F;gBAC5FC,IAAAA,sCAA0B,EAAC3E,cAAc,IACzC;oBACAA,YAAY;gBACd;gBAEA,kGAAkG;gBAClG,IAAI,CAACD,OAAO;oBACV,MAAM,IAAIW,MACR,CAAC,qCAAqC,EAAEV,SAAS,uCAAuC,CAAC;gBAE7F;gBAEA,IAAIH,wBAAwB;oBAC1B,0CAA0C;oBAC1C+E,kBAAkB5E,UAAUD;gBAC9B,OAAO;oBACLkE,UAAUY,GAAG,CAAC;wBACZ7E;wBACAD;oBACF;gBACF;YACF,OAAO,IAAI,OAAOkB,UAAU,aAAYA,yBAAAA,MAAOH,OAAO,GAAE;gBACtD,+BAA+B;gBAC/B,MAAMgE,UAAU7D,MAAMS,IAAI,GAAGM,UAAUf,MAAMS,IAAI,GAAG,MAAMM;gBAC1DmC,gBAAgBlD,MAAMH,OAAO,EAAEG,MAAMC,MAAM,IAAI,MAAM4D;YACvD;QACF;IACF;IAEA,SAASF,kBAAkBlD,IAAY,EAAE3B,KAAgB;QACvD,MAAMgF,aAAahG,kBAAkB2C;QACrC,KAAK,MAAMsD,aAAaD,WAAY;YAClCd,UAAUY,GAAG,CAAC;gBAAE7E,UAAUgF;gBAAWjF;YAAM;QAC7C;IACF;IAEAoE,gBAAgB7E,SAASwB,OAAO,EAAE;IAElC,OAAOmE,SAAS5D,MAAM6D,IAAI,CAACjB,YAAY,CAAChD,QAAUA,MAAMjB,QAAQ,EAAEF,GAAG,CAAC,CAACmB;QACrE,MAAMkE,QAAQlE,MAAMjB,QAAQ,CAACoF,KAAK,CAAC;QACnC,yDAAyD;QACzD,MAAMC,kBAAkBF,MAAMrF,GAAG,CAAC,CAACwF;YACjC,IAAIA,SAAS,cAAc;gBACzB,OAAO,CAAC,UAAU,CAAC;YACrB,OAAO,IAAIA,KAAK7D,UAAU,CAAC,MAAM;gBAC/B,OAAO,CAAC,CAAC,EAAE6D,KAAKZ,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7B,OAAO,IAAIY,KAAK7D,UAAU,CAAC,MAAM;gBAC/B,OAAO,CAAC,IAAI,EAAE6D,KAAKZ,KAAK,CAAC,GAAG,CAAC,CAAC;YAChC;YACA,OAAOY;QACT;QACA,MAAMC,mBAAmBF,gBAAgB5C,IAAI,CAAC;QAC9C,MAAMzC,WAAWuF,mBAAmB;QACpC,OAAO;YACL,GAAGtE,KAAK;YACRjB;YACAC,UAAUsF,iBAAiBC,OAAO,CAAC,gBAAgB;QACrD;IACF;AACF;AAEA,SAASP,SAAYQ,KAAU,EAAErB,GAAyB;IACxD,MAAMsB,OAAO,IAAIxB;IACjB,MAAMyB,SAAc,EAAE;IACtB,KAAK,MAAM1E,SAASwE,MAAO;QACzB,MAAMG,KAAKxB,IAAInD;QACf,IAAI,CAACyE,KAAKG,GAAG,CAACD,KAAK;YACjBF,KAAKb,GAAG,CAACe;YACTD,OAAOG,IAAI,CAAC7E;QACd;IACF;IACA,OAAO0E;AACT;AAIO,SAAS5G,kBAAkBgH,SAAiB;IACjD,MAAMhB,aAAa,IAAIb;IACvB,MAAM8B,WAAWD,UAAUX,KAAK,CAAC;IAEjC,SAASa,mBAAmBD,QAAkB,EAAEE,UAAU,EAAE;QAC1D,IAAIF,SAASxB,MAAM,KAAK,GAAG;YACzB,IAAI0B,SAASnB,WAAWF,GAAG,CAACqB;YAC5B;QACF;QAEA,MAAM,CAACC,MAAM,GAAGC,KAAK,GAAGJ;QAExB,IAAI9G,eAAeiH,OAAO;YACxB,MAAME,SAASF,KAAKzB,KAAK,CAAC,GAAG,CAAC,GAAGU,KAAK,CAAC;YAEvC,IAAIiB,OAAO7B,MAAM,GAAG,GAAG;gBACrB,KAAK,MAAM8B,SAASD,OAAQ;oBAC1B,uDAAuD;oBACvDJ,mBAAmB;wBAAC,CAAC,CAAC,EAAEK,MAAMC,IAAI,GAAG,CAAC,CAAC;2BAAKH;qBAAK,EAAEF;gBACrD;gBACA;YACF,OAAO;gBACL,4CAA4C;gBAC5CD,mBAAmBG,MAAMF,UAAU,GAAGA,QAAQ,EAAE,EAAEG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEA,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;YACjF,qEAAqE;YACvE;QACF,OAAO,IAAIH,SAAS;YAClBA,UAAU,GAAGA,QAAQ,CAAC,EAAEC,MAAM;QAChC,OAAO;YACLD,UAAUC;QACZ;QAEAF,mBAAmBG,MAAMF;IAC3B;IAEAD,mBAAmBD;IAEnB,OAAO3E,MAAM6D,IAAI,CAACH;AACpB;AAEO,eAAepG,+BACpBmD,SAAgC,EAChC,EACErC,QAAQ,IAAIC,KAAK,EACjB6C,QAAQ,EACRiE,aAAa,EACbC,YAAY,EAMb;IAED,MAAM,EAAE5D,cAAc,EAAE6D,YAAY,EAAE,GAAG,MAAM5E,UAAU6E,sBAAsB;IAE/E,MAAM9C,YAAY,MAAMC,qBAAqB;QAC3CC,QAAQjC;QACRxC,UAAUuD;QACV,4EAA4E;QAC5EZ,mBAAmB;QACnBM;QACAiE;IACF;IAEA,6CAA6C;IAC7C,KAAK,MAAM,CAACzG,OAAOK,SAAS,IAAIyD,UAAW;QACzCpE,MAAMU,GAAG,CAACJ,OAAOK;IACnB;IAEA,IAAIqG,gBAAgB3E,UAAU8E,8BAA8B,EAAE;QAC5D,wGAAwG;QACxG,MAAM/H,gCAAgCiD,UAAUzC,WAAW,EAAE;YAC3DC,UAAUoH;YACVlH,cAAc;YACdC;YACAF,aAAa,OAAO,EAAEU,QAAQ,EAAED,QAAQ,EAAE;gBACxCP,MAAMU,GAAG,CAACH,UAAU;oBAClBI,UAAUqG;oBACVnG,SAASL;oBACTC,cAAc;gBAChB;gBACA,OAAOuG;YACT;QACF;IACF;IAEA,OAAOhH;AACT;AAEA,eAAeqE,qBAAqB,EAClC7B,iBAAiB,EACjB8B,MAAM,EACNxB,QAAQ,EACRiE,aAAa,EACb,GAAGK,OAMJ;IACC,MAAM,EAAEvH,QAAQ,EAAEG,KAAK,EAAE,GAAG,MAAMsE,OAAO+C,8BAA8B,CAAC;QACtE/E,WAAW;QACXgF,mBAAmBF,MAAMvH,QAAQ;QACjC2C;QACAM;IACF;IAEA,8KAA8K;IAC9K,IAAIiE,eAAe;QACjBlH,SAAS0H,UAAU,GAAG,EAAE;QACxB1H,SAAS2H,cAAc,GAAG,EAAE;IAC9B;IAEAxH,MAAMU,GAAG,CAAC,qBAAqB;QAC7BC,UAAU8G,KAAKC,SAAS,CAAC7H,UAAU,MAAM;QACzCY,cAAc;IAChB;IAEA,OAAOT;AACT;AAEA,SAASuE,8BAA8B5C,MAAc;IACnD,MAAMyC,YAAYuD,IAAAA,gCAAwB,EAAChG;IAC3C,IAAIyC,UAAUW,MAAM,EAAE;QACpB,0CAA0C;QAC1CpC,QAAG,CAACiF,IAAI,CACNC,gBAAK,CAACC,MAAM,CAAC,0GAA0G,EAAE1D,UACtH/D,GAAG,CAAC,CAAC0H,IAAM9F,eAAI,CAAC+F,QAAQ,CAACrG,QAAQoG,IACjC/E,IAAI,CAAC,MAAM,CAAC;IAEnB;AACF"}