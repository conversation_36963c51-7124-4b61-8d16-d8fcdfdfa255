{"version": 3, "sources": ["../../../src/export/exportHermes.ts"], "sourcesContent": ["import { ExpoConfig, getConfigFilePaths, Platform } from '@expo/config';\nimport JsonFile from '@expo/json-file';\nimport fs from 'fs';\nimport path from 'path';\n\nexport async function assertEngineMismatchAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'ios' | 'android' | 'jsEngine'>,\n  platform: Platform\n) {\n  const isHermesManaged = isEnableHermesManaged(exp, platform);\n  const paths = getConfigFilePaths(projectRoot);\n  const configFilePath = paths.dynamicConfigPath ?? paths.staticConfigPath ?? 'app.json';\n  await maybeThrowFromInconsistentEngineAsync(\n    projectRoot,\n    configFilePath,\n    platform,\n    isHermesManaged\n  );\n}\n\nexport function isEnableHermesManaged(\n  expoConfig: Partial<Pick<ExpoConfig, 'ios' | 'android' | 'jsEngine'>>,\n  platform: string\n): boolean {\n  switch (platform) {\n    case 'android': {\n      return (expoConfig.android?.jsEngine ?? expoConfig.jsEngine) !== 'jsc';\n    }\n    case 'ios': {\n      return (expoConfig.ios?.jsEngine ?? expoConfig.jsEngine) !== 'jsc';\n    }\n    default:\n      return false;\n  }\n}\n\nexport function parseGradleProperties(content: string): Record<string, string> {\n  const result: Record<string, string> = {};\n  for (let line of content.split('\\n')) {\n    line = line.trim();\n    if (!line || line.startsWith('#')) {\n      continue;\n    }\n\n    const sepIndex = line.indexOf('=');\n    const key = line.slice(0, sepIndex);\n    const value = line.slice(sepIndex + 1);\n    result[key] = value;\n  }\n  return result;\n}\n\nexport async function maybeThrowFromInconsistentEngineAsync(\n  projectRoot: string,\n  configFilePath: string,\n  platform: string,\n  isHermesManaged: boolean\n): Promise<void> {\n  const configFileName = path.basename(configFilePath);\n  if (\n    platform === 'android' &&\n    (await maybeInconsistentEngineAndroidAsync(projectRoot, isHermesManaged))\n  ) {\n    throw new Error(\n      `JavaScript engine configuration is inconsistent between ${configFileName} and Android native project.\\n` +\n        `In ${configFileName}: Hermes is ${isHermesManaged ? 'enabled' : 'not enabled'}\\n` +\n        `In Android native project: Hermes is ${isHermesManaged ? 'not enabled' : 'enabled'}\\n` +\n        `Please check the following files for inconsistencies:\\n` +\n        `  - ${configFilePath}\\n` +\n        `  - ${path.join(projectRoot, 'android', 'gradle.properties')}\\n` +\n        `  - ${path.join(projectRoot, 'android', 'app', 'build.gradle')}\\n` +\n        'Learn more: https://expo.fyi/hermes-android-config'\n    );\n  }\n\n  if (platform === 'ios' && (await maybeInconsistentEngineIosAsync(projectRoot, isHermesManaged))) {\n    throw new Error(\n      `JavaScript engine configuration is inconsistent between ${configFileName} and iOS native project.\\n` +\n        `In ${configFileName}: Hermes is ${isHermesManaged ? 'enabled' : 'not enabled'}\\n` +\n        `In iOS native project: Hermes is ${isHermesManaged ? 'not enabled' : 'enabled'}\\n` +\n        `Please check the following files for inconsistencies:\\n` +\n        `  - ${configFilePath}\\n` +\n        `  - ${path.join(projectRoot, 'ios', 'Podfile')}\\n` +\n        `  - ${path.join(projectRoot, 'ios', 'Podfile.properties.json')}\\n` +\n        'Learn more: https://expo.fyi/hermes-ios-config'\n    );\n  }\n}\n\nexport async function maybeInconsistentEngineAndroidAsync(\n  projectRoot: string,\n  isHermesManaged: boolean\n): Promise<boolean> {\n  // Trying best to check android native project if by chance to be consistent between app config\n\n  // Check gradle.properties from prebuild template\n  const gradlePropertiesPath = path.join(projectRoot, 'android', 'gradle.properties');\n  if (fs.existsSync(gradlePropertiesPath)) {\n    const props = parseGradleProperties(await fs.promises.readFile(gradlePropertiesPath, 'utf8'));\n    const isHermesBare = props['hermesEnabled'] === 'true';\n    if (isHermesManaged !== isHermesBare) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nexport function isHermesPossiblyEnabled(projectRoot: string): boolean | null {\n  // Trying best to check ios native project if by chance to be consistent between app config\n\n  // Check ios/Podfile for \":hermes_enabled => true\"\n  const podfilePath = path.join(projectRoot, 'ios', 'Podfile');\n  if (fs.existsSync(podfilePath)) {\n    const content = fs.readFileSync(podfilePath, 'utf8');\n    const isPropsReference =\n      content.search(\n        /^\\s*:hermes_enabled\\s*=>\\s*podfile_properties\\['expo.jsEngine'\\]\\s*==\\s*nil\\s*\\|\\|\\s*podfile_properties\\['expo.jsEngine'\\]\\s*==\\s*'hermes',?/m\n      ) >= 0;\n    const isHermesBare = content.search(/^\\s*:hermes_enabled\\s*=>\\s*true,?\\s+/m) >= 0;\n    if (!isPropsReference && isHermesBare) {\n      return true;\n    }\n  }\n\n  // Check Podfile.properties.json from prebuild template\n  const podfilePropertiesPath = path.join(projectRoot, 'ios', 'Podfile.properties.json');\n  if (fs.existsSync(podfilePropertiesPath)) {\n    try {\n      const props = JsonFile.read(podfilePropertiesPath);\n      return props['expo.jsEngine'] === 'hermes';\n    } catch {\n      // ignore\n    }\n  }\n\n  return null;\n}\n\nexport async function maybeInconsistentEngineIosAsync(\n  projectRoot: string,\n  isHermesManaged: boolean\n): Promise<boolean> {\n  // Trying best to check ios native project if by chance to be consistent between app config\n\n  // Check ios/Podfile for \":hermes_enabled => true\"\n  const podfilePath = path.join(projectRoot, 'ios', 'Podfile');\n  if (fs.existsSync(podfilePath)) {\n    const content = await fs.promises.readFile(podfilePath, 'utf8');\n    const isPropsReference =\n      content.search(\n        /^\\s*:hermes_enabled\\s*=>\\s*podfile_properties\\['expo.jsEngine'\\]\\s*==\\s*nil\\s*\\|\\|\\s*podfile_properties\\['expo.jsEngine'\\]\\s*==\\s*'hermes',?/m\n      ) >= 0;\n    const isHermesBare = content.search(/^\\s*:hermes_enabled\\s*=>\\s*true,?\\s+/m) >= 0;\n    if (!isPropsReference && isHermesManaged !== isHermesBare) {\n      return true;\n    }\n  }\n\n  // Check Podfile.properties.json from prebuild template\n  const podfilePropertiesPath = path.join(projectRoot, 'ios', 'Podfile.properties.json');\n  if (fs.existsSync(podfilePropertiesPath)) {\n    const props = await parsePodfilePropertiesAsync(podfilePropertiesPath);\n    const isHermesBare = props['expo.jsEngine'] === 'hermes';\n    if (isHermesManaged !== isHermesBare) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n// https://github.com/facebook/hermes/blob/release-v0.5/include/hermes/BCGen/HBC/BytecodeFileFormat.h#L24-L25\nconst HERMES_MAGIC_HEADER = 'c61fbc03c103191f';\n\nexport async function isHermesBytecodeBundleAsync(file: string): Promise<boolean> {\n  const header = await readHermesHeaderAsync(file);\n  return header.subarray(0, 8).toString('hex') === HERMES_MAGIC_HEADER;\n}\n\nexport async function getHermesBytecodeBundleVersionAsync(file: string): Promise<number> {\n  const header = await readHermesHeaderAsync(file);\n  if (header.subarray(0, 8).toString('hex') !== HERMES_MAGIC_HEADER) {\n    throw new Error('Invalid hermes bundle file');\n  }\n  return header.readUInt32LE(8);\n}\n\nasync function readHermesHeaderAsync(file: string): Promise<Buffer> {\n  const fd = await fs.promises.open(file, 'r');\n  const buffer = Buffer.alloc(12);\n  await fd.read(buffer, 0, 12, null);\n  await fd.close();\n  return buffer;\n}\n\nasync function parsePodfilePropertiesAsync(\n  podfilePropertiesPath: string\n): Promise<Record<string, string>> {\n  try {\n    return JSON.parse(await fs.promises.readFile(podfilePropertiesPath, 'utf8'));\n  } catch {\n    return {};\n  }\n}\n\nexport function isAndroidUsingHermes(projectRoot: string) {\n  // Check gradle.properties from prebuild template\n  const gradlePropertiesPath = path.join(projectRoot, 'android', 'gradle.properties');\n  if (fs.existsSync(gradlePropertiesPath)) {\n    const props = parseGradleProperties(fs.readFileSync(gradlePropertiesPath, 'utf8'));\n    return props['hermesEnabled'] === 'true';\n  }\n\n  // Assume Hermes is used by default.\n  return true;\n}\n\nexport function isIosUsingHermes(projectRoot: string) {\n  // If nullish, then assume Hermes is used.\n  return isHermesPossiblyEnabled(projectRoot) !== false;\n}\n"], "names": ["assertEngineMismatchAsync", "getHermesBytecodeBundleVersionAsync", "isAndroidUsingHermes", "isEnableHermesManaged", "isHermesBytecodeBundleAsync", "isHermesPossiblyEnabled", "isIosUsingHermes", "maybeInconsistentEngineAndroidAsync", "maybeInconsistentEngineIosAsync", "maybeThrowFromInconsistentEngineAsync", "parseGradleProperties", "projectRoot", "exp", "platform", "isHermesManaged", "paths", "getConfigFilePaths", "config<PERSON><PERSON><PERSON><PERSON>", "dynamicConfigPath", "staticConfigPath", "expoConfig", "android", "jsEngine", "ios", "content", "result", "line", "split", "trim", "startsWith", "sepIndex", "indexOf", "key", "slice", "value", "configFileName", "path", "basename", "Error", "join", "gradlePropertiesPath", "fs", "existsSync", "props", "promises", "readFile", "isHermesBare", "podfilePath", "readFileSync", "isPropsReference", "search", "podfilePropertiesPath", "JsonFile", "read", "parsePodfilePropertiesAsync", "HERMES_MAGIC_HEADER", "file", "header", "readHermesHeaderAsync", "subarray", "toString", "readUInt32LE", "fd", "open", "buffer", "<PERSON><PERSON><PERSON>", "alloc", "close", "JSON", "parse"], "mappings": ";;;;;;;;;;;IAKsBA,yBAAyB;eAAzBA;;IAgLAC,mCAAmC;eAAnCA;;IA0BNC,oBAAoB;eAApBA;;IA1LAC,qBAAqB;eAArBA;;IA2JMC,2BAA2B;eAA3BA;;IAnENC,uBAAuB;eAAvBA;;IA8GAC,gBAAgB;eAAhBA;;IAjIMC,mCAAmC;eAAnCA;;IAkDAC,+BAA+B;eAA/BA;;IAvFAC,qCAAqC;eAArCA;;IAhBNC,qBAAqB;eAArBA;;;;yBArCyC;;;;;;;gEACpC;;;;;;;gEACN;;;;;;;gEACE;;;;;;;;;;;AAEV,eAAeV,0BACpBW,WAAmB,EACnBC,GAAqD,EACrDC,QAAkB;IAElB,MAAMC,kBAAkBX,sBAAsBS,KAAKC;IACnD,MAAME,QAAQC,IAAAA,4BAAkB,EAACL;IACjC,MAAMM,iBAAiBF,MAAMG,iBAAiB,IAAIH,MAAMI,gBAAgB,IAAI;IAC5E,MAAMV,sCACJE,aACAM,gBACAJ,UACAC;AAEJ;AAEO,SAASX,sBACdiB,UAAqE,EACrEP,QAAgB;IAEhB,OAAQA;QACN,KAAK;YAAW;oBACNO;gBAAR,OAAO,AAACA,CAAAA,EAAAA,sBAAAA,WAAWC,OAAO,qBAAlBD,oBAAoBE,QAAQ,KAAIF,WAAWE,QAAQ,AAAD,MAAO;YACnE;QACA,KAAK;YAAO;oBACFF;gBAAR,OAAO,AAACA,CAAAA,EAAAA,kBAAAA,WAAWG,GAAG,qBAAdH,gBAAgBE,QAAQ,KAAIF,WAAWE,QAAQ,AAAD,MAAO;YAC/D;QACA;YACE,OAAO;IACX;AACF;AAEO,SAASZ,sBAAsBc,OAAe;IACnD,MAAMC,SAAiC,CAAC;IACxC,KAAK,IAAIC,QAAQF,QAAQG,KAAK,CAAC,MAAO;QACpCD,OAAOA,KAAKE,IAAI;QAChB,IAAI,CAACF,QAAQA,KAAKG,UAAU,CAAC,MAAM;YACjC;QACF;QAEA,MAAMC,WAAWJ,KAAKK,OAAO,CAAC;QAC9B,MAAMC,MAAMN,KAAKO,KAAK,CAAC,GAAGH;QAC1B,MAAMI,QAAQR,KAAKO,KAAK,CAACH,WAAW;QACpCL,MAAM,CAACO,IAAI,GAAGE;IAChB;IACA,OAAOT;AACT;AAEO,eAAehB,sCACpBE,WAAmB,EACnBM,cAAsB,EACtBJ,QAAgB,EAChBC,eAAwB;IAExB,MAAMqB,iBAAiBC,eAAI,CAACC,QAAQ,CAACpB;IACrC,IACEJ,aAAa,aACZ,MAAMN,oCAAoCI,aAAaG,kBACxD;QACA,MAAM,IAAIwB,MACR,CAAC,wDAAwD,EAAEH,eAAe,8BAA8B,CAAC,GACvG,CAAC,GAAG,EAAEA,eAAe,YAAY,EAAErB,kBAAkB,YAAY,cAAc,EAAE,CAAC,GAClF,CAAC,qCAAqC,EAAEA,kBAAkB,gBAAgB,UAAU,EAAE,CAAC,GACvF,CAAC,uDAAuD,CAAC,GACzD,CAAC,IAAI,EAAEG,eAAe,EAAE,CAAC,GACzB,CAAC,IAAI,EAAEmB,eAAI,CAACG,IAAI,CAAC5B,aAAa,WAAW,qBAAqB,EAAE,CAAC,GACjE,CAAC,IAAI,EAAEyB,eAAI,CAACG,IAAI,CAAC5B,aAAa,WAAW,OAAO,gBAAgB,EAAE,CAAC,GACnE;IAEN;IAEA,IAAIE,aAAa,SAAU,MAAML,gCAAgCG,aAAaG,kBAAmB;QAC/F,MAAM,IAAIwB,MACR,CAAC,wDAAwD,EAAEH,eAAe,0BAA0B,CAAC,GACnG,CAAC,GAAG,EAAEA,eAAe,YAAY,EAAErB,kBAAkB,YAAY,cAAc,EAAE,CAAC,GAClF,CAAC,iCAAiC,EAAEA,kBAAkB,gBAAgB,UAAU,EAAE,CAAC,GACnF,CAAC,uDAAuD,CAAC,GACzD,CAAC,IAAI,EAAEG,eAAe,EAAE,CAAC,GACzB,CAAC,IAAI,EAAEmB,eAAI,CAACG,IAAI,CAAC5B,aAAa,OAAO,WAAW,EAAE,CAAC,GACnD,CAAC,IAAI,EAAEyB,eAAI,CAACG,IAAI,CAAC5B,aAAa,OAAO,2BAA2B,EAAE,CAAC,GACnE;IAEN;AACF;AAEO,eAAeJ,oCACpBI,WAAmB,EACnBG,eAAwB;IAExB,+FAA+F;IAE/F,iDAAiD;IACjD,MAAM0B,uBAAuBJ,eAAI,CAACG,IAAI,CAAC5B,aAAa,WAAW;IAC/D,IAAI8B,aAAE,CAACC,UAAU,CAACF,uBAAuB;QACvC,MAAMG,QAAQjC,sBAAsB,MAAM+B,aAAE,CAACG,QAAQ,CAACC,QAAQ,CAACL,sBAAsB;QACrF,MAAMM,eAAeH,KAAK,CAAC,gBAAgB,KAAK;QAChD,IAAI7B,oBAAoBgC,cAAc;YACpC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEO,SAASzC,wBAAwBM,WAAmB;IACzD,2FAA2F;IAE3F,kDAAkD;IAClD,MAAMoC,cAAcX,eAAI,CAACG,IAAI,CAAC5B,aAAa,OAAO;IAClD,IAAI8B,aAAE,CAACC,UAAU,CAACK,cAAc;QAC9B,MAAMvB,UAAUiB,aAAE,CAACO,YAAY,CAACD,aAAa;QAC7C,MAAME,mBACJzB,QAAQ0B,MAAM,CACZ,oJACG;QACP,MAAMJ,eAAetB,QAAQ0B,MAAM,CAAC,4CAA4C;QAChF,IAAI,CAACD,oBAAoBH,cAAc;YACrC,OAAO;QACT;IACF;IAEA,uDAAuD;IACvD,MAAMK,wBAAwBf,eAAI,CAACG,IAAI,CAAC5B,aAAa,OAAO;IAC5D,IAAI8B,aAAE,CAACC,UAAU,CAACS,wBAAwB;QACxC,IAAI;YACF,MAAMR,QAAQS,mBAAQ,CAACC,IAAI,CAACF;YAC5B,OAAOR,KAAK,CAAC,gBAAgB,KAAK;QACpC,EAAE,OAAM;QACN,SAAS;QACX;IACF;IAEA,OAAO;AACT;AAEO,eAAenC,gCACpBG,WAAmB,EACnBG,eAAwB;IAExB,2FAA2F;IAE3F,kDAAkD;IAClD,MAAMiC,cAAcX,eAAI,CAACG,IAAI,CAAC5B,aAAa,OAAO;IAClD,IAAI8B,aAAE,CAACC,UAAU,CAACK,cAAc;QAC9B,MAAMvB,UAAU,MAAMiB,aAAE,CAACG,QAAQ,CAACC,QAAQ,CAACE,aAAa;QACxD,MAAME,mBACJzB,QAAQ0B,MAAM,CACZ,oJACG;QACP,MAAMJ,eAAetB,QAAQ0B,MAAM,CAAC,4CAA4C;QAChF,IAAI,CAACD,oBAAoBnC,oBAAoBgC,cAAc;YACzD,OAAO;QACT;IACF;IAEA,uDAAuD;IACvD,MAAMK,wBAAwBf,eAAI,CAACG,IAAI,CAAC5B,aAAa,OAAO;IAC5D,IAAI8B,aAAE,CAACC,UAAU,CAACS,wBAAwB;QACxC,MAAMR,QAAQ,MAAMW,4BAA4BH;QAChD,MAAML,eAAeH,KAAK,CAAC,gBAAgB,KAAK;QAChD,IAAI7B,oBAAoBgC,cAAc;YACpC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,6GAA6G;AAC7G,MAAMS,sBAAsB;AAErB,eAAenD,4BAA4BoD,IAAY;IAC5D,MAAMC,SAAS,MAAMC,sBAAsBF;IAC3C,OAAOC,OAAOE,QAAQ,CAAC,GAAG,GAAGC,QAAQ,CAAC,WAAWL;AACnD;AAEO,eAAetD,oCAAoCuD,IAAY;IACpE,MAAMC,SAAS,MAAMC,sBAAsBF;IAC3C,IAAIC,OAAOE,QAAQ,CAAC,GAAG,GAAGC,QAAQ,CAAC,WAAWL,qBAAqB;QACjE,MAAM,IAAIjB,MAAM;IAClB;IACA,OAAOmB,OAAOI,YAAY,CAAC;AAC7B;AAEA,eAAeH,sBAAsBF,IAAY;IAC/C,MAAMM,KAAK,MAAMrB,aAAE,CAACG,QAAQ,CAACmB,IAAI,CAACP,MAAM;IACxC,MAAMQ,SAASC,OAAOC,KAAK,CAAC;IAC5B,MAAMJ,GAAGT,IAAI,CAACW,QAAQ,GAAG,IAAI;IAC7B,MAAMF,GAAGK,KAAK;IACd,OAAOH;AACT;AAEA,eAAeV,4BACbH,qBAA6B;IAE7B,IAAI;QACF,OAAOiB,KAAKC,KAAK,CAAC,MAAM5B,aAAE,CAACG,QAAQ,CAACC,QAAQ,CAACM,uBAAuB;IACtE,EAAE,OAAM;QACN,OAAO,CAAC;IACV;AACF;AAEO,SAASjD,qBAAqBS,WAAmB;IACtD,iDAAiD;IACjD,MAAM6B,uBAAuBJ,eAAI,CAACG,IAAI,CAAC5B,aAAa,WAAW;IAC/D,IAAI8B,aAAE,CAACC,UAAU,CAACF,uBAAuB;QACvC,MAAMG,QAAQjC,sBAAsB+B,aAAE,CAACO,YAAY,CAACR,sBAAsB;QAC1E,OAAOG,KAAK,CAAC,gBAAgB,KAAK;IACpC;IAEA,oCAAoC;IACpC,OAAO;AACT;AAEO,SAASrC,iBAAiBK,WAAmB;IAClD,0CAA0C;IAC1C,OAAON,wBAAwBM,iBAAiB;AAClD"}