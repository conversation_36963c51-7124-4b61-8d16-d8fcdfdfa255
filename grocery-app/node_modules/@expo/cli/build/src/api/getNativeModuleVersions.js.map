{"version": 3, "sources": ["../../../src/api/getNativeModuleVersions.ts"], "sourcesContent": ["import { createCachedFetch, getResponseDataOrThrow } from './rest/client';\nimport { CommandError } from '../utils/errors';\n\ninterface NativeModule {\n  npmPackage: string;\n  versionRange: string;\n}\ntype BundledNativeModuleList = NativeModule[];\n\nexport type BundledNativeModules = Record<string, string>;\n\n/**\n * The endpoint returns the list of bundled native modules for a given SDK version.\n * The data is populated by the `et sync-bundled-native-modules` script from expo/expo repo.\n * See the code for more details:\n * https://github.com/expo/expo/blob/main/tools/src/commands/SyncBundledNativeModules.ts\n *\n * Example result:\n * [\n *   {\n *     id: \"79285187-e5c4-47f7-b6a9-664f5d16f0db\",\n *     sdkVersion: \"41.0.0\",\n *     npmPackage: \"expo-camera\",\n *     versionRange: \"~10.1.0\",\n *     createdAt: \"2021-04-29T09:34:32.825Z\",\n *     updatedAt: \"2021-04-29T09:34:32.825Z\"\n *   },\n *   ...\n * ]\n */\nexport async function getNativeModuleVersionsAsync(\n  sdkVersion: string\n): Promise<BundledNativeModules> {\n  const fetchAsync = createCachedFetch({\n    cacheDirectory: 'native-modules-cache',\n    // 1 minute cache\n    ttl: 1000 * 60,\n  });\n  const response = await fetchAsync(`sdks/${sdkVersion}/native-modules`);\n  if (!response.ok) {\n    throw new CommandError(\n      'API',\n      `Unexpected response when fetching version info from Expo servers: ${response.statusText}.`\n    );\n  }\n\n  const json = await response.json();\n  const data = getResponseDataOrThrow<BundledNativeModuleList>(json);\n  if (!data.length) {\n    throw new CommandError('VERSIONS', 'The bundled native module list from the Expo API is empty');\n  }\n  return fromBundledNativeModuleList(data);\n}\n\nfunction fromBundledNativeModuleList(list: BundledNativeModuleList): BundledNativeModules {\n  return list.reduce((acc, i) => {\n    acc[i.npmPackage] = i.versionRange;\n    return acc;\n  }, {} as BundledNativeModules);\n}\n"], "names": ["getNativeModuleVersionsAsync", "sdkVersion", "fetchAsync", "createCachedFetch", "cacheDirectory", "ttl", "response", "ok", "CommandError", "statusText", "json", "data", "getResponseDataOrThrow", "length", "fromBundledNativeModuleList", "list", "reduce", "acc", "i", "npmPackage", "versionRange"], "mappings": ";;;;+BA8BsBA;;;eAAAA;;;wBA9BoC;wBAC7B;AA6BtB,eAAeA,6BACpBC,UAAkB;IAElB,MAAMC,aAAaC,IAAAA,yBAAiB,EAAC;QACnCC,gBAAgB;QAChB,iBAAiB;QACjBC,KAAK,OAAO;IACd;IACA,MAAMC,WAAW,MAAMJ,WAAW,CAAC,KAAK,EAAED,WAAW,eAAe,CAAC;IACrE,IAAI,CAACK,SAASC,EAAE,EAAE;QAChB,MAAM,IAAIC,oBAAY,CACpB,OACA,CAAC,kEAAkE,EAAEF,SAASG,UAAU,CAAC,CAAC,CAAC;IAE/F;IAEA,MAAMC,OAAO,MAAMJ,SAASI,IAAI;IAChC,MAAMC,OAAOC,IAAAA,8BAAsB,EAA0BF;IAC7D,IAAI,CAACC,KAAKE,MAAM,EAAE;QAChB,MAAM,IAAIL,oBAAY,CAAC,YAAY;IACrC;IACA,OAAOM,4BAA4BH;AACrC;AAEA,SAASG,4BAA4BC,IAA6B;IAChE,OAAOA,KAAKC,MAAM,CAAC,CAACC,KAAKC;QACvBD,GAAG,CAACC,EAAEC,UAAU,CAAC,GAAGD,EAAEE,YAAY;QAClC,OAAOH;IACT,GAAG,CAAC;AACN"}