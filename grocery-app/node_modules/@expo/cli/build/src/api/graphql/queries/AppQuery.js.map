{"version": 3, "sources": ["../../../../../src/api/graphql/queries/AppQuery.ts"], "sourcesContent": ["import { gql } from '@urql/core';\n\nimport { AppByIdQuery } from '../../../graphql/generated';\nimport { graphqlClient, withErrorHandlingAsync } from '../client';\nimport { AppFragmentNode } from '../types/App';\n\nexport const AppQuery = {\n  async byIdAsync(projectId: string): Promise<AppByIdQuery['app']['byId']> {\n    const data = await withErrorHandlingAsync(\n      graphqlClient\n        .query<AppByIdQuery>(\n          gql`\n            query AppByIdQuery($appId: String!) {\n              app {\n                byId(appId: $appId) {\n                  id\n                  ...AppFragment\n                }\n              }\n            }\n\n            ${AppFragmentNode}\n          `,\n          { appId: projectId },\n          {\n            additionalTypenames: ['App'],\n          }\n        )\n        .toPromise()\n    );\n    return data.app.byId;\n  },\n};\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "byIdAsync", "projectId", "data", "withErrorHandlingAsync", "graphqlClient", "query", "gql", "AppFragmentNode", "appId", "additionalTypenames", "to<PERSON>romise", "app", "byId"], "mappings": ";;;;+BAMaA;;;eAAAA;;;;yBANO;;;;;;wBAGkC;qBACtB;AAEzB,MAAMA,WAAW;IACtB,MAAMC,WAAUC,SAAiB;QAC/B,MAAMC,OAAO,MAAMC,IAAAA,8BAAsB,EACvCC,qBAAa,CACVC,KAAK,CACJC,IAAAA,WAAG,CAAA,CAAC;;;;;;;;;;YAUF,EAAEC,oBAAe,CAAC;UACpB,CAAC,EACD;YAAEC,OAAOP;QAAU,GACnB;YACEQ,qBAAqB;gBAAC;aAAM;QAC9B,GAEDC,SAAS;QAEd,OAAOR,KAAKS,GAAG,CAACC,IAAI;IACtB;AACF"}