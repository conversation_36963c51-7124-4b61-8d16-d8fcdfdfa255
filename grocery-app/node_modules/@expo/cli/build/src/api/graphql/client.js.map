{"version": 3, "sources": ["../../../../src/api/graphql/client.ts"], "sourcesContent": ["import {\n  cacheExchange,\n  Client,\n  CombinedError as GraphqlError,\n  AnyVariables,\n  DocumentInput,\n  createClient as createUrqlClient,\n  fetchExchange,\n  OperationContext,\n  OperationResult,\n  OperationResultSource,\n} from '@urql/core';\nimport { retryExchange } from '@urql/exchange-retry';\n\nimport * as Log from '../../log';\nimport { fetch } from '../../utils/fetch';\nimport { getExpoApiBaseUrl } from '../endpoint';\nimport { wrapFetchWithOffline } from '../rest/wrapFetchWithOffline';\nimport { wrapFetchWithProxy } from '../rest/wrapFetchWithProxy';\nimport { wrapFetchWithUserAgent } from '../rest/wrapFetchWithUserAgent';\nimport { getAccessToken, getSession } from '../user/UserSettings';\n\ntype AccessTokenHeaders = {\n  authorization: string;\n};\n\ntype SessionHeaders = {\n  'expo-session': string;\n};\n\nexport const graphqlClient = createUrqlClient({\n  url: getExpoApiBaseUrl() + '/graphql',\n  exchanges: [\n    cacheExchange,\n    retryExchange({\n      maxDelayMs: 4000,\n      retryIf: (err) =>\n        !!(err && (err.networkError || err.graphQLErrors.some((e) => e?.extensions?.isTransient))),\n    }),\n    fetchExchange,\n  ],\n  // @ts-ignore Type 'typeof fetch' is not assignable to type '(input: RequestInfo, init?: RequestInit | undefined) => Promise<Response>'.\n  fetch: wrapFetchWithOffline(wrapFetchWithProxy(wrapFetchWithUserAgent(fetch))),\n  fetchOptions: (): { headers?: AccessTokenHeaders | SessionHeaders } => {\n    const token = getAccessToken();\n    if (token) {\n      return {\n        headers: {\n          authorization: `Bearer ${token}`,\n        },\n      };\n    }\n    const sessionSecret = getSession()?.sessionSecret;\n    if (sessionSecret) {\n      return {\n        headers: {\n          'expo-session': sessionSecret,\n        },\n      };\n    }\n    return {};\n  },\n}) as StricterClient;\n\n/* Please specify additionalTypenames in your Graphql queries */\nexport interface StricterClient extends Client {\n  query<Data = any, Variables extends AnyVariables = AnyVariables>(\n    query: DocumentInput<Data, Variables>,\n    variables: Variables,\n    context: Partial<OperationContext> & { additionalTypenames: string[] }\n  ): OperationResultSource<OperationResult<Data, Variables>>;\n}\n\nexport async function withErrorHandlingAsync<T>(promise: Promise<OperationResult<T>>): Promise<T> {\n  const { data, error } = await promise;\n\n  if (error) {\n    if (error.graphQLErrors.some((e) => e?.extensions?.isTransient)) {\n      Log.error(`We've encountered a transient error, please try again shortly.`);\n    }\n    throw error;\n  }\n\n  // Check for a malformed response. This only checks the root query's existence. It doesn't affect\n  // returning responses with an empty result set.\n  if (!data) {\n    throw new Error('Returned query result data is null!');\n  }\n\n  return data;\n}\n\nexport { GraphqlError };\n"], "names": ["GraphqlError", "graphqlClient", "withErrorHandlingAsync", "createUrqlClient", "url", "getExpoApiBaseUrl", "exchanges", "cacheExchange", "retryExchange", "max<PERSON>elay<PERSON>", "retryIf", "err", "networkError", "graphQLErrors", "some", "e", "extensions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchExchange", "fetch", "wrapFetchWithOffline", "wrapFetchWithProxy", "wrapFetchWithUserAgent", "fetchOptions", "getSession", "token", "getAccessToken", "headers", "authorization", "sessionSecret", "promise", "data", "error", "Log", "Error"], "mappings": ";;;;;;;;;;;IA4FSA,YAAY;eAAZA,qBAAY;;IA9DRC,aAAa;eAAbA;;IA2CSC,sBAAsB;eAAtBA;;;;yBA9Df;;;;;;;yBACuB;;;;;;6DAET;uBACC;0BACY;sCACG;oCACF;wCACI;8BACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpC,MAAMD,gBAAgBE,IAAAA,oBAAgB,EAAC;IAC5CC,KAAKC,IAAAA,2BAAiB,MAAK;IAC3BC,WAAW;QACTC,qBAAa;QACbC,IAAAA,8BAAa,EAAC;YACZC,YAAY;YACZC,SAAS,CAACC,MACR,CAAC,CAAEA,CAAAA,OAAQA,CAAAA,IAAIC,YAAY,IAAID,IAAIE,aAAa,CAACC,IAAI,CAAC,CAACC;wBAAMA;2BAAAA,sBAAAA,gBAAAA,EAAGC,UAAU,qBAAbD,cAAeE,WAAW;kBAAA,CAAC;QAC5F;QACAC,qBAAa;KACd;IACD,wIAAwI;IACxIC,OAAOC,IAAAA,0CAAoB,EAACC,IAAAA,sCAAkB,EAACC,IAAAA,8CAAsB,EAACH,YAAK;IAC3EI,cAAc;YASUC;QARtB,MAAMC,QAAQC,IAAAA,4BAAc;QAC5B,IAAID,OAAO;YACT,OAAO;gBACLE,SAAS;oBACPC,eAAe,CAAC,OAAO,EAAEH,OAAO;gBAClC;YACF;QACF;QACA,MAAMI,iBAAgBL,cAAAA,IAAAA,wBAAU,wBAAVA,YAAcK,aAAa;QACjD,IAAIA,eAAe;YACjB,OAAO;gBACLF,SAAS;oBACP,gBAAgBE;gBAClB;YACF;QACF;QACA,OAAO,CAAC;IACV;AACF;AAWO,eAAe3B,uBAA0B4B,OAAoC;IAClF,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMF;IAE9B,IAAIE,OAAO;QACT,IAAIA,MAAMnB,aAAa,CAACC,IAAI,CAAC,CAACC;gBAAMA;mBAAAA,sBAAAA,gBAAAA,EAAGC,UAAU,qBAAbD,cAAeE,WAAW;YAAG;YAC/DgB,KAAID,KAAK,CAAC,CAAC,8DAA8D,CAAC;QAC5E;QACA,MAAMA;IACR;IAEA,iGAAiG;IACjG,gDAAgD;IAChD,IAAI,CAACD,MAAM;QACT,MAAM,IAAIG,MAAM;IAClB;IAEA,OAAOH;AACT"}