{"version": 3, "sources": ["../../../src/api/getExpoGoIntermediateCertificate.ts"], "sourcesContent": ["import { fetchAsync } from './rest/client';\nimport { CommandError } from '../utils/errors';\n\nexport async function getExpoGoIntermediateCertificateAsync(easProjectId: string): Promise<string> {\n  const response = await fetchAsync(\n    `projects/${encodeURIComponent(\n      easProjectId\n    )}/development-certificates/expo-go-intermediate-certificate`,\n    {\n      method: 'GET',\n    }\n  );\n  if (!response.ok) {\n    throw new CommandError('API', `Unexpected error from Expo servers: ${response.statusText}.`);\n  }\n\n  return await response.text();\n}\n"], "names": ["getExpoGoIntermediateCertificateAsync", "easProjectId", "response", "fetchAsync", "encodeURIComponent", "method", "ok", "CommandError", "statusText", "text"], "mappings": ";;;;+BAGsBA;;;eAAAA;;;wBAHK;wBACE;AAEtB,eAAeA,sCAAsCC,YAAoB;IAC9E,MAAMC,WAAW,MAAMC,IAAAA,kBAAU,EAC/B,CAAC,SAAS,EAAEC,mBACVH,cACA,0DAA0D,CAAC,EAC7D;QACEI,QAAQ;IACV;IAEF,IAAI,CAACH,SAASI,EAAE,EAAE;QAChB,MAAM,IAAIC,oBAAY,CAAC,OAAO,CAAC,oCAAoC,EAAEL,SAASM,UAAU,CAAC,CAAC,CAAC;IAC7F;IAEA,OAAO,MAAMN,SAASO,IAAI;AAC5B"}