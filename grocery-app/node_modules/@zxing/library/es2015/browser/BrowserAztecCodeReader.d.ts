import { Browser<PERSON>odeReader } from './BrowserCodeReader';
/**
 * Aztec Code reader to use from browser.
 *
 * @class BrowserAztecCodeReader
 * @extends {BrowserCodeReader}
 */
export declare class Browser<PERSON>ztecCodeReader extends Browser<PERSON><PERSON>Reader {
    /**
     * Creates an instance of BrowserAztecCodeReader.
     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries
     *
     * @memberOf BrowserAztecCodeReader
     */
    constructor(timeBetweenScansMillis?: number);
}
