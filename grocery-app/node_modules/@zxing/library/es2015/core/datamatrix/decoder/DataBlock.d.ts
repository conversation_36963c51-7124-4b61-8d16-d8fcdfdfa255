import Version from './Version';
/**
 * <p>Encapsulates a block of data within a Data Matrix Code. Data Matrix Codes may split their data into
 * multiple blocks, each of which is a unit of data and error-correction codewords. Each
 * is represented by an instance of this class.</p>
 *
 * <AUTHOR> (<PERSON>)
 */
export default class DataBlock {
    private numDataCodewords;
    private codewords;
    constructor(numDataCodewords: number, codewords: Uint8Array);
    /**
     * <p>When Data Matrix Codes use multiple data blocks, they actually interleave the bytes of each of them.
     * That is, the first byte of data block 1 to n is written, then the second bytes, and so on. This
     * method will separate the data into original blocks.</p>
     *
     * @param rawCodewords bytes as read directly from the Data Matrix Code
     * @param version version of the Data Matrix Code
     * @return DataBlocks containing original bytes, "de-interleaved" from representation in the
     *         Data Matrix Code
     */
    static getDataBlocks(rawCodewords: Int8Array, version: Version): DataBlock[];
    getNumDataCodewords(): number;
    getCodewords(): Uint8Array;
}
