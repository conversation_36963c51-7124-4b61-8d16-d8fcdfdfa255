import { Browser<PERSON>odeReader } from './BrowserCodeReader';
/**
 * @deprecated Moving to @zxing/browser
 *
 * QR Code reader to use from browser.
 */
export declare class BrowserQRCodeReader extends Browser<PERSON>odeReader {
    /**
     * Creates an instance of BrowserQ<PERSON>odeReader.
     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries
     */
    constructor(timeBetweenScansMillis?: number);
}
