{"name": "@zxing/library", "version": "0.21.3", "description": "TypeScript port of ZXing multi-format 1D/2D barcode image processing library.", "keywords": ["reader", "writer", "decode", "encode", "scanner", "generator", "barcode", "qr-code", "barcode 1d", "barcode 2d", "typescript", "zxing"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">= 10.4.0"}, "license": "MIT", "bugs": "https://github.com/zxing-js/library/issues/new", "repository": {"type": "git", "url": "https://github.com/zxing-js/library"}, "homepage": "https://zxing-js.github.io/library/", "private": false, "main": "./cjs/index.js", "module": "./esm/index.js", "typings": "./esm/index.d.ts", "esnext": "./es2015/index.js", "unpkg": "./umd/index.min.js", "scripts": {"lint": "yarn tslint --project .", "clean": "yarn shx rm -rf dist output", "test": "yarn test:build && yarn test:run", "test:build": "tsc --build tsconfig.test.json", "test:run": "mocha -r tsconfig-paths/register --timeout 200000 output/tests/**/*.spec.js", "cover": "nyc --reporter=lcov --reporter=text yarn test", "build": "yarn clean && yarn build:es2015 && yarn build:esm && yarn build:esnext && yarn build:cjs && yarn build:umd && yarn build:umd:min && yarn build:copy", "build:es2015": "tsc --build tsconfig.lib-es2015.json", "build:esm": "tsc --build tsconfig.lib-esm.json", "build:esnext": "tsc --build tsconfig.lib-esm.json", "build:cjs": "tsc --build tsconfig.lib-cjs.json", "build:umd": "rollup -c rollup.config.js", "build:umd:min": "cd dist/umd && terser --compress --mangle --source-map --screw-ie8 --comments -o index.min.js -- index.js && gzip index.min.js -c > index.min.js.gz", "build:copy": "cp README.md dist && cp package.json dist && cp LICENSE dist", "shx": "./node_modules/.bin/shx", "tsc": "./node_modules/.bin/tsc", "tslint": "./node_modules/.bin/tslint \"./src/**/*.ts\""}, "dependencies": {"ts-custom-error": "^3.2.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^7.1.3", "@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^10.12.29", "@types/seedrandom": "^2.4.27", "@types/sharp": "^0.22.2", "@zxing/text-encoding": "~0.9.0", "chai": "^4.2.0", "codacy-coverage": "^3.4.0", "eslint": "^5.15.1", "karma": "^6.3.16", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-mocha": "^1.3.0", "karma-remap-coverage": "^0.1.5", "karma-sinon": "^1.0.5", "karma-sourcemap-loader": "^0.3.7", "karma-typescript": "^3.0.13", "karma-typescript-preprocessor": "^0.4.0", "mocha": "^6.2.3", "mocha-lcov-reporter": "^1.3.0", "nyc": "^15.1.0", "rollup": "^2.8.2", "seedrandom": "^2.4.4", "sharp": "^0.32.6", "shx": "0.3.4", "sinon": "^7.2.7", "terser": "^5.16.1", "ts-node": "^9.0.0", "tsconfig-paths": "^3.9.0", "tslint": "^6.1.3", "tslint-no-circular-imports": "^0.7.0", "typescript": "^3", "yarn": "^1.17.3"}, "optionalDependencies": {"@zxing/text-encoding": "~0.9.0"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/zxing-js", "logo": "https://opencollective.com/zxing-js/logo.txt"}}