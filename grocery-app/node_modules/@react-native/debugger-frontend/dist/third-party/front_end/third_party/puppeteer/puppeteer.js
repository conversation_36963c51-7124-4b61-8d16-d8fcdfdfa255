var e=function(t,r){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};function t(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function i(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(i.prototype=r.prototype,new i)}function r(e,t,r,i){return new(r||(r=Promise))((function(n,s){function a(e){try{c(i.next(e))}catch(e){s(e)}}function o(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,o)}c((i=i.apply(e,t||[])).next())}))}function i(e,t){var r,i,n,s,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function o(o){return function(c){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;s&&(s=0,o[0]&&(a=0)),a;)try{if(r=1,i&&(n=2&o[0]?i.return:o[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,o[1])).done)return n;switch(i=0,n&&(o=[2&o[0],n.value]),o[0]){case 0:case 1:n=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(n=a.trys,(n=n.length>0&&n[n.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!n||o[1]>n[0]&&o[1]<n[3])){a.label=o[1];break}if(6===o[0]&&a.label<n[1]){a.label=n[1],n=o;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(o);break}n[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],i=0}finally{r=n=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}}function n(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],i=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function s(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var i,n,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=s.next()).done;)a.push(i.value)}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return a}function a(e,t,r){if(r||2===arguments.length)for(var i,n=0,s=t.length;n<s;n++)!i&&n in t||(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}function o(e){return this instanceof o?(this.v=e,this):new o(e)}function c(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i,n=r.apply(e,t||[]),s=[];return i={},a("next"),a("throw"),a("return"),i[Symbol.asyncIterator]=function(){return this},i;function a(e){n[e]&&(i[e]=function(t){return new Promise((function(r,i){s.push([e,t,r,i])>1||c(e,t)}))})}function c(e,t){try{(r=n[e](t)).value instanceof o?Promise.resolve(r.value.v).then(l,d):u(s[0][2],r)}catch(e){u(s[0][3],e)}var r}function l(e){c("next",e)}function d(e){c("throw",e)}function u(e,t){e(t),s.shift(),s.length&&c(s[0][0],s[0][1])}}function l(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=n(e),t={},i("next"),i("throw"),i("return"),t[Symbol.asyncIterator]=function(){return this},t);function i(r){t[r]=e[r]&&function(t){return new Promise((function(i,n){(function(e,t,r,i){Promise.resolve(i).then((function(t){e({value:t,done:r})}),t)})(i,n,(t=e[r](t)).done,t.value)}))}}}function d(e){return"function"==typeof e}function u(e){var t=e((function(e){Error.call(e),e.stack=(new Error).stack}));return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var h=u((function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map((function(e,t){return t+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}}));function p(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var f=function(){function e(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}var t;return e.prototype.unsubscribe=function(){var e,t,r,i,o;if(!this.closed){this.closed=!0;var c=this._parentage;if(c)if(this._parentage=null,Array.isArray(c))try{for(var l=n(c),u=l.next();!u.done;u=l.next()){u.value.remove(this)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(t=l.return)&&t.call(l)}finally{if(e)throw e.error}}else c.remove(this);var p=this.initialTeardown;if(d(p))try{p()}catch(e){o=e instanceof h?e.errors:[e]}var f=this._finalizers;if(f){this._finalizers=null;try{for(var m=n(f),y=m.next();!y.done;y=m.next()){var w=y.value;try{g(w)}catch(e){o=null!=o?o:[],e instanceof h?o=a(a([],s(o)),s(e.errors)):o.push(e)}}}catch(e){r={error:e}}finally{try{y&&!y.done&&(i=m.return)&&i.call(m)}finally{if(r)throw r.error}}}if(o)throw new h(o)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)g(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!==(r=this._finalizers)&&void 0!==r?r:[]).push(t)}},e.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},e.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},e.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&p(t,e)},e.prototype.remove=function(t){var r=this._finalizers;r&&p(r,t),t instanceof e&&t._removeParent(this)},e.EMPTY=((t=new e).closed=!0,t),e}(),m=f.EMPTY;function y(e){return e instanceof f||e&&"closed"in e&&d(e.remove)&&d(e.add)&&d(e.unsubscribe)}function g(e){d(e)?e():e.unsubscribe()}var w={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},v={setTimeout:function(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];var n=v.delegate;return(null==n?void 0:n.setTimeout)?n.setTimeout.apply(n,a([e,t],s(r))):setTimeout.apply(void 0,a([e,t],s(r)))},clearTimeout:function(e){var t=v.delegate;return((null==t?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function b(e){v.setTimeout((function(){var t=w.onUnhandledError;if(!t)throw e;t(e)}))}function k(){}var S=C("C",void 0,void 0);function C(e,t,r){return{kind:e,value:t,error:r}}var T=null;function E(e){if(w.useDeprecatedSynchronousErrorHandling){var t=!T;if(t&&(T={errorThrown:!1,error:null}),e(),t){var r=T,i=r.errorThrown,n=r.error;if(T=null,i)throw n}}else e()}var I=function(e){function r(t){var r=e.call(this)||this;return r.isStopped=!1,t?(r.destination=t,y(t)&&t.add(r)):r.destination=M,r}return t(r,e),r.create=function(e,t,r){return new D(e,t,r)},r.prototype.next=function(e){this.isStopped?R(function(e){return C("N",e,void 0)}(e),this):this._next(e)},r.prototype.error=function(e){this.isStopped?R(C("E",void 0,e),this):(this.isStopped=!0,this._error(e))},r.prototype.complete=function(){this.isStopped?R(S,this):(this.isStopped=!0,this._complete())},r.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},r.prototype._next=function(e){this.destination.next(e)},r.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},r.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},r}(f),x=Function.prototype.bind;function F(e,t){return x.call(e,t)}var _=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){P(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){P(e)}else P(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){P(e)}},e}(),D=function(e){function r(t,r,i){var n,s,a=e.call(this)||this;d(t)||!t?n={next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=i?i:void 0}:a&&w.useDeprecatedNextContext?((s=Object.create(t)).unsubscribe=function(){return a.unsubscribe()},n={next:t.next&&F(t.next,s),error:t.error&&F(t.error,s),complete:t.complete&&F(t.complete,s)}):n=t;return a.destination=new _(n),a}return t(r,e),r}(I);function P(e){var t;w.useDeprecatedSynchronousErrorHandling?(t=e,w.useDeprecatedSynchronousErrorHandling&&T&&(T.errorThrown=!0,T.error=t)):b(e)}function R(e,t){var r=w.onStoppedNotification;r&&v.setTimeout((function(){return r(e,t)}))}var M={closed:!0,next:k,error:function(e){throw e},complete:k},A="function"==typeof Symbol&&Symbol.observable||"@@observable";function O(e){return e}function q(e){return 0===e.length?O:1===e.length?e[0]:function(t){return e.reduce((function(e,t){return t(e)}),t)}}var N=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var i,n=this,s=(i=e)&&i instanceof I||function(e){return e&&d(e.next)&&d(e.error)&&d(e.complete)}(i)&&y(i)?e:new D(e,t,r);return E((function(){var e=n,t=e.operator,r=e.source;s.add(t?t.call(s,r):r?n._subscribe(s):n._trySubscribe(s))})),s},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=B(t))((function(t,i){var n=new D({next:function(t){try{e(t)}catch(e){i(e),n.unsubscribe()}},error:i,complete:t});r.subscribe(n)}))},e.prototype._subscribe=function(e){var t;return null===(t=this.source)||void 0===t?void 0:t.subscribe(e)},e.prototype[A]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return q(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=B(e))((function(e,r){var i;t.subscribe((function(e){return i=e}),(function(e){return r(e)}),(function(){return e(i)}))}))},e.create=function(t){return new e(t)},e}();function B(e){var t;return null!==(t=null!=e?e:w.Promise)&&void 0!==t?t:Promise}function L(e){return function(t){if(function(e){return d(null==e?void 0:e.lift)}(t))return t.lift((function(t){try{return e(t,this)}catch(e){this.error(e)}}));throw new TypeError("Unable to lift unknown Observable type")}}function j(e,t,r,i,n){return new H(e,t,r,i,n)}var H=function(e){function r(t,r,i,n,s,a){var o=e.call(this,t)||this;return o.onFinalize=s,o.shouldUnsubscribe=a,o._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,o._error=n?function(e){try{n(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,o._complete=i?function(){try{i()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,o}return t(r,e),r.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),!r&&(null===(t=this.onFinalize)||void 0===t||t.call(this))}},r}(I),$=u((function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}})),K=function(e){function r(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return t(r,e),r.prototype.lift=function(e){var t=new V(this,this);return t.operator=e,t},r.prototype._throwIfClosed=function(){if(this.closed)throw new $},r.prototype.next=function(e){var t=this;E((function(){var r,i;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var s=n(t.currentObservers),a=s.next();!a.done;a=s.next()){a.value.next(e)}}catch(e){r={error:e}}finally{try{a&&!a.done&&(i=s.return)&&i.call(s)}finally{if(r)throw r.error}}}}))},r.prototype.error=function(e){var t=this;E((function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var r=t.observers;r.length;)r.shift().error(e)}}))},r.prototype.complete=function(){var e=this;E((function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}}))},r.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(r.prototype,"observed",{get:function(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0},enumerable:!1,configurable:!0}),r.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},r.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},r.prototype._innerSubscribe=function(e){var t=this,r=this,i=r.hasError,n=r.isStopped,s=r.observers;return i||n?m:(this.currentObservers=null,s.push(e),new f((function(){t.currentObservers=null,p(s,e)})))},r.prototype._checkFinalizedStatuses=function(e){var t=this,r=t.hasError,i=t.thrownError,n=t.isStopped;r?e.error(i):n&&e.complete()},r.prototype.asObservable=function(){var e=new N;return e.source=this,e},r.create=function(e,t){return new V(e,t)},r}(N),V=function(e){function r(t,r){var i=e.call(this)||this;return i.destination=t,i.source=r,i}return t(r,e),r.prototype.next=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===r||r.call(t,e)},r.prototype.error=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===r||r.call(t,e)},r.prototype.complete=function(){var e,t;null===(t=null===(e=this.destination)||void 0===e?void 0:e.complete)||void 0===t||t.call(e)},r.prototype._subscribe=function(e){var t,r;return null!==(r=null===(t=this.source)||void 0===t?void 0:t.subscribe(e))&&void 0!==r?r:m},r}(K),W={now:function(){return(W.delegate||Date).now()},delegate:void 0},z=function(e){function r(t,r,i){void 0===t&&(t=1/0),void 0===r&&(r=1/0),void 0===i&&(i=W);var n=e.call(this)||this;return n._bufferSize=t,n._windowTime=r,n._timestampProvider=i,n._buffer=[],n._infiniteTimeWindow=!0,n._infiniteTimeWindow=r===1/0,n._bufferSize=Math.max(1,t),n._windowTime=Math.max(1,r),n}return t(r,e),r.prototype.next=function(t){var r=this,i=r.isStopped,n=r._buffer,s=r._infiniteTimeWindow,a=r._timestampProvider,o=r._windowTime;i||(n.push(t),!s&&n.push(a.now()+o)),this._trimBuffer(),e.prototype.next.call(this,t)},r.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),r=this._infiniteTimeWindow,i=this._buffer.slice(),n=0;n<i.length&&!e.closed;n+=r?1:2)e.next(i[n]);return this._checkFinalizedStatuses(e),t},r.prototype._trimBuffer=function(){var e=this,t=e._bufferSize,r=e._timestampProvider,i=e._buffer,n=e._infiniteTimeWindow,s=(n?1:2)*t;if(t<1/0&&s<i.length&&i.splice(0,i.length-s),!n){for(var a=r.now(),o=0,c=1;c<i.length&&i[c]<=a;c+=2)o=c;o&&i.splice(0,o+1)}},r}(K),U=function(e){function r(t,r){return e.call(this)||this}return t(r,e),r.prototype.schedule=function(e,t){return void 0===t&&(t=0),this},r}(f),G={setInterval:function(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];var n=G.delegate;return(null==n?void 0:n.setInterval)?n.setInterval.apply(n,a([e,t],s(r))):setInterval.apply(void 0,a([e,t],s(r)))},clearInterval:function(e){var t=G.delegate;return((null==t?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0},Q=function(e){function r(t,r){var i=e.call(this,t,r)||this;return i.scheduler=t,i.work=r,i.pending=!1,i}return t(r,e),r.prototype.schedule=function(e,t){var r;if(void 0===t&&(t=0),this.closed)return this;this.state=e;var i=this.id,n=this.scheduler;return null!=i&&(this.id=this.recycleAsyncId(n,i,t)),this.pending=!0,this.delay=t,this.id=null!==(r=this.id)&&void 0!==r?r:this.requestAsyncId(n,this.id,t),this},r.prototype.requestAsyncId=function(e,t,r){return void 0===r&&(r=0),G.setInterval(e.flush.bind(e,this),r)},r.prototype.recycleAsyncId=function(e,t,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return t;null!=t&&G.clearInterval(t)},r.prototype.execute=function(e,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var r=this._execute(e,t);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},r.prototype._execute=function(e,t){var r,i=!1;try{this.work(e)}catch(e){i=!0,r=e||new Error("Scheduled action threw falsy error")}if(i)return this.unsubscribe(),r},r.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,r=this.scheduler,i=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,p(i,this),null!=t&&(this.id=this.recycleAsyncId(r,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},r}(U),J=function(){function e(t,r){void 0===r&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(e,t,r){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(r,t)},e.now=W.now,e}(),X=new(function(e){function r(t,r){void 0===r&&(r=J.now);var i=e.call(this,t,r)||this;return i.actions=[],i._active=!1,i}return t(r,e),r.prototype.flush=function(e){var t=this.actions;if(this._active)t.push(e);else{var r;this._active=!0;do{if(r=e.execute(e.state,e.delay))break}while(e=t.shift());if(this._active=!1,r){for(;e=t.shift();)e.unsubscribe();throw r}}},r}(J))(Q),Y=X,Z=new N((function(e){return e.complete()}));function ee(e){return e&&d(e.schedule)}function te(e){return e[e.length-1]}function re(e){return ee(te(e))?e.pop():void 0}var ie=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e};function ne(e){return d(null==e?void 0:e.then)}function se(e){return d(e[A])}function ae(e){return Symbol.asyncIterator&&d(null==e?void 0:e[Symbol.asyncIterator])}function oe(e){return new TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var ce="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function le(e){return d(null==e?void 0:e[ce])}function de(e){return c(this,arguments,(function(){var t,r,n;return i(this,(function(i){switch(i.label){case 0:t=e.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,o(t.read())];case 3:return r=i.sent(),n=r.value,r.done?[4,o(void 0)]:[3,5];case 4:return[2,i.sent()];case 5:return[4,o(n)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}}))}))}function ue(e){return d(null==e?void 0:e.getReader)}function he(e){if(e instanceof N)return e;if(null!=e){if(se(e))return s=e,new N((function(e){var t=s[A]();if(d(t.subscribe))return t.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")}));if(ie(e))return i=e,new N((function(e){for(var t=0;t<i.length&&!e.closed;t++)e.next(i[t]);e.complete()}));if(ne(e))return r=e,new N((function(e){r.then((function(t){e.closed||(e.next(t),e.complete())}),(function(t){return e.error(t)})).then(null,b)}));if(ae(e))return pe(e);if(le(e))return t=e,new N((function(e){var r,i;try{for(var s=n(t),a=s.next();!a.done;a=s.next()){var o=a.value;if(e.next(o),e.closed)return}}catch(e){r={error:e}}finally{try{a&&!a.done&&(i=s.return)&&i.call(s)}finally{if(r)throw r.error}}e.complete()}));if(ue(e))return pe(de(e))}var t,r,i,s;throw oe(e)}function pe(e){return new N((function(t){(function(e,t){var n,s,a,o;return r(this,void 0,void 0,(function(){var r,c;return i(this,(function(i){switch(i.label){case 0:i.trys.push([0,5,6,11]),n=l(e),i.label=1;case 1:return[4,n.next()];case 2:if((s=i.sent()).done)return[3,4];if(r=s.value,t.next(r),t.closed)return[2];i.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return c=i.sent(),a={error:c},[3,11];case 6:return i.trys.push([6,,9,10]),s&&!s.done&&(o=n.return)?[4,o.call(n)]:[3,8];case 7:i.sent(),i.label=8;case 8:return[3,10];case 9:if(a)throw a.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}}))}))})(e,t).catch((function(e){return t.error(e)}))}))}function fe(e,t,r,i,n){void 0===i&&(i=0),void 0===n&&(n=!1);var s=t.schedule((function(){r(),n?e.add(this.schedule(null,i)):this.unsubscribe()}),i);if(e.add(s),!n)return s}function me(e,t){return void 0===t&&(t=0),L((function(r,i){r.subscribe(j(i,(function(r){return fe(i,e,(function(){return i.next(r)}),t)}),(function(){return fe(i,e,(function(){return i.complete()}),t)}),(function(r){return fe(i,e,(function(){return i.error(r)}),t)})))}))}function ye(e,t){return void 0===t&&(t=0),L((function(r,i){i.add(e.schedule((function(){return r.subscribe(i)}),t))}))}function ge(e,t){if(!e)throw new Error("Iterable cannot be null");return new N((function(r){fe(r,t,(function(){var i=e[Symbol.asyncIterator]();fe(r,t,(function(){i.next().then((function(e){e.done?r.complete():r.next(e.value)}))}),0,!0)}))}))}function we(e,t){if(null!=e){if(se(e))return function(e,t){return he(e).pipe(ye(t),me(t))}(e,t);if(ie(e))return function(e,t){return new N((function(r){var i=0;return t.schedule((function(){i===e.length?r.complete():(r.next(e[i++]),r.closed||this.schedule())}))}))}(e,t);if(ne(e))return function(e,t){return he(e).pipe(ye(t),me(t))}(e,t);if(ae(e))return ge(e,t);if(le(e))return function(e,t){return new N((function(r){var i;return fe(r,t,(function(){i=e[ce](),fe(r,t,(function(){var e,t,n;try{t=(e=i.next()).value,n=e.done}catch(e){return void r.error(e)}n?r.complete():r.next(t)}),0,!0)})),function(){return d(null==i?void 0:i.return)&&i.return()}}))}(e,t);if(ue(e))return function(e,t){return ge(de(e),t)}(e,t)}throw oe(e)}function ve(e,t){return t?we(e,t):he(e)}function be(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return ve(e,re(e))}var ke=u((function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}}));function Se(e,t){var r="object"==typeof t;return new Promise((function(i,n){var s=new D({next:function(e){i(e),s.unsubscribe()},error:n,complete:function(){r?i(t.defaultValue):n(new ke)}});e.subscribe(s)}))}function Ce(e,t){return L((function(r,i){var n=0;r.subscribe(j(i,(function(r){i.next(e.call(t,r,n++))})))}))}var Te=Array.isArray;function Ee(e){return Ce((function(t){return function(e,t){return Te(t)?e.apply(void 0,a([],s(t))):e(t)}(e,t)}))}Array.isArray,Object.getPrototypeOf,Object.prototype,Object.keys;function Ie(e,t,r,i,n,s,a,o){var c=[],l=0,d=0,u=!1,h=function(){!u||c.length||l||t.complete()},p=function(e){return l<i?f(e):c.push(e)},f=function(e){s&&t.next(e),l++;var o=!1;he(r(e,d++)).subscribe(j(t,(function(e){null==n||n(e),s?p(e):t.next(e)}),(function(){o=!0}),void 0,(function(){if(o)try{l--;for(var e=function(){var e=c.shift();a?fe(t,a,(function(){return f(e)})):f(e)};c.length&&l<i;)e();h()}catch(e){t.error(e)}})))};return e.subscribe(j(t,p,(function(){u=!0,h()}))),function(){null==o||o()}}function xe(e,t,r){return void 0===r&&(r=1/0),d(t)?xe((function(r,i){return Ce((function(e,n){return t(r,e,i,n)}))(he(e(r,i)))}),r):("number"==typeof t&&(r=t),L((function(t,i){return Ie(t,i,e,r)})))}function Fe(e){return void 0===e&&(e=1/0),xe(O,e)}function _e(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Fe(1)(ve(e,re(e)))}function De(e){return new N((function(t){he(e()).subscribe(t)}))}var Pe=["addListener","removeListener"],Re=["addEventListener","removeEventListener"],Me=["on","off"];function Ae(e,t,r,i){if(d(r)&&(i=r,r=void 0),i)return Ae(e,t,r).pipe(Ee(i));var n=s(function(e){return d(e.addEventListener)&&d(e.removeEventListener)}(e)?Re.map((function(i){return function(n){return e[i](t,n,r)}})):function(e){return d(e.addListener)&&d(e.removeListener)}(e)?Pe.map(Oe(e,t)):function(e){return d(e.on)&&d(e.off)}(e)?Me.map(Oe(e,t)):[],2),a=n[0],o=n[1];if(!a&&ie(e))return xe((function(e){return Ae(e,t,r)}))(he(e));if(!a)throw new TypeError("Invalid event target");return new N((function(e){var t=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.next(1<t.length?t:t[0])};return a(t),function(){return o(t)}}))}function Oe(e,t){return function(r){return function(i){return e[r](t,i)}}}function qe(e,t,r){void 0===e&&(e=0),void 0===r&&(r=Y);var i=-1;return null!=t&&(ee(t)?r=t:i=t),new N((function(t){var n,s=(n=e)instanceof Date&&!isNaN(n)?+e-r.now():e;s<0&&(s=0);var a=0;return r.schedule((function(){t.closed||(t.next(a++),0<=i?this.schedule(void 0,i):t.complete())}),s)}))}function Ne(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=re(e),i=function(e,t){return"number"==typeof te(e)?e.pop():t}(e,1/0),n=e;return n.length?1===n.length?he(n[0]):Fe(i)(ve(n,r)):Z}var Be=new N(k),Le=Array.isArray;function je(e){return 1===e.length&&Le(e[0])?e[0]:e}function He(e,t){return L((function(r,i){var n=0;r.subscribe(j(i,(function(r){return e.call(t,r,n++)&&i.next(r)})))}))}function $e(e){return function(t){for(var r=[],i=function(i){r.push(he(e[i]).subscribe(j(t,(function(e){if(r){for(var n=0;n<r.length;n++)n!==i&&r[n].unsubscribe();r=null}t.next(e)}))))},n=0;r&&!t.closed&&n<e.length;n++)i(n)}}function Ke(e){return L((function(t,r){var i,n=null,s=!1;n=t.subscribe(j(r,void 0,void 0,(function(a){i=he(e(a,Ke(e)(t))),n?(n.unsubscribe(),n=null,i.subscribe(r)):s=!0}))),s&&(n.unsubscribe(),n=null,i.subscribe(r))}))}function Ve(e){return L((function(t,r){var i=!1;t.subscribe(j(r,(function(e){i=!0,r.next(e)}),(function(){i||r.next(e),r.complete()})))}))}function We(e){return e<=0?function(){return Z}:L((function(t,r){var i=0;t.subscribe(j(r,(function(t){++i<=e&&(r.next(t),e<=i&&r.complete())})))}))}function ze(){return L((function(e,t){e.subscribe(j(t,k))}))}function Ue(e){return void 0===e&&(e=Ge),L((function(t,r){var i=!1;t.subscribe(j(r,(function(e){i=!0,r.next(e)}),(function(){return i?r.complete():r.error(e())})))}))}function Ge(){return new ke}function Qe(e,t){var r=arguments.length>=2;return function(i){return i.pipe(e?He((function(t,r){return e(t,r,i)})):O,We(1),r?Ve(t):Ue((function(){return new ke})))}}function Je(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length?L((function(t,r){$e(a([t],s(e)))(r)})):O}function Xe(e){var t;void 0===e&&(e=1/0);var r=(t=e&&"object"==typeof e?e:{count:e}).count,i=void 0===r?1/0:r,n=t.delay,s=t.resetOnSuccess,a=void 0!==s&&s;return i<=0?O:L((function(e,t){var r,s=0,o=function(){var c=!1;r=e.subscribe(j(t,(function(e){a&&(s=0),t.next(e)}),void 0,(function(e){if(s++<i){var a=function(){r?(r.unsubscribe(),r=null,o()):c=!0};if(null!=n){var l="number"==typeof n?qe(n):he(n(e,s)),d=j(t,(function(){d.unsubscribe(),a()}),(function(){t.complete()}));l.subscribe(d)}else a()}else t.error(e)}))),c&&(r.unsubscribe(),r=null,o())};o()}))}function Ye(e,t,r){var i=d(e)||t||r?{next:e,error:t,complete:r}:e;return i?L((function(e,t){var r;null===(r=i.subscribe)||void 0===r||r.call(i);var n=!0;e.subscribe(j(t,(function(e){var r;null===(r=i.next)||void 0===r||r.call(i,e),t.next(e)}),(function(){var e;n=!1,null===(e=i.complete)||void 0===e||e.call(i),t.complete()}),(function(e){var r;n=!1,null===(r=i.error)||void 0===r||r.call(i,e),t.error(e)}),(function(){var e,t;n&&(null===(e=i.unsubscribe)||void 0===e||e.call(i)),null===(t=i.finalize)||void 0===t||t.call(i)})))})):O}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Symbol.dispose??=Symbol("dispose"),Symbol.asyncDispose??=Symbol("asyncDispose");const Ze=Symbol.dispose,et=Symbol.asyncDispose;class tt{#e=!1;#t=[];get disposed(){return this.#e}dispose(){if(!this.#e){this.#e=!0;for(const e of this.#t.reverse())e[Ze]()}}use(e){return e&&this.#t.push(e),e}adopt(e,t){return this.#t.push({[Ze](){t(e)}}),e}defer(e){this.#t.push({[Ze](){e()}})}move(){if(this.#e)throw new ReferenceError("a disposed stack can not use anything new");const e=new tt;return e.#t=this.#t,this.#e=!0,e}[Ze]=this.dispose;[Symbol.toStringTag]="DisposableStack"}class rt{#e=!1;#t=[];get disposed(){return this.#e}async dispose(){if(!this.#e){this.#e=!0;for(const e of this.#t.reverse())await e[et]()}}use(e){return e&&this.#t.push(e),e}adopt(e,t){return this.#t.push({[et]:()=>t(e)}),e}defer(e){this.#t.push({[et]:()=>e()})}move(){if(this.#e)throw new ReferenceError("a disposed stack can not use anything new");const e=new rt;return e.#t=this.#t,this.#e=!0,e}[et]=this.dispose;[Symbol.toStringTag]="AsyncDisposableStack"}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class it{#r;#i=new Map;constructor(e=function(e){return{all:e=e||new Map,on:function(t,r){var i=e.get(t);i?i.push(r):e.set(t,[r])},off:function(t,r){var i=e.get(t);i&&(r?i.splice(i.indexOf(r)>>>0,1):e.set(t,[]))},emit:function(t,r){var i=e.get(t);i&&i.slice().map((function(e){e(r)})),(i=e.get("*"))&&i.slice().map((function(e){e(t,r)}))}}}(new Map)){this.#r=e}on(e,t){const r=this.#i.get(e);return void 0===r?this.#i.set(e,[t]):r.push(t),this.#r.on(e,t),this}off(e,t){const r=this.#i.get(e)??[];if(void 0===t){for(const t of r)this.#r.off(e,t);return this.#i.delete(e),this}const i=r.lastIndexOf(t);return i>-1&&this.#r.off(e,...r.splice(i,1)),this}emit(e,t){return this.#r.emit(e,t),this.listenerCount(e)>0}once(e,t){const r=i=>{t(i),this.off(e,r)};return this.on(e,r)}listenerCount(e){return this.#i.get(e)?.length||0}removeAllListeners(e){return void 0!==e?this.off(e):(this[Ze](),this)}[Ze](){for(const[e,t]of this.#i)for(const r of t)this.#r.off(e,r);this.#i.clear()}}const nt=(e,t)=>{if(!e)throw new Error(t)},st=!("undefined"==typeof process||!process.version);
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
let at=null;const ot=e=>st?async(...t)=>{lt&&ct.push(e+t),(await async function(){return at||(at=(await import("debug")).default),at}())(e)(t)}:(...t)=>{const r=globalThis.__PUPPETEER_DEBUG;if(!r)return;("*"===r||(r.endsWith("*")?e.startsWith(r):e===r))&&console.log(`${e}:`,...t)};let ct=[],lt=!1;
/**
 * @license
 * Copyright 2018 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class dt extends Error{constructor(e,t){super(e,t),this.name=this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}}class ut extends dt{}class ht extends dt{#n;#s="";set code(e){this.#n=e}get code(){return this.#n}set originalMessage(e){this.#s=e}get originalMessage(){return this.#s}}class pt extends dt{}class ft extends ht{}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const mt={letter:{width:8.5,height:11},legal:{width:8.5,height:14},tabloid:{width:11,height:17},ledger:{width:17,height:11},a0:{width:33.1,height:46.8},a1:{width:23.4,height:33.1},a2:{width:16.54,height:23.4},a3:{width:11.7,height:16.54},a4:{width:8.27,height:11.7},a5:{width:5.83,height:8.27},a6:{width:4.13,height:5.83}},yt=ot("puppeteer:error"),gt=(Object.freeze({width:800,height:600}),Symbol("Source URL for Puppeteer evaluation scripts"));
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class wt{static INTERNAL_URL="pptr:internal";static fromCallSite(e,t){const r=new wt;return r.#a=e,r.#o=t.toString(),r}static parse=e=>{e=e.slice(5);const[t="",r=""]=e.split(";"),i=new wt;return i.#a=t,i.#o=decodeURIComponent(r),i};static isPuppeteerURL=e=>e.startsWith("pptr:");#a;#o;get functionName(){return this.#a}get siteString(){return this.#o}toString(){return`pptr:${[this.#a,encodeURIComponent(this.#o)].join(";")}`}}const vt=(e,t)=>{if(Object.prototype.hasOwnProperty.call(t,gt))return t;const r=Error.prepareStackTrace;Error.prepareStackTrace=(e,t)=>t[2];const i=(new Error).stack;return Error.prepareStackTrace=r,Object.assign(t,{[gt]:wt.fromCallSite(e,i)})},bt=e=>"string"==typeof e||e instanceof String,kt=e=>"number"==typeof e||e instanceof Number;function St(e,...t){if(bt(e))return nt(0===t.length,"Cannot evaluate a string with arguments"),e;return`(${e})(${t.map((function(e){return Object.is(e,void 0)?"undefined":JSON.stringify(e)})).join(",")})`}let Ct=null;async function Tt(){if(!Ct)try{Ct=await import("fs/promises")}catch(e){if(e instanceof TypeError)throw new Error("Cannot write to a path outside of a Node-like environment.");throw e}return Ct}async function Et(e,t){const r=[],i=e.getReader();if(t){const e=await Tt(),n=await e.open(t,"w+");try{for(;;){const{done:e,value:t}=await i.read();if(e)break;r.push(t),await n.writeFile(t)}}finally{await n.close()}}else for(;;){const{done:e,value:t}=await i.read();if(e)break;r.push(t)}try{return Buffer.concat(r)}catch(e){return yt(e),null}}async function It(e,t){return new ReadableStream({async pull(r){const{data:i,base64Encoded:n,eof:s}=await e.send("IO.read",{handle:t});r.enqueue(function(e,t){return t?Uint8Array.from(atob(e),(e=>e.codePointAt(0))):(new TextEncoder).encode(e)}(i,n??!1)),s&&(await e.send("IO.close",{handle:t}),r.close())}})}function xt(e,t){return 0===e?Be:qe(e).pipe(Ce((()=>{throw new ut(`Timed out after waiting ${e}ms`,{cause:t})})))}const Ft="__puppeteer_utility_world__22.13.1",_t=/^[\040\t]*\/\/[@#] sourceURL=\s*(\S*?)\s*$/m;const Dt=500;const Pt={px:1,in:96,cm:37.8,mm:3.78};function Rt(e,t="in"){if(void 0===e)return;let r;if(kt(e))r=e;else{if(!bt(e))throw new Error("page.pdf() Cannot handle parameter type: "+typeof e);{const t=e;let i=t.substring(t.length-2).toLowerCase(),n="";i in Pt?n=t.substring(0,t.length-2):(i="px",n=t);const s=Number(n);nt(!isNaN(s),"Failed to parse parameter value: "+t),r=s*Pt[i]}}return r/Pt[t]}function Mt(e,t){return new N((r=>{const i=e=>{r.next(e)};return e.on(t,i),()=>{e.off(t,i)}}))}function At(e,t){return e?Ae(e,"abort").pipe(Ce((()=>{if(e.reason instanceof Error)throw e.reason.cause=t,e.reason;throw new Error(e.reason,{cause:t})}))):Be}function Ot(e){return xe((t=>ve(Promise.resolve(e(t))).pipe(He((e=>e)),Ce((()=>t)))))}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const qt=new Map([["geolocation","geolocation"],["midi","midi"],["notifications","notifications"],["camera","videoCapture"],["microphone","audioCapture"],["background-sync","backgroundSync"],["ambient-light-sensor","sensors"],["accelerometer","sensors"],["gyroscope","sensors"],["magnetometer","sensors"],["accessibility-events","accessibilityEvents"],["clipboard-read","clipboardReadWrite"],["clipboard-write","clipboardReadWrite"],["clipboard-sanitized-write","clipboardSanitizedWrite"],["payment-handler","paymentHandler"],["persistent-storage","durableStorage"],["idle-detection","idleDetection"],["midi-sysex","midiSysex"]]);class Nt extends it{constructor(){super()}async waitForTarget(e,t={}){const{timeout:r=3e4}=t;return await Se(Ne(Mt(this,"targetcreated"),Mt(this,"targetchanged"),ve(this.targets())).pipe(Ot(e),Je(xt(r))))}async pages(){const e=await Promise.all(this.browserContexts().map((e=>e.pages())));return e.reduce(((e,t)=>e.concat(t)),[])}isConnected(){return this.connected}[Ze](){this.process()?this.close().catch(yt):this.disconnect().catch(yt)}[et](){return this.process()?this.close():this.disconnect()}}var Bt;!function(e){e.Disconnected=Symbol("CDPSession.Disconnected"),e.Swapped=Symbol("CDPSession.Swapped"),e.Ready=Symbol("CDPSession.Ready"),e.SessionAttached="sessionattached",e.SessionDetached="sessiondetached"}(Bt||(Bt={}));class Lt extends it{constructor(){super()}parentSession(){}}
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class jt{static create(e){return new jt(e)}static async race(e){const t=new Set;try{const r=e.map((e=>e instanceof jt?(e.#c&&t.add(e),e.valueOrThrow()):e));return await Promise.race(r)}finally{for(const e of t)e.reject(new Error("Timeout cleared"))}}#l=!1;#d=!1;#u;#h;#p=new Promise((e=>{this.#h=e}));#c;#f;constructor(e){e&&e.timeout>0&&(this.#f=new ut(e.message),this.#c=setTimeout((()=>{this.reject(this.#f)}),e.timeout))}#m(e){clearTimeout(this.#c),this.#u=e,this.#h()}resolve(e){this.#d||this.#l||(this.#l=!0,this.#m(e))}reject(e){this.#d||this.#l||(this.#d=!0,this.#m(e))}resolved(){return this.#l}finished(){return this.#l||this.#d}value(){return this.#u}#y;valueOrThrow(){return this.#y||(this.#y=(async()=>{if(await this.#p,this.#d)throw this.#u;return this.#u})()),this.#y}}
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Ht{static Guard=class{#g;#w;constructor(e,t){this.#g=e,this.#w=t}[Ze](){return this.#w?.(),this.#g.release()}};#v=!1;#b=[];async acquire(e){if(!this.#v)return this.#v=!0,new Ht.Guard(this);const t=jt.create();return this.#b.push(t.resolve.bind(t)),await t.valueOrThrow(),new Ht.Guard(this,e)}release(){const e=this.#b.shift();e?e():this.#v=!1}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class $t extends it{constructor(){super()}#k;#S=0;startScreenshot(){const e=this.#k||new Ht;return this.#k=e,this.#S++,e.acquire((()=>{this.#S--,0===this.#S&&(this.#k=void 0)}))}waitForScreenshotOperations(){return this.#k?.acquire()}async waitForTarget(e,t={}){const{timeout:r=3e4}=t;return await Se(Ne(Mt(this,"targetcreated"),Mt(this,"targetchanged"),ve(this.targets())).pipe(Ot(e),Je(xt(r))))}get closed(){return!this.browser().browserContexts().includes(this)}get id(){}[Ze](){this.close().catch(yt)}[et](){return this.close()}}
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var Kt,Vt=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Wt=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});class zt extends $t{#C;#T;#E;constructor(e,t,r){super(),this.#C=e,this.#T=t,this.#E=r}get id(){return this.#E}targets(){return this.#T.targets().filter((e=>e.browserContext()===this))}async pages(){return(await Promise.all(this.targets().filter((e=>"page"===e.type()||"other"===e.type()&&this.#T._getIsPageTargetCallback()?.(e))).map((e=>e.page())))).filter((e=>!!e))}isIncognito(){return!!this.#E}async overridePermissions(e,t){const r=t.map((e=>{const t=qt.get(e);if(!t)throw new Error("Unknown permission: "+e);return t}));await this.#C.send("Browser.grantPermissions",{origin:e,browserContextId:this.#E||void 0,permissions:r})}async clearPermissionOverrides(){await this.#C.send("Browser.resetPermissions",{browserContextId:this.#E||void 0})}async newPage(){const e={stack:[],error:void 0,hasError:!1};try{Vt(e,await this.waitForScreenshotOperations(),!1);return await this.#T._createPageInContext(this.#E)}catch(t){e.error=t,e.hasError=!0}finally{Wt(e)}}browser(){return this.#T}async close(){nt(this.#E,"Non-incognito profiles cannot be closed!"),await this.#T._disposeContext(this.#E)}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */!function(e){e.PAGE="page",e.BACKGROUND_PAGE="background_page",e.SERVICE_WORKER="service_worker",e.SHARED_WORKER="shared_worker",e.BROWSER="browser",e.WEBVIEW="webview",e.OTHER="other",e.TAB="tab"}(Kt||(Kt={}));class Ut{constructor(){}async worker(){return null}async page(){return null}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */function Gt(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}function Qt(e,t,r){return e.message=t,e.originalMessage=r??e.originalMessage,e}function Jt(e){let t=e.error.message;return e.error&&"object"==typeof e.error&&"data"in e.error&&(t+=` ${e.error.data}`),t}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Xt{#I=new Map;#x=function(){let e=0;return()=>++e}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */();create(e,t,r){const i=new Yt(this.#x(),e,t);this.#I.set(i.id,i);try{r(i.id)}catch(e){throw i.promise.catch(yt).finally((()=>{this.#I.delete(i.id)})),i.reject(e),e}return i.promise.finally((()=>{this.#I.delete(i.id)}))}reject(e,t,r){const i=this.#I.get(e);i&&this._reject(i,t,r)}_reject(e,t,r){let i,n;t instanceof ht?(i=t,i.cause=e.error,n=t.message):(i=e.error,n=t),e.reject(Qt(i,`Protocol error (${e.label}): ${n}`,r))}resolve(e,t){const r=this.#I.get(e);r&&r.resolve(t)}clear(){for(const e of this.#I.values())this._reject(e,new ft("Target closed"));this.#I.clear()}getPendingProtocolErrors(){const e=[];for(const t of this.#I.values())e.push(new Error(`${t.label} timed out. Trace: ${t.error.stack}`));return e}}class Yt{#E;#F=new ht;#_=jt.create();#D;#P;constructor(e,t,r){this.#E=e,this.#P=t,r&&(this.#D=setTimeout((()=>{this.#_.reject(Qt(this.#F,`${t} timed out. Increase the 'protocolTimeout' setting in launch/connect calls for a higher timeout if needed.`))}),r))}resolve(e){clearTimeout(this.#D),this.#_.resolve(e)}reject(e){clearTimeout(this.#D),this.#_.reject(e)}get id(){return this.#E}get promise(){return this.#_.valueOrThrow()}get error(){return this.#F}get label(){return this.#P}}class Zt extends Lt{#R;#M;#I=new Xt;#C;#A;#O;constructor(e,t,r,i){super(),this.#C=e,this.#M=t,this.#R=r,this.#A=i}_setTarget(e){this.#O=e}_target(){return nt(this.#O,"Target must exist"),this.#O}connection(){return this.#C}parentSession(){if(!this.#A)return this;const e=this.#C?.session(this.#A);return e??void 0}send(e,t,r){return this.#C?this.#C._rawSend(this.#I,e,t,this.#R,r):Promise.reject(new ft(`Protocol error (${e}): Session closed. Most likely the ${this.#M} has been closed.`))}_onMessage(e){e.id?e.error?this.#I.reject(e.id,Jt(e),e.error.message):this.#I.resolve(e.id,e.result):(nt(!e.id),this.emit(e.method,e.params))}async detach(){if(!this.#C)throw new Error(`Session already detached. Most likely the ${this.#M} has been closed.`);await this.#C.send("Target.detachFromTarget",{sessionId:this.#R})}_onClosed(){this.#I.clear(),this.#C=void 0,this.emit(Bt.Disconnected,void 0)}id(){return this.#R}getPendingProtocolErrors(){return this.#I.getPendingProtocolErrors()}}
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class er{#q;#N;constructor(){this.#q=null,this.#N=null}setDefaultTimeout(e){this.#q=e}setDefaultNavigationTimeout(e){this.#N=e}navigationTimeout(){return null!==this.#N?this.#N:null!==this.#q?this.#q:3e4}timeout(){return null!==this.#q?this.#q:3e4}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var tr=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},rr=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});const ir=new WeakSet;function nr(e,t){let r=!1;if(e.prototype[Ze]){const t=e.prototype[Ze];e.prototype[Ze]=function(){if(!ir.has(this))return t.call(this);ir.delete(this)},r=!0}if(e.prototype[et]){const t=e.prototype[et];e.prototype[et]=function(){if(!ir.has(this))return t.call(this);ir.delete(this)},r=!0}return r&&(e.prototype.move=function(){return ir.add(this),this}),e}function sr(e=(e=>`Attempted to use disposed ${e.constructor.name}.`)){return(t,r)=>function(...r){if(this.disposed)throw new Error(e(this));return t.call(this,...r)}}function ar(e,t){const r=new WeakMap;let i=-1;return function(...t){if(-1===i&&(i=t.length),i!==t.length)throw new Error("Memoized method was called with the wrong number of arguments");let n=!1,s=r;for(const e of t)s.has(e)||(n=!0,s.set(e,new WeakMap)),s=s.get(e);if(n)return e.call(this,...t)}}function or(e=function(){return this}){return(t,r)=>{const i=new WeakMap;return async function(...r){const n={stack:[],error:void 0,hasError:!1};try{const s=e.call(this);let a=i.get(s);a||(a=new Ht,i.set(s,a));tr(n,await a.acquire(),!0);return await t.call(this,...r)}catch(e){n.error=e,n.hasError=!0}finally{const e=rr(n);e&&await e}}}}new WeakMap;var cr,lr=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},dr=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});!function(e){e.Action="action"}(cr||(cr={}));class ur extends it{static race(e){return gr.create(e)}visibility=null;_timeout=3e4;#B=!0;#L=!0;#j=!0;operators={conditions:(e,t)=>xe((r=>Ne(...e.map((e=>e(r,t)))).pipe(Ve(r)))),retryAndRaceWithSignalAndTimer:(e,t)=>{const r=[];return e&&r.push(At(e,t)),r.push(xt(this._timeout,t)),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return q(e)}(Xe({delay:wr}),Je(...r))}};get timeout(){return this._timeout}setTimeout(e){const t=this._clone();return t._timeout=e,t}setVisibility(e){const t=this._clone();return t.visibility=e,t}setWaitForEnabled(e){const t=this._clone();return t.#L=e,t}setEnsureElementIsInTheViewport(e){const t=this._clone();return t.#B=e,t}setWaitForStableBoundingBox(e){const t=this._clone();return t.#j=e,t}copyOptions(e){return this._timeout=e._timeout,this.visibility=e.visibility,this.#L=e.#L,this.#B=e.#B,this.#j=e.#j,this}#H=(e,t)=>this.#L?ve(e.frame.waitForFunction((e=>{if(!(e instanceof HTMLElement))return!0;return!["BUTTON","INPUT","SELECT","TEXTAREA","OPTION","OPTGROUP"].includes(e.nodeName)||!e.hasAttribute("disabled")}),{timeout:this._timeout,signal:t},e)).pipe(ze()):Z;#$=e=>this.#j?De((()=>ve(e.evaluate((e=>new Promise((t=>{window.requestAnimationFrame((()=>{const r=e.getBoundingClientRect();window.requestAnimationFrame((()=>{const i=e.getBoundingClientRect();t([{x:r.x,y:r.y,width:r.width,height:r.height},{x:i.x,y:i.y,width:i.width,height:i.height}])}))}))}))))))).pipe(Qe((([e,t])=>e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height)),Xe({delay:wr}),ze()):Z;#K=e=>this.#B?ve(e.isIntersectingViewport({threshold:0})).pipe(He((e=>!e)),xe((()=>ve(e.scrollIntoView()))),xe((()=>De((()=>ve(e.isIntersectingViewport({threshold:0})))).pipe(Qe(O),Xe({delay:wr}),ze())))):Z;#V(e){const t=e?.signal,r=new Error("Locator.click");return this._wait(e).pipe(this.operators.conditions([this.#K,this.#$,this.#H],t),Ye((()=>this.emit(cr.Action,void 0))),xe((t=>ve(t.click(e)).pipe(Ke((e=>{throw t.dispose().catch(yt),e}))))),this.operators.retryAndRaceWithSignalAndTimer(t,r))}#W(e,t){const r=t?.signal,i=new Error("Locator.fill");return this._wait(t).pipe(this.operators.conditions([this.#K,this.#$,this.#H],r),Ye((()=>this.emit(cr.Action,void 0))),xe((t=>ve(t.evaluate((e=>e instanceof HTMLSelectElement?"select":e instanceof HTMLTextAreaElement?"typeable-input":e instanceof HTMLInputElement?new Set(["textarea","text","url","tel","search","password","number","email"]).has(e.type)?"typeable-input":"other-input":e.isContentEditable?"contenteditable":"unknown"))).pipe(xe((r=>{switch(r){case"select":return ve(t.select(e).then(k));case"contenteditable":case"typeable-input":return ve(t.evaluate(((e,t)=>{const r=e.isContentEditable?e.innerText:e.value;if(t.length<=r.length||!t.startsWith(e.value))return e.isContentEditable?e.innerText="":e.value="",t;const i=e.isContentEditable?e.innerText:e.value;return e.isContentEditable?(e.innerText="",e.innerText=i):(e.value="",e.value=i),t.substring(i.length)}),e)).pipe(xe((e=>ve(t.type(e)))));case"other-input":return ve(t.focus()).pipe(xe((()=>ve(t.evaluate(((e,t)=>{e.value=t,e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0}))}),e)))));case"unknown":throw new Error("Element cannot be filled out.")}}))).pipe(Ke((e=>{throw t.dispose().catch(yt),e}))))),this.operators.retryAndRaceWithSignalAndTimer(r,i))}#z(e){const t=e?.signal,r=new Error("Locator.hover");return this._wait(e).pipe(this.operators.conditions([this.#K,this.#$],t),Ye((()=>this.emit(cr.Action,void 0))),xe((e=>ve(e.hover()).pipe(Ke((t=>{throw e.dispose().catch(yt),t}))))),this.operators.retryAndRaceWithSignalAndTimer(t,r))}#U(e){const t=e?.signal,r=new Error("Locator.scroll");return this._wait(e).pipe(this.operators.conditions([this.#K,this.#$],t),Ye((()=>this.emit(cr.Action,void 0))),xe((t=>ve(t.evaluate(((e,t,r)=>{void 0!==t&&(e.scrollTop=t),void 0!==r&&(e.scrollLeft=r)}),e?.scrollTop,e?.scrollLeft)).pipe(Ke((e=>{throw t.dispose().catch(yt),e}))))),this.operators.retryAndRaceWithSignalAndTimer(t,r))}clone(){return this._clone()}async waitHandle(e){const t=new Error("Locator.waitHandle");return await Se(this._wait(e).pipe(this.operators.retryAndRaceWithSignalAndTimer(e?.signal,t)))}async wait(e){const t={stack:[],error:void 0,hasError:!1};try{const r=lr(t,await this.waitHandle(e),!1);return await r.jsonValue()}catch(e){t.error=e,t.hasError=!0}finally{dr(t)}}map(e){return new mr(this._clone(),(t=>t.evaluateHandle(e)))}filter(e){return new fr(this._clone(),(async(t,r)=>(await t.frame.waitForFunction(e,{signal:r,timeout:this._timeout},t),!0)))}filterHandle(e){return new fr(this._clone(),e)}mapHandle(e){return new mr(this._clone(),e)}click(e){return Se(this.#V(e))}fill(e,t){return Se(this.#W(e,t))}hover(e){return Se(this.#z(e))}scroll(e){return Se(this.#U(e))}}class hr extends ur{static create(e,t){return new hr(e,t).setTimeout("getDefaultTimeout"in e?e.getDefaultTimeout():e.page().getDefaultTimeout())}#G;#Q;constructor(e,t){super(),this.#G=e,this.#Q=t}_clone(){return new hr(this.#G,this.#Q)}_wait(e){const t=e?.signal;return De((()=>ve(this.#G.waitForFunction(this.#Q,{timeout:this.timeout,signal:t})))).pipe(Ue())}}class pr extends ur{#J;constructor(e){super(),this.#J=e,this.copyOptions(this.#J)}get delegate(){return this.#J}setTimeout(e){const t=super.setTimeout(e);return t.#J=this.#J.setTimeout(e),t}setVisibility(e){const t=super.setVisibility(e);return t.#J=t.#J.setVisibility(e),t}setWaitForEnabled(e){const t=super.setWaitForEnabled(e);return t.#J=this.#J.setWaitForEnabled(e),t}setEnsureElementIsInTheViewport(e){const t=super.setEnsureElementIsInTheViewport(e);return t.#J=this.#J.setEnsureElementIsInTheViewport(e),t}setWaitForStableBoundingBox(e){const t=super.setWaitForStableBoundingBox(e);return t.#J=this.#J.setWaitForStableBoundingBox(e),t}}class fr extends pr{#X;constructor(e,t){super(e),this.#X=t}_clone(){return new fr(this.delegate.clone(),this.#X).copyOptions(this)}_wait(e){return this.delegate._wait(e).pipe(xe((t=>ve(Promise.resolve(this.#X(t,e?.signal))).pipe(He((e=>e)),Ce((()=>t))))),Ue())}}class mr extends pr{#Y;constructor(e,t){super(e),this.#Y=t}_clone(){return new mr(this.delegate.clone(),this.#Y).copyOptions(this)}_wait(e){return this.delegate._wait(e).pipe(xe((t=>ve(Promise.resolve(this.#Y(t,e?.signal))))))}}class yr extends ur{static create(e,t){return new yr(e,t).setTimeout("getDefaultTimeout"in e?e.getDefaultTimeout():e.page().getDefaultTimeout())}#G;#Z;constructor(e,t){super(),this.#G=e,this.#Z=t}#ee=e=>this.visibility?(()=>{switch(this.visibility){case"hidden":return De((()=>ve(e.isHidden())));case"visible":return De((()=>ve(e.isVisible())))}})().pipe(Qe(O),Xe({delay:wr}),ze()):Z;_clone(){return new yr(this.#G,this.#Z).copyOptions(this)}_wait(e){const t=e?.signal;return De((()=>ve(this.#G.waitForSelector(this.#Z,{visible:!1,timeout:this._timeout,signal:t})))).pipe(He((e=>null!==e)),Ue(),this.operators.conditions([this.#ee],t))}}class gr extends ur{static create(e){const t=function(e){for(const t of e)if(!(t instanceof ur))throw new Error("Unknown locator for race candidate");return e}(e);return new gr(t)}#te;constructor(e){super(),this.#te=e}_clone(){return new gr(this.#te.map((e=>e.clone()))).copyOptions(this)}_wait(e){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 1===(e=je(e)).length?he(e[0]):new N($e(e))}(...this.#te.map((t=>t._wait(e))))}}const wr=100;
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var vr=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},br=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var m in i)f[m]="access"===m?{}:i[m];for(var m in i.access)f.access[m]=i.access[m];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var y=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===y)continue;if(null===y||"object"!=typeof y)throw new TypeError("Object expected");(o=a(y.get))&&(u.get=o),(o=a(y.set))&&(u.set=o),(o=a(y.init))&&n.unshift(o)}else(o=a(y))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0},kr=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Sr=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});let Cr=(()=>{let e,t=it,r=[];return class extends t{static{const i="function"==typeof Symbol&&Symbol.metadata?Object.create(t[Symbol.metadata]??null):void 0;br(this,null,e,{kind:"method",name:"screenshot",static:!1,private:!1,access:{has:e=>"screenshot"in e,get:e=>e.screenshot},metadata:i},null,r),i&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:i})}_isDragging=(vr(this,r),!1);_timeoutSettings=new er;#re=new WeakMap;#ie=new z(1);constructor(){var e,t,r,i;super(),Mt(this,"request").pipe(xe((e=>_e(be(1),Ne(Mt(this,"requestfailed"),Mt(this,"requestfinished"),Mt(this,"response").pipe(Ce((e=>e.request())))).pipe(He((t=>t.id===e.id)),We(1),Ce((()=>-1)))))),(t=(e,t)=>be(e+t),r=0,void 0===i&&(i=1/0),L((function(e,n){var s=r;return Ie(e,n,(function(e,r){return t(s,e,r)}),i,(function(e){s=e}),!1,void 0,(function(){return s=null}))}))),(e=Mt(this,"close"),L((function(t,r){he(e).subscribe(j(r,(function(){return r.complete()}),k)),!r.closed&&t.subscribe(r)}))),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=re(e);return L((function(t,i){(r?_e(e,t,r):_e(e,t)).subscribe(i)}))}(0)).subscribe(this.#ie)}on(e,t){if("request"!==e)return super.on(e,t);let r=this.#re.get(t);return void 0===r&&(r=e=>{e.enqueueInterceptAction((()=>t(e)))},this.#re.set(t,r)),super.on(e,r)}off(e,t){return"request"===e&&(t=this.#re.get(t)||t),super.off(e,t)}get accessibility(){return this.mainFrame().accessibility}locator(e){return"string"==typeof e?yr.create(this,e):hr.create(this,e)}locatorRace(e){return ur.race(e)}async $(e){return await this.mainFrame().$(e)}async $$(e,t){return await this.mainFrame().$$(e,t)}async evaluateHandle(e,...t){return e=vt(this.evaluateHandle.name,e),await this.mainFrame().evaluateHandle(e,...t)}async $eval(e,t,...r){return t=vt(this.$eval.name,t),await this.mainFrame().$eval(e,t,...r)}async $$eval(e,t,...r){return t=vt(this.$$eval.name,t),await this.mainFrame().$$eval(e,t,...r)}async addScriptTag(e){return await this.mainFrame().addScriptTag(e)}async addStyleTag(e){return await this.mainFrame().addStyleTag(e)}url(){return this.mainFrame().url()}async content(){return await this.mainFrame().content()}async setContent(e,t){await this.mainFrame().setContent(e,t)}async goto(e,t){return await this.mainFrame().goto(e,t)}async waitForNavigation(e={}){return await this.mainFrame().waitForNavigation(e)}waitForRequest(e,t={}){const{timeout:r=this._timeoutSettings.timeout(),signal:i}=t;if("string"==typeof e){const t=e;e=e=>e.url()===t}return Se(Mt(this,"request").pipe(Ot(e),Je(xt(r),At(i),Mt(this,"close").pipe(Ce((()=>{throw new ft("Page closed!")}))))))}waitForResponse(e,t={}){const{timeout:r=this._timeoutSettings.timeout(),signal:i}=t;if("string"==typeof e){const t=e;e=e=>e.url()===t}return Se(Mt(this,"response").pipe(Ot(e),Je(xt(r),At(i),Mt(this,"close").pipe(Ce((()=>{throw new ft("Page closed!")}))))))}waitForNetworkIdle(e={}){return Se(this.waitForNetworkIdle$(e))}waitForNetworkIdle$(e={}){const{timeout:t=this._timeoutSettings.timeout(),idleTime:r=Dt,concurrency:i=0,signal:n}=e;return this.#ie.pipe((s=e=>e>i?Z:qe(r),L((function(e,t){var r=null,i=0,n=!1,o=function(){return n&&!r&&t.complete()};e.subscribe(j(t,(function(e){null==r||r.unsubscribe();var n=0,c=i++;he(s(e,c)).subscribe(r=j(t,(function(r){return t.next(a?a(e,r,c,n++):r)}),(function(){r=null,o()})))}),(function(){n=!0,o()})))}))),Ce((()=>{})),Je(xt(t),At(n),Mt(this,"close").pipe(Ce((()=>{throw new ft("Page closed!")})))));var s,a}async waitForFrame(e,t={}){const{timeout:r=this.getDefaultTimeout(),signal:i}=t;return bt(e)&&(e=t=>e===t.url()),await Se(Ne(Mt(this,"frameattached"),Mt(this,"framenavigated"),ve(this.frames())).pipe(Ot(e),Qe(),Je(xt(r),At(i),Mt(this,"close").pipe(Ce((()=>{throw new ft("Page closed.")}))))))}async emulate(e){await Promise.all([this.setUserAgent(e.userAgent),this.setViewport(e.viewport)])}async evaluate(e,...t){return e=vt(this.evaluate.name,e),await this.mainFrame().evaluate(e,...t)}async _maybeWriteBufferToFile(e,t){if(!e)return;const r=await Tt();await r.writeFile(e,t)}async screencast(e={}){const[{ScreenRecorder:t},[r,i,n]]=await Promise.all([import("./package/lib/esm/puppeteer/node/ScreenRecorder.js"),this.#ne()]);let s;if(e.crop){const{x:t,y:a,width:o,height:c}=Er(Tr(e.crop));if(t<0||a<0)throw new Error("`crop.x` and `crop.y` must be greater than or equal to 0.");if(o<=0||c<=0)throw new Error("`crop.height` and `crop.width` must be greater than or equal to 0.");const l=r/n,d=i/n;if(t+o>l)throw new Error(`\`crop.width\` cannot be larger than the viewport width (${l}).`);if(a+c>d)throw new Error(`\`crop.height\` cannot be larger than the viewport height (${d}).`);s={x:t*n,y:a*n,width:o*n,height:c*n}}if(void 0!==e.speed&&e.speed<=0)throw new Error("`speed` must be greater than 0.");if(void 0!==e.scale&&e.scale<=0)throw new Error("`scale` must be greater than 0.");const a=new t(this,r,i,{...e,path:e.ffmpegPath,crop:s});try{await this._startScreencast()}catch(e){throw a.stop(),e}if(e.path){const{createWriteStream:t}=await import("fs"),r=t(e.path,"binary");a.pipe(r)}return a}#se=0;#ae;async _startScreencast(){++this.#se,this.#ae||(this.#ae=this.mainFrame().client.send("Page.startScreencast",{format:"png"}).then((()=>new Promise((e=>this.mainFrame().client.once("Page.screencastFrame",(()=>e()))))))),await this.#ae}async _stopScreencast(){--this.#se,this.#ae&&(this.#ae=void 0,0===this.#se&&await this.mainFrame().client.send("Page.stopScreencast"))}async#ne(){const e={stack:[],error:void 0,hasError:!1};try{const t=this.viewport(),r=kr(e,new tt,!1);return t&&0!==t.deviceScaleFactor&&(await this.setViewport({...t,deviceScaleFactor:0}),r.defer((()=>{this.setViewport(t).catch(yt)}))),await this.mainFrame().isolatedRealm().evaluate((()=>[window.visualViewport.width*window.devicePixelRatio,window.visualViewport.height*window.devicePixelRatio,window.devicePixelRatio]))}catch(t){e.error=t,e.hasError=!0}finally{Sr(e)}}async screenshot(e={}){const t={stack:[],error:void 0,hasError:!1};try{kr(t,await this.browserContext().startScreenshot(),!1);await this.bringToFront();const r={...e,clip:e.clip?{...e.clip}:void 0};if(void 0===r.type&&void 0!==r.path){const e=r.path;switch(e.slice(e.lastIndexOf(".")+1).toLowerCase()){case"png":r.type="png";break;case"jpeg":case"jpg":r.type="jpeg";break;case"webp":r.type="webp"}}if(void 0!==r.quality){if(r.quality<0||r.quality>100)throw new Error(`Expected 'quality' (${r.quality}) to be between 0 and 100, inclusive.`);if(void 0===r.type||!["jpeg","webp"].includes(r.type))throw new Error(`${r.type??"png"} screenshots do not support 'quality'.`)}if(r.clip){if(r.clip.width<=0)throw new Error("'width' in 'clip' must be positive.");if(r.clip.height<=0)throw new Error("'height' in 'clip' must be positive.")}!function(e){e.optimizeForSpeed??=!1,e.type??="png",e.fromSurface??=!0,e.fullPage??=!1,e.omitBackground??=!1,e.encoding??="binary",e.captureBeyondViewport??=!0}(r);const i=kr(t,new rt,!0);if(r.clip){if(r.fullPage)throw new Error("'clip' and 'fullPage' are mutually exclusive");r.clip=Er(Tr(r.clip))}else if(r.fullPage){if(!r.captureBeyondViewport){const e=await this.mainFrame().isolatedRealm().evaluate((()=>{const e=document.documentElement;return{width:e.scrollWidth,height:e.scrollHeight}})),t=this.viewport();await this.setViewport({...t,...e}),i.defer((async()=>{await this.setViewport(t).catch(yt)}))}}else r.captureBeyondViewport=!1;const n=await this._screenshot(r);if("base64"===r.encoding)return n;const s=Buffer.from(n,"base64");return await this._maybeWriteBufferToFile(r.path,s),s}catch(e){t.error=e,t.hasError=!0}finally{const e=Sr(t);e&&await e}}async title(){return await this.mainFrame().title()}click(e,t){return this.mainFrame().click(e,t)}focus(e){return this.mainFrame().focus(e)}hover(e){return this.mainFrame().hover(e)}select(e,...t){return this.mainFrame().select(e,...t)}tap(e){return this.mainFrame().tap(e)}type(e,t,r){return this.mainFrame().type(e,t,r)}async waitForSelector(e,t={}){return await this.mainFrame().waitForSelector(e,t)}waitForFunction(e,t,...r){return this.mainFrame().waitForFunction(e,t,...r)}[(e=[or((function(){return this.browser()}))],Ze)](){this.close().catch(yt)}[et](){return this.close()}}})();new Set(["Timestamp","Documents","Frames","JSEventListeners","Nodes","LayoutCount","RecalcStyleCount","LayoutDuration","RecalcStyleDuration","ScriptDuration","TaskDuration","JSHeapUsedSize","JSHeapTotalSize"]);function Tr(e){return{...e,...e.width<0?{x:e.x+e.width,width:-e.width}:{x:e.x,width:e.width},...e.height<0?{y:e.y+e.height,height:-e.height}:{y:e.y,height:e.height}}}function Er(e){const t=Math.round(e.x),r=Math.round(e.y),i=Math.round(e.width+e.x-t),n=Math.round(e.height+e.y-r);return{...e,x:t,y:r,width:i,height:n}}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Ir{#oe;#ce;#le;#de;constructor(e,t,r,i){this.#oe=e,this.#ce=t,this.#le=r,this.#de=i}type(){return this.#oe}text(){return this.#ce}args(){return this.#le}location(){return this.#de[0]??{}}stackTrace(){return this.#de}}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class xr{#ue;#he;#pe=!1;constructor(e,t){this.#ue=e,this.#he="selectSingle"!==t.mode}isMultiple(){return this.#he}async accept(e){nt(!this.#pe,"Cannot accept FileChooser which is already handled!"),this.#pe=!0,await this.#ue.uploadFile(...e)}async cancel(){nt(!this.#pe,"Cannot cancel FileChooser which is already handled!"),this.#pe=!0,await this.#ue.evaluate((e=>{e.dispatchEvent(new Event("cancel",{bubbles:!0}))}))}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var Fr;!function(e){e.Request=Symbol("NetworkManager.Request"),e.RequestServedFromCache=Symbol("NetworkManager.RequestServedFromCache"),e.Response=Symbol("NetworkManager.Response"),e.RequestFailed=Symbol("NetworkManager.RequestFailed"),e.RequestFinished=Symbol("NetworkManager.RequestFinished")}(Fr||(Fr={}));
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var _r=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},Dr=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var m in i)f[m]="access"===m?{}:i[m];for(var m in i.access)f.access[m]=i.access[m];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var y=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===y)continue;if(null===y||"object"!=typeof y)throw new TypeError("Object expected");(o=a(y.get))&&(u.get=o),(o=a(y.set))&&(u.set=o),(o=a(y.init))&&n.unshift(o)}else(o=a(y))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0},Pr=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Rr=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});let Mr=(()=>{let e,t,r,i,n=[nr],s=[],a=[];(class{static{t=this}static{const o="function"==typeof Symbol&&Symbol.metadata?Object.create(null):void 0;Dr(this,null,r,{kind:"method",name:"getProperty",static:!1,private:!1,access:{has:e=>"getProperty"in e,get:e=>e.getProperty},metadata:o},null,a),Dr(this,null,i,{kind:"method",name:"getProperties",static:!1,private:!1,access:{has:e=>"getProperties"in e,get:e=>e.getProperties},metadata:o},null,a),Dr(null,e={value:t},n,{kind:"class",name:t.name,metadata:o},null,s),t=e.value,o&&Object.defineProperty(t,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:o}),_r(t,s)}constructor(){_r(this,a)}async evaluate(e,...t){return e=vt(this.evaluate.name,e),await this.realm.evaluate(e,this,...t)}async evaluateHandle(e,...t){return e=vt(this.evaluateHandle.name,e),await this.realm.evaluateHandle(e,this,...t)}async getProperty(e){return await this.evaluateHandle(((e,t)=>e[t]),e)}async getProperties(){const e=await this.evaluate((e=>{const t=[],r=Object.getOwnPropertyDescriptors(e);for(const e in r)r[e]?.enumerable&&t.push(e);return t})),t=new Map,r=await Promise.all(e.map((e=>this.getProperty(e))));for(const[i,n]of Object.entries(e)){const e={stack:[],error:void 0,hasError:!1};try{const s=Pr(e,r[i],!1);s&&t.set(n,s.move())}catch(t){e.error=t,e.hasError=!0}finally{Rr(e)}}return t}[(r=[sr()],i=[sr()],Ze)](){this.dispose().catch(yt)}[et](){return this.dispose()}});return t})();var Ar=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Or=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});class qr{#fe;#me;#ye;constructor(e,t,r){this.#fe=e,this.#me=t,this.#ye=r}get name(){return this.#fe}get initSource(){return this.#ye}async run(e,t,r,i){const n=new tt;try{if(!i){const i={stack:[],error:void 0,hasError:!1};try{const s=Ar(i,await e.evaluateHandle(((e,t)=>globalThis[e].args.get(t)),this.#fe,t),!1),a=await s.getProperties();for(const[e,t]of a)if(e in r)if("node"===t.remoteObject().subtype)r[+e]=t;else n.use(t);else n.use(t)}catch(e){i.error=e,i.hasError=!0}finally{Or(i)}}await e.evaluate(((e,t,r)=>{const i=globalThis[e].callbacks;i.get(t).resolve(r),i.delete(t)}),this.#fe,t,await this.#me(...r));for(const e of r)e instanceof Mr&&n.use(e)}catch(r){Gt(r)?await e.evaluate(((e,t,r,i)=>{const n=new Error(r);n.stack=i;const s=globalThis[e].callbacks;s.get(t).reject(n),s.delete(t)}),this.#fe,t,r.message,r.stack).catch(yt):await e.evaluate(((e,t,r)=>{const i=globalThis[e].callbacks;i.get(t).reject(r),i.delete(t)}),this.#fe,t,r).catch(yt)}}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const Nr=ot("puppeteer:protocol:SEND ►"),Br=ot("puppeteer:protocol:RECV ◀");class Lr extends it{#ge;#we;#ve;#be;#ke=new Map;#Se=!1;#Ce=new Set;#I=new Xt;constructor(e,t,r=0,i){super(),this.#ge=e,this.#ve=r,this.#be=i??18e4,this.#we=t,this.#we.onmessage=this.onMessage.bind(this),this.#we.onclose=this.#Te.bind(this)}static fromSession(e){return e.connection()}get delay(){return this.#ve}get timeout(){return this.#be}get _closed(){return this.#Se}get _sessions(){return this.#ke}session(e){return this.#ke.get(e)||null}url(){return this.#ge}send(e,t,r){return this._rawSend(this.#I,e,t,void 0,r)}_rawSend(e,t,r,i,n){return this.#Se?Promise.reject(new Error("Protocol error: Connection closed.")):e.create(t,n?.timeout??this.#be,(e=>{const n=JSON.stringify({method:t,params:r,id:e,sessionId:i});Nr(n),this.#we.send(n)}))}async closeBrowser(){await this.send("Browser.close")}async onMessage(e){this.#ve&&await new Promise((e=>setTimeout(e,this.#ve))),Br(e);const t=JSON.parse(e);if("Target.attachedToTarget"===t.method){const e=t.params.sessionId,r=new Zt(this,t.params.targetInfo.type,e,t.sessionId);this.#ke.set(e,r),this.emit(Bt.SessionAttached,r);const i=this.#ke.get(t.sessionId);i&&i.emit(Bt.SessionAttached,r)}else if("Target.detachedFromTarget"===t.method){const e=this.#ke.get(t.params.sessionId);if(e){e._onClosed(),this.#ke.delete(t.params.sessionId),this.emit(Bt.SessionDetached,e);const r=this.#ke.get(t.sessionId);r&&r.emit(Bt.SessionDetached,e)}}if(t.sessionId){const e=this.#ke.get(t.sessionId);e&&e._onMessage(t)}else t.id?t.error?this.#I.reject(t.id,Jt(t),t.error.message):this.#I.resolve(t.id,t.result):this.emit(t.method,t.params)}#Te(){if(!this.#Se){this.#Se=!0,this.#we.onmessage=void 0,this.#we.onclose=void 0,this.#I.clear();for(const e of this.#ke.values())e._onClosed();this.#ke.clear(),this.emit(Bt.Disconnected,void 0)}}dispose(){this.#Te(),this.#we.close()}isAutoAttached(e){return!this.#Ce.has(e)}async _createSession(e,t=!0){t||this.#Ce.add(e.targetId);const{sessionId:r}=await this.send("Target.attachToTarget",{targetId:e.targetId,flatten:!0});this.#Ce.delete(e.targetId);const i=this.#ke.get(r);if(!i)throw new Error("CDPSession creation failed.");return i}async createSession(e){return await this._createSession(e,!1)}getPendingProtocolErrors(){const e=[];e.push(...this.#I.getPendingProtocolErrors());for(const t of this.#ke.values())e.push(...t.getPendingProtocolErrors());return e}}function jr(e){return e instanceof ft}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Hr{#Ee;#Ie;constructor(e){this.#Ee=new $r(e),this.#Ie=new Kr(e)}updateClient(e){this.#Ee.updateClient(e),this.#Ie.updateClient(e)}async startJSCoverage(e={}){return await this.#Ee.start(e)}async stopJSCoverage(){return await this.#Ee.stop()}async startCSSCoverage(e={}){return await this.#Ie.start(e)}async stopCSSCoverage(){return await this.#Ie.stop()}}class $r{#xe;#Fe=!1;#_e=new Map;#De=new Map;#Pe;#Re=!1;#Me=!1;#Ae=!1;constructor(e){this.#xe=e}updateClient(e){this.#xe=e}async start(e={}){nt(!this.#Fe,"JSCoverage is already enabled");const{resetOnNavigation:t=!0,reportAnonymousScripts:r=!1,includeRawScriptCoverage:i=!1,useBlockCoverage:n=!0}=e;this.#Re=t,this.#Me=r,this.#Ae=i,this.#Fe=!0,this.#_e.clear(),this.#De.clear(),this.#Pe=new tt;const s=this.#Pe.use(new it(this.#xe));s.on("Debugger.scriptParsed",this.#Oe.bind(this)),s.on("Runtime.executionContextsCleared",this.#qe.bind(this)),await Promise.all([this.#xe.send("Profiler.enable"),this.#xe.send("Profiler.startPreciseCoverage",{callCount:this.#Ae,detailed:n}),this.#xe.send("Debugger.enable"),this.#xe.send("Debugger.setSkipAllPauses",{skip:!0})])}#qe(){this.#Re&&(this.#_e.clear(),this.#De.clear())}async#Oe(e){if(!wt.isPuppeteerURL(e.url)&&(e.url||this.#Me))try{const t=await this.#xe.send("Debugger.getScriptSource",{scriptId:e.scriptId});this.#_e.set(e.scriptId,e.url),this.#De.set(e.scriptId,t.scriptSource)}catch(e){yt(e)}}async stop(){nt(this.#Fe,"JSCoverage is not enabled"),this.#Fe=!1;const e=await Promise.all([this.#xe.send("Profiler.takePreciseCoverage"),this.#xe.send("Profiler.stopPreciseCoverage"),this.#xe.send("Profiler.disable"),this.#xe.send("Debugger.disable")]);this.#Pe?.dispose();const t=[],r=e[0];for(const e of r.result){let r=this.#_e.get(e.scriptId);!r&&this.#Me&&(r="debugger://VM"+e.scriptId);const i=this.#De.get(e.scriptId);if(void 0===i||void 0===r)continue;const n=[];for(const t of e.functions)n.push(...t.ranges);const s=Vr(n);this.#Ae?t.push({url:r,ranges:s,text:i,rawScriptCoverage:e}):t.push({url:r,ranges:s,text:i})}return t}}class Kr{#xe;#Fe=!1;#Ne=new Map;#Be=new Map;#Le;#Re=!1;constructor(e){this.#xe=e}updateClient(e){this.#xe=e}async start(e={}){nt(!this.#Fe,"CSSCoverage is already enabled");const{resetOnNavigation:t=!0}=e;this.#Re=t,this.#Fe=!0,this.#Ne.clear(),this.#Be.clear(),this.#Le=new tt;const r=this.#Le.use(new it(this.#xe));r.on("CSS.styleSheetAdded",this.#je.bind(this)),r.on("Runtime.executionContextsCleared",this.#qe.bind(this)),await Promise.all([this.#xe.send("DOM.enable"),this.#xe.send("CSS.enable"),this.#xe.send("CSS.startRuleUsageTracking")])}#qe(){this.#Re&&(this.#Ne.clear(),this.#Be.clear())}async#je(e){const t=e.header;if(t.sourceURL)try{const e=await this.#xe.send("CSS.getStyleSheetText",{styleSheetId:t.styleSheetId});this.#Ne.set(t.styleSheetId,t.sourceURL),this.#Be.set(t.styleSheetId,e.text)}catch(e){yt(e)}}async stop(){nt(this.#Fe,"CSSCoverage is not enabled"),this.#Fe=!1;const e=await this.#xe.send("CSS.stopRuleUsageTracking");await Promise.all([this.#xe.send("CSS.disable"),this.#xe.send("DOM.disable")]),this.#Le?.dispose();const t=new Map;for(const r of e.ruleUsage){let e=t.get(r.styleSheetId);e||(e=[],t.set(r.styleSheetId,e)),e.push({startOffset:r.startOffset,endOffset:r.endOffset,count:r.used?1:0})}const r=[];for(const e of this.#Ne.keys()){const i=this.#Ne.get(e);nt(void 0!==i,`Stylesheet URL is undefined (styleSheetId=${e})`);const n=this.#Be.get(e);nt(void 0!==n,`Stylesheet text is undefined (styleSheetId=${e})`);const s=Vr(t.get(e)||[]);r.push({url:i,ranges:s,text:n})}return r}}function Vr(e){const t=[];for(const r of e)t.push({offset:r.startOffset,type:0,range:r}),t.push({offset:r.endOffset,type:1,range:r});t.sort(((e,t)=>{if(e.offset!==t.offset)return e.offset-t.offset;if(e.type!==t.type)return t.type-e.type;const r=e.range.endOffset-e.range.startOffset,i=t.range.endOffset-t.range.startOffset;return 0===e.type?i-r:r-i}));const r=[],i=[];let n=0;for(const e of t){if(r.length&&n<e.offset&&r[r.length-1]>0){const t=i[i.length-1];t&&t.end===n?t.end=e.offset:i.push({start:n,end:e.offset})}n=e.offset,0===e.type?r.push(e.range.count):r.pop()}return i.filter((e=>e.end-e.start>0))}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Wr{#oe;#He;#$e;handled=!1;constructor(e,t,r=""){this.#oe=e,this.#He=t,this.#$e=r}type(){return this.#oe}message(){return this.#He}defaultValue(){return this.#$e}async accept(e){nt(!this.handled,"Cannot accept dialog which is already handled!"),this.handled=!0,await this.handle({accept:!0,text:e})}async dismiss(){nt(!this.handled,"Cannot dismiss dialog which is already handled!"),this.handled=!0,await this.handle({accept:!1})}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class zr extends Wr{#xe;constructor(e,t,r,i=""){super(t,r,i),this.#xe=e}async handle(e){await this.#xe.send("Page.handleJavaScriptDialog",{accept:e.accept,promptText:e.text})}}var Ur=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},Gr=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var m in i)f[m]="access"===m?{}:i[m];for(var m in i.access)f.access[m]=i.access[m];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var y=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===y)continue;if(null===y||"object"!=typeof y)throw new TypeError("Object expected");(o=a(y.get))&&(u.get=o),(o=a(y.set))&&(u.set=o),(o=a(y.init))&&n.unshift(o)}else(o=a(y))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0},Qr=self&&self.__setFunctionName||function(e,t,r){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})};class Jr{#Ke;#Ve;#We;constructor(e,t,r){this.#Ke=e,this.#Ve=t,this.#We=r,this.#Ve.registerState(this)}async setState(e){this.#Ke=e,await this.sync()}get state(){return this.#Ke}async sync(){await Promise.all(this.#Ve.clients().map((e=>this.#We(e,this.#Ke))))}}let Xr=(()=>{let e,t,r,i,n,s,a,o,c,l,d,u,h,p,f,m,y,g,w,v,b=[];return class{static{const k="function"==typeof Symbol&&Symbol.metadata?Object.create(null):void 0;e=[ar],r=[ar],n=[ar],a=[ar],c=[ar],d=[ar],h=[ar],f=[ar],y=[ar],w=[ar],Gr(this,t={value:Qr((async function(e,t){if(!t.viewport)return void await Promise.all([e.send("Emulation.clearDeviceMetricsOverride"),e.send("Emulation.setTouchEmulationEnabled",{enabled:!1})]).catch(yt);const{viewport:r}=t,i=r.isMobile||!1,n=r.width,s=r.height,a=r.deviceScaleFactor??1,o=r.isLandscape?{angle:90,type:"landscapePrimary"}:{angle:0,type:"portraitPrimary"},c=r.hasTouch||!1;await Promise.all([e.send("Emulation.setDeviceMetricsOverride",{mobile:i,width:n,height:s,deviceScaleFactor:a,screenOrientation:o}).catch((e=>{if(!e.message.includes("Target does not support metrics override"))throw e;yt(e)})),e.send("Emulation.setTouchEmulationEnabled",{enabled:c})])}),"#applyViewport")},e,{kind:"method",name:"#applyViewport",static:!1,private:!0,access:{has:e=>#ze in e,get:e=>e.#ze},metadata:k},null,b),Gr(this,i={value:Qr((async function(e,t){t.active&&(t.overrides?await e.send("Emulation.setIdleOverride",{isUserActive:t.overrides.isUserActive,isScreenUnlocked:t.overrides.isScreenUnlocked}):await e.send("Emulation.clearIdleOverride"))}),"#emulateIdleState")},r,{kind:"method",name:"#emulateIdleState",static:!1,private:!0,access:{has:e=>#Ue in e,get:e=>e.#Ue},metadata:k},null,b),Gr(this,s={value:Qr((async function(e,t){if(t.active)try{await e.send("Emulation.setTimezoneOverride",{timezoneId:t.timezoneId||""})}catch(e){if(Gt(e)&&e.message.includes("Invalid timezone"))throw new Error(`Invalid timezone ID: ${t.timezoneId}`);throw e}}),"#emulateTimezone")},n,{kind:"method",name:"#emulateTimezone",static:!1,private:!0,access:{has:e=>#Ge in e,get:e=>e.#Ge},metadata:k},null,b),Gr(this,o={value:Qr((async function(e,t){t.active&&await e.send("Emulation.setEmulatedVisionDeficiency",{type:t.visionDeficiency||"none"})}),"#emulateVisionDeficiency")},a,{kind:"method",name:"#emulateVisionDeficiency",static:!1,private:!0,access:{has:e=>#Qe in e,get:e=>e.#Qe},metadata:k},null,b),Gr(this,l={value:Qr((async function(e,t){t.active&&await e.send("Emulation.setCPUThrottlingRate",{rate:t.factor??1})}),"#emulateCpuThrottling")},c,{kind:"method",name:"#emulateCpuThrottling",static:!1,private:!0,access:{has:e=>#Je in e,get:e=>e.#Je},metadata:k},null,b),Gr(this,u={value:Qr((async function(e,t){t.active&&await e.send("Emulation.setEmulatedMedia",{features:t.mediaFeatures})}),"#emulateMediaFeatures")},d,{kind:"method",name:"#emulateMediaFeatures",static:!1,private:!0,access:{has:e=>#Xe in e,get:e=>e.#Xe},metadata:k},null,b),Gr(this,p={value:Qr((async function(e,t){t.active&&await e.send("Emulation.setEmulatedMedia",{media:t.type||""})}),"#emulateMediaType")},h,{kind:"method",name:"#emulateMediaType",static:!1,private:!0,access:{has:e=>#Ye in e,get:e=>e.#Ye},metadata:k},null,b),Gr(this,m={value:Qr((async function(e,t){t.active&&await e.send("Emulation.setGeolocationOverride",t.geoLocation?{longitude:t.geoLocation.longitude,latitude:t.geoLocation.latitude,accuracy:t.geoLocation.accuracy}:void 0)}),"#setGeolocation")},f,{kind:"method",name:"#setGeolocation",static:!1,private:!0,access:{has:e=>#Ze in e,get:e=>e.#Ze},metadata:k},null,b),Gr(this,g={value:Qr((async function(e,t){t.active&&await e.send("Emulation.setDefaultBackgroundColorOverride",{color:t.color})}),"#setDefaultBackgroundColor")},y,{kind:"method",name:"#setDefaultBackgroundColor",static:!1,private:!0,access:{has:e=>#et in e,get:e=>e.#et},metadata:k},null,b),Gr(this,v={value:Qr((async function(e,t){t.active&&await e.send("Emulation.setScriptExecutionDisabled",{value:!t.javaScriptEnabled})}),"#setJavaScriptEnabled")},w,{kind:"method",name:"#setJavaScriptEnabled",static:!1,private:!0,access:{has:e=>#tt in e,get:e=>e.#tt},metadata:k},null,b),k&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:k})}#xe=Ur(this,b);#rt=!1;#it=!1;#nt=[];#st=new Jr({active:!1},this,this.#ze);#at=new Jr({active:!1},this,this.#Ue);#ot=new Jr({active:!1},this,this.#Ge);#ct=new Jr({active:!1},this,this.#Qe);#lt=new Jr({active:!1},this,this.#Je);#dt=new Jr({active:!1},this,this.#Xe);#ut=new Jr({active:!1},this,this.#Ye);#ht=new Jr({active:!1},this,this.#Ze);#pt=new Jr({active:!1},this,this.#et);#ft=new Jr({javaScriptEnabled:!0,active:!1},this,this.#tt);#mt=new Set;constructor(e){this.#xe=e}updateClient(e){this.#xe=e,this.#mt.delete(e)}registerState(e){this.#nt.push(e)}clients(){return[this.#xe,...Array.from(this.#mt)]}async registerSpeculativeSession(e){this.#mt.add(e),e.once(Bt.Disconnected,(()=>{this.#mt.delete(e)})),Promise.all(this.#nt.map((e=>e.sync().catch(yt))))}get javascriptEnabled(){return this.#ft.state.javaScriptEnabled}async emulateViewport(e){const t=this.#st.state;if(!e&&!t.active)return!1;await this.#st.setState(e?{viewport:e,active:!0}:{active:!1});const r=e?.isMobile||!1,i=e?.hasTouch||!1,n=this.#rt!==r||this.#it!==i;return this.#rt=r,this.#it=i,n}get#ze(){return t.value}async emulateIdleState(e){await this.#at.setState({active:!0,overrides:e})}get#Ue(){return i.value}get#Ge(){return s.value}async emulateTimezone(e){await this.#ot.setState({timezoneId:e,active:!0})}get#Qe(){return o.value}async emulateVisionDeficiency(e){const t=new Set(["none","achromatopsia","blurredVision","deuteranopia","protanopia","tritanopia"]);nt(!e||t.has(e),`Unsupported vision deficiency: ${e}`),await this.#ct.setState({active:!0,visionDeficiency:e})}get#Je(){return l.value}async emulateCPUThrottling(e){nt(null===e||e>=1,"Throttling rate should be greater or equal to 1"),await this.#lt.setState({active:!0,factor:e??void 0})}get#Xe(){return u.value}async emulateMediaFeatures(e){if(Array.isArray(e))for(const t of e){const e=t.name;nt(/^(?:prefers-(?:color-scheme|reduced-motion)|color-gamut)$/.test(e),"Unsupported media feature: "+e)}await this.#dt.setState({active:!0,mediaFeatures:e})}get#Ye(){return p.value}async emulateMediaType(e){nt("screen"===e||"print"===e||void 0===(e??void 0),"Unsupported media type: "+e),await this.#ut.setState({type:e,active:!0})}get#Ze(){return m.value}async setGeolocation(e){const{longitude:t,latitude:r,accuracy:i=0}=e;if(t<-180||t>180)throw new Error(`Invalid longitude "${t}": precondition -180 <= LONGITUDE <= 180 failed.`);if(r<-90||r>90)throw new Error(`Invalid latitude "${r}": precondition -90 <= LATITUDE <= 90 failed.`);if(i<0)throw new Error(`Invalid accuracy "${i}": precondition 0 <= ACCURACY failed.`);await this.#ht.setState({active:!0,geoLocation:{longitude:t,latitude:r,accuracy:i}})}get#et(){return g.value}async resetDefaultBackgroundColor(){await this.#pt.setState({active:!0,color:void 0})}async setTransparentBackgroundColor(){await this.#pt.setState({active:!0,color:{r:0,g:0,b:0,a:0}})}get#tt(){return v.value}async setJavaScriptEnabled(e){await this.#ft.setState({active:!0,javaScriptEnabled:e})}}})();
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Yr extends it{#C;#yt=new Map;#gt=new Map;#wt=new Map;#vt;#bt;#kt=new WeakMap;#St=jt.create();#Ct=new Set;constructor(e,t,r){super(),this.#C=e,this.#vt=r,this.#bt=t,this.#C.on("Target.targetCreated",this.#Tt),this.#C.on("Target.targetDestroyed",this.#Et),this.#C.on(Bt.SessionDetached,this.#It),this.setupAttachmentListeners(this.#C)}setupAttachmentListeners(e){const t=t=>this.#xt(e,t);nt(!this.#kt.has(e)),this.#kt.set(e,t),e.on("Target.attachedToTarget",t)}#It=e=>{this.removeSessionListeners(e),this.#wt.delete(e.id())};removeSessionListeners(e){this.#kt.has(e)&&(e.off("Target.attachedToTarget",this.#kt.get(e)),this.#kt.delete(e))}getAvailableTargets(){return this.#gt}getChildTargets(e){return new Set}dispose(){this.#C.off("Target.targetCreated",this.#Tt),this.#C.off("Target.targetDestroyed",this.#Et)}async initialize(){await this.#C.send("Target.setDiscoverTargets",{discover:!0,filter:[{}]}),this.#Ct=new Set(this.#yt.keys()),await this.#St.valueOrThrow()}#Tt=async e=>{if(this.#yt.has(e.targetInfo.targetId))return;if(this.#yt.set(e.targetInfo.targetId,e.targetInfo),"browser"===e.targetInfo.type&&e.targetInfo.attached){const t=this.#bt(e.targetInfo,void 0);return t._initialize(),this.#gt.set(e.targetInfo.targetId,t),void this.#Ft(t._targetId)}const t=this.#bt(e.targetInfo,void 0);!this.#vt||this.#vt(t)?(t._initialize(),this.#gt.set(e.targetInfo.targetId,t),this.emit("targetAvailable",t),this.#Ft(t._targetId)):this.#Ft(e.targetInfo.targetId)};#Et=e=>{this.#yt.delete(e.targetId),this.#Ft(e.targetId);const t=this.#gt.get(e.targetId);t&&(this.emit("targetGone",t),this.#gt.delete(e.targetId))};#xt=async(e,t)=>{const r=t.targetInfo,i=this.#C.session(t.sessionId);if(!i)throw new Error(`Session ${t.sessionId} was not created.`);const n=this.#gt.get(r.targetId);nt(n,`Target ${r.targetId} is missing`),i._setTarget(n),this.setupAttachmentListeners(i),this.#wt.set(i.id(),this.#gt.get(r.targetId)),e.emit(Bt.Ready,i)};#Ft(e){this.#Ct.delete(e),0===this.#Ct.size&&this.#St.resolve()}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const Zr=Symbol("_isElementHandle"),ei=new Map;
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */function ti(e){let t=e.toString();try{new Function(`(${t})`)}catch(e){if(e.message.includes("Refused to evaluate a string as JavaScript because 'unsafe-eval' is not an allowed source of script in the following Content Security Policy directive"))return t;let r="function ";t.startsWith("async ")&&(r=`async ${r}`,t=t.substring(6)),t=`${r}${t}`;try{new Function(`(${t})`)}catch{throw new Error("Passed function cannot be serialized!")}}return t}const ri=(e,t)=>{let r=ti(e);for(const[e,i]of Object.entries(t))r=r.replace(new RegExp(`PLACEHOLDER\\(\\s*(?:'${e}'|"${e}")\\s*\\)`,"g"),`(${i})`);return(e=>{let t=ei.get(e);return t||(t=new Function(`return ${e}`)(),ei.set(e,t),t)})(r)};
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var ii=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},ni=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});const si=20;async function*ai(e,t){const r={stack:[],error:void 0,hasError:!1};try{const i=ii(r,await e.evaluateHandle((async(e,t)=>{const r=[];for(;r.length<t;){const t=await e.next();if(t.done)break;r.push(t.value)}return r}),t),!1),n=await i.getProperties(),s=n.values();return ii(r,new tt,!1).defer((()=>{for(const e of s){const t={stack:[],error:void 0,hasError:!1};try{ii(t,e,!1)[Ze]()}catch(e){t.error=e,t.hasError=!0}finally{ni(t)}}})),yield*s,0===n.size}catch(e){r.error=e,r.hasError=!0}finally{ni(r)}}async function*oi(e){const t={stack:[],error:void 0,hasError:!1};try{const r=ii(t,await e.evaluateHandle((e=>async function*(){yield*e}())),!1);yield*async function*(e){let t=si;for(;!(yield*ai(e,t));)t<<=1}(r)}catch(e){t.error=e,t.hasError=!0}finally{ni(t)}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class ci{static create=e=>new ci(e);#_t;constructor(e){this.#_t=e}async get(e){return await this.#_t(e)}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var li=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},di=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});class ui{static querySelectorAll;static querySelector;static get _querySelector(){if(this.querySelector)return this.querySelector;if(!this.querySelectorAll)throw new Error("Cannot create default `querySelector`.");return this.querySelector=ri((async(e,t,r)=>{const i=PLACEHOLDER("querySelectorAll")(e,t,r);for await(const e of i)return e;return null}),{querySelectorAll:ti(this.querySelectorAll)})}static get _querySelectorAll(){if(this.querySelectorAll)return this.querySelectorAll;if(!this.querySelector)throw new Error("Cannot create default `querySelectorAll`.");return this.querySelectorAll=ri((async function*(e,t,r){const i=PLACEHOLDER("querySelector"),n=await i(e,t,r);n&&(yield n)}),{querySelector:ti(this.querySelector)})}static async*queryAll(e,t){const r={stack:[],error:void 0,hasError:!1};try{const i=li(r,await e.evaluateHandle(this._querySelectorAll,t,ci.create((e=>e.puppeteerUtil))),!1);yield*oi(i)}catch(e){r.error=e,r.hasError=!0}finally{di(r)}}static async queryOne(e,t){const r={stack:[],error:void 0,hasError:!1};try{const i=li(r,await e.evaluateHandle(this._querySelector,t,ci.create((e=>e.puppeteerUtil))),!1);return Zr in i?i.move():null}catch(e){r.error=e,r.hasError=!0}finally{di(r)}}static async waitFor(e,t,r){const i={stack:[],error:void 0,hasError:!1};try{let n;const s=li(i,await(async()=>{if(Zr in e)return n=e.frame,await n.isolatedRealm().adoptHandle(e);n=e})(),!1),{visible:a=!1,hidden:o=!1,timeout:c,signal:l}=r,d=r.polling??(a||o?"raf":"mutation");try{const e={stack:[],error:void 0,hasError:!1};try{l?.throwIfAborted();const r=li(e,await n.isolatedRealm().waitForFunction((async(e,t,r,i,n)=>{const s=e.createFunction(t),a=await s(i??document,r,e);return e.checkVisibility(a,n)}),{polling:d,root:s,timeout:c,signal:l},ci.create((e=>e.puppeteerUtil)),ti(this._querySelector),t,s,!!a||!o&&void 0),!1);if(l?.aborted)throw l.reason;return Zr in r?await n.mainRealm().transferHandle(r):null}catch(t){e.error=t,e.hasError=!0}finally{di(e)}}catch(e){if(!Gt(e))throw e;if("AbortError"===e.name)throw e;throw e.message=`Waiting for selector \`${t}\` failed: ${e.message}`,e}}catch(e){i.error=e,i.hasError=!0}finally{di(i)}}}class hi{static async*map(e,t){for await(const r of e)yield await t(r)}static async*flatMap(e,t){for await(const r of e)yield*t(r)}static async collect(e){const t=[];for await(const r of e)t.push(r);return t}static async first(e){for await(const t of e)return t}}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const pi=e=>e.replace(/ +/g," ").trim(),fi=/\[\s*(?<attribute>\w+)\s*=\s*(?<quote>"|')(?<value>\\.|.*?(?=\k<quote>))\k<quote>\s*\]/g;class mi extends ui{static querySelector=async(e,t,{ariaQuerySelector:r})=>await r(e,t);static async*queryAll(e,t){const{name:r,role:i}=(e=>{const t={},r=e.replace(fi,((e,r,i,n)=>(r=r.trim(),nt((e=>["name","role"].includes(e))(r),`Unknown aria attribute "${r}" in selector`),t[r]=pi(n),"")));return r&&!t.name&&(t.name=pi(r)),t})(t);yield*e.queryAXTree(r,i)}static queryOne=async(e,t)=>await hi.first(this.queryAll(e,t))??null}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class yi extends ui{static querySelector=(e,t,{cssQuerySelector:r})=>r(e,t);static querySelectorAll=(e,t,{cssQuerySelectorAll:r})=>r(e,t)}const gi=new
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class{#Dt=!1;#Pt=new Set;append(e){this.#Rt((()=>{this.#Pt.add(e)}))}pop(e){this.#Rt((()=>{this.#Pt.delete(e)}))}inject(e,t=!1){(this.#Dt||t)&&e(this.#_t()),this.#Dt=!1}#Rt(e){e(),this.#Dt=!0}#_t(){return`(() => {\n      const module = {};\n      "use strict";var g=Object.defineProperty;var X=Object.getOwnPropertyDescriptor;var B=Object.getOwnPropertyNames;var Y=Object.prototype.hasOwnProperty;var l=(t,e)=>{for(var r in e)g(t,r,{get:e[r],enumerable:!0})},J=(t,e,r,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of B(e))!Y.call(t,n)&&n!==r&&g(t,n,{get:()=>e[n],enumerable:!(o=X(e,n))||o.enumerable});return t};var z=t=>J(g({},"__esModule",{value:!0}),t);var pe={};l(pe,{default:()=>he});module.exports=z(pe);var N=class extends Error{constructor(e,r){super(e,r),this.name=this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}},p=class extends N{};var c=class t{static create(e){return new t(e)}static async race(e){let r=new Set;try{let o=e.map(n=>n instanceof t?(n.#n&&r.add(n),n.valueOrThrow()):n);return await Promise.race(o)}finally{for(let o of r)o.reject(new Error("Timeout cleared"))}}#e=!1;#r=!1;#o;#t;#a=new Promise(e=>{this.#t=e});#n;#i;constructor(e){e&&e.timeout>0&&(this.#i=new p(e.message),this.#n=setTimeout(()=>{this.reject(this.#i)},e.timeout))}#l(e){clearTimeout(this.#n),this.#o=e,this.#t()}resolve(e){this.#r||this.#e||(this.#e=!0,this.#l(e))}reject(e){this.#r||this.#e||(this.#r=!0,this.#l(e))}resolved(){return this.#e}finished(){return this.#e||this.#r}value(){return this.#o}#s;valueOrThrow(){return this.#s||(this.#s=(async()=>{if(await this.#a,this.#r)throw this.#o;return this.#o})()),this.#s}};var L=new Map,F=t=>{let e=L.get(t);return e||(e=new Function(\`return \${t}\`)(),L.set(t,e),e)};var x={};l(x,{ariaQuerySelector:()=>G,ariaQuerySelectorAll:()=>b});var G=(t,e)=>globalThis.__ariaQuerySelector(t,e),b=async function*(t,e){yield*await globalThis.__ariaQuerySelectorAll(t,e)};var E={};l(E,{cssQuerySelector:()=>K,cssQuerySelectorAll:()=>Z});var K=(t,e)=>t.querySelector(e),Z=function(t,e){return t.querySelectorAll(e)};var A={};l(A,{customQuerySelectors:()=>P});var v=class{#e=new Map;register(e,r){if(!r.queryOne&&r.queryAll){let o=r.queryAll;r.queryOne=(n,i)=>{for(let s of o(n,i))return s;return null}}else if(r.queryOne&&!r.queryAll){let o=r.queryOne;r.queryAll=(n,i)=>{let s=o(n,i);return s?[s]:[]}}else if(!r.queryOne||!r.queryAll)throw new Error("At least one query method must be defined.");this.#e.set(e,{querySelector:r.queryOne,querySelectorAll:r.queryAll})}unregister(e){this.#e.delete(e)}get(e){return this.#e.get(e)}clear(){this.#e.clear()}},P=new v;var R={};l(R,{pierceQuerySelector:()=>ee,pierceQuerySelectorAll:()=>te});var ee=(t,e)=>{let r=null,o=n=>{let i=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT);do{let s=i.currentNode;s.shadowRoot&&o(s.shadowRoot),!(s instanceof ShadowRoot)&&s!==n&&!r&&s.matches(e)&&(r=s)}while(!r&&i.nextNode())};return t instanceof Document&&(t=t.documentElement),o(t),r},te=(t,e)=>{let r=[],o=n=>{let i=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT);do{let s=i.currentNode;s.shadowRoot&&o(s.shadowRoot),!(s instanceof ShadowRoot)&&s!==n&&s.matches(e)&&r.push(s)}while(i.nextNode())};return t instanceof Document&&(t=t.documentElement),o(t),r};var u=(t,e)=>{if(!t)throw new Error(e)};var y=class{#e;#r;#o;#t;constructor(e,r){this.#e=e,this.#r=r}async start(){let e=this.#t=c.create(),r=await this.#e();if(r){e.resolve(r);return}this.#o=new MutationObserver(async()=>{let o=await this.#e();o&&(e.resolve(o),await this.stop())}),this.#o.observe(this.#r,{childList:!0,subtree:!0,attributes:!0})}async stop(){u(this.#t,"Polling never started."),this.#t.finished()||this.#t.reject(new Error("Polling stopped")),this.#o&&(this.#o.disconnect(),this.#o=void 0)}result(){return u(this.#t,"Polling never started."),this.#t.valueOrThrow()}},w=class{#e;#r;constructor(e){this.#e=e}async start(){let e=this.#r=c.create(),r=await this.#e();if(r){e.resolve(r);return}let o=async()=>{if(e.finished())return;let n=await this.#e();if(!n){window.requestAnimationFrame(o);return}e.resolve(n),await this.stop()};window.requestAnimationFrame(o)}async stop(){u(this.#r,"Polling never started."),this.#r.finished()||this.#r.reject(new Error("Polling stopped"))}result(){return u(this.#r,"Polling never started."),this.#r.valueOrThrow()}},T=class{#e;#r;#o;#t;constructor(e,r){this.#e=e,this.#r=r}async start(){let e=this.#t=c.create(),r=await this.#e();if(r){e.resolve(r);return}this.#o=setInterval(async()=>{let o=await this.#e();o&&(e.resolve(o),await this.stop())},this.#r)}async stop(){u(this.#t,"Polling never started."),this.#t.finished()||this.#t.reject(new Error("Polling stopped")),this.#o&&(clearInterval(this.#o),this.#o=void 0)}result(){return u(this.#t,"Polling never started."),this.#t.valueOrThrow()}};var _={};l(_,{PCombinator:()=>H,pQuerySelector:()=>fe,pQuerySelectorAll:()=>$});var a=class{static async*map(e,r){for await(let o of e)yield await r(o)}static async*flatMap(e,r){for await(let o of e)yield*r(o)}static async collect(e){let r=[];for await(let o of e)r.push(o);return r}static async first(e){for await(let r of e)return r}};var C={};l(C,{textQuerySelectorAll:()=>m});var re=new Set(["checkbox","image","radio"]),oe=t=>t instanceof HTMLSelectElement||t instanceof HTMLTextAreaElement||t instanceof HTMLInputElement&&!re.has(t.type),ne=new Set(["SCRIPT","STYLE"]),f=t=>!ne.has(t.nodeName)&&!document.head?.contains(t),I=new WeakMap,j=t=>{for(;t;)I.delete(t),t instanceof ShadowRoot?t=t.host:t=t.parentNode},W=new WeakSet,se=new MutationObserver(t=>{for(let e of t)j(e.target)}),d=t=>{let e=I.get(t);if(e||(e={full:"",immediate:[]},!f(t)))return e;let r="";if(oe(t))e.full=t.value,e.immediate.push(t.value),t.addEventListener("input",o=>{j(o.target)},{once:!0,capture:!0});else{for(let o=t.firstChild;o;o=o.nextSibling){if(o.nodeType===Node.TEXT_NODE){e.full+=o.nodeValue??"",r+=o.nodeValue??"";continue}r&&e.immediate.push(r),r="",o.nodeType===Node.ELEMENT_NODE&&(e.full+=d(o).full)}r&&e.immediate.push(r),t instanceof Element&&t.shadowRoot&&(e.full+=d(t.shadowRoot).full),W.has(t)||(se.observe(t,{childList:!0,characterData:!0,subtree:!0}),W.add(t))}return I.set(t,e),e};var m=function*(t,e){let r=!1;for(let o of t.childNodes)if(o instanceof Element&&f(o)){let n;o.shadowRoot?n=m(o.shadowRoot,e):n=m(o,e);for(let i of n)yield i,r=!0}r||t instanceof Element&&f(t)&&d(t).full.includes(e)&&(yield t)};var k={};l(k,{checkVisibility:()=>le,pierce:()=>S,pierceAll:()=>O});var ie=["hidden","collapse"],le=(t,e)=>{if(!t)return e===!1;if(e===void 0)return t;let r=t.nodeType===Node.TEXT_NODE?t.parentElement:t,o=window.getComputedStyle(r),n=o&&!ie.includes(o.visibility)&&!ae(r);return e===n?t:!1};function ae(t){let e=t.getBoundingClientRect();return e.width===0||e.height===0}var ce=t=>"shadowRoot"in t&&t.shadowRoot instanceof ShadowRoot;function*S(t){ce(t)?yield t.shadowRoot:yield t}function*O(t){t=S(t).next().value,yield t;let e=[document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT)];for(let r of e){let o;for(;o=r.nextNode();)o.shadowRoot&&(yield o.shadowRoot,e.push(document.createTreeWalker(o.shadowRoot,NodeFilter.SHOW_ELEMENT)))}}var Q={};l(Q,{xpathQuerySelectorAll:()=>q});var q=function*(t,e,r=-1){let n=(t.ownerDocument||document).evaluate(e,t,null,XPathResult.ORDERED_NODE_ITERATOR_TYPE),i=[],s;for(;(s=n.iterateNext())&&(i.push(s),!(r&&i.length===r)););for(let h=0;h<i.length;h++)s=i[h],yield s,delete i[h]};var ue=/[-\\w\\P{ASCII}*]/,H=(r=>(r.Descendent=">>>",r.Child=">>>>",r))(H||{}),V=t=>"querySelectorAll"in t,M=class{#e;#r=[];#o=void 0;elements;constructor(e,r){this.elements=[e],this.#e=r,this.#t()}async run(){if(typeof this.#o=="string")switch(this.#o.trimStart()){case":scope":this.#t();break}for(;this.#o!==void 0;this.#t()){let e=this.#o;typeof e=="string"?e[0]&&ue.test(e[0])?this.elements=a.flatMap(this.elements,async function*(r){V(r)&&(yield*r.querySelectorAll(e))}):this.elements=a.flatMap(this.elements,async function*(r){if(!r.parentElement){if(!V(r))return;yield*r.querySelectorAll(e);return}let o=0;for(let n of r.parentElement.children)if(++o,n===r)break;yield*r.parentElement.querySelectorAll(\`:scope>:nth-child(\${o})\${e}\`)}):this.elements=a.flatMap(this.elements,async function*(r){switch(e.name){case"text":yield*m(r,e.value);break;case"xpath":yield*q(r,e.value);break;case"aria":yield*b(r,e.value);break;default:let o=P.get(e.name);if(!o)throw new Error(\`Unknown selector type: \${e.name}\`);yield*o.querySelectorAll(r,e.value)}})}}#t(){if(this.#r.length!==0){this.#o=this.#r.shift();return}if(this.#e.length===0){this.#o=void 0;return}let e=this.#e.shift();switch(e){case">>>>":{this.elements=a.flatMap(this.elements,S),this.#t();break}case">>>":{this.elements=a.flatMap(this.elements,O),this.#t();break}default:this.#r=e,this.#t();break}}},D=class{#e=new WeakMap;calculate(e,r=[]){if(e===null)return r;e instanceof ShadowRoot&&(e=e.host);let o=this.#e.get(e);if(o)return[...o,...r];let n=0;for(let s=e.previousSibling;s;s=s.previousSibling)++n;let i=this.calculate(e.parentNode,[n]);return this.#e.set(e,i),[...i,...r]}},U=(t,e)=>{if(t.length+e.length===0)return 0;let[r=-1,...o]=t,[n=-1,...i]=e;return r===n?U(o,i):r<n?-1:1},de=async function*(t){let e=new Set;for await(let o of t)e.add(o);let r=new D;yield*[...e.values()].map(o=>[o,r.calculate(o)]).sort(([,o],[,n])=>U(o,n)).map(([o])=>o)},$=function(t,e){let r=JSON.parse(e);if(r.some(o=>{let n=0;return o.some(i=>(typeof i=="string"?++n:n=0,n>1))}))throw new Error("Multiple deep combinators found in sequence.");return de(a.flatMap(r,o=>{let n=new M(t,o);return n.run(),n.elements}))},fe=async function(t,e){for await(let r of $(t,e))return r;return null};var me=Object.freeze({...x,...A,...R,..._,...C,...k,...Q,...E,Deferred:c,createFunction:F,createTextContent:d,IntervalPoller:T,isSuitableNodeForTextMatching:f,MutationPoller:y,RAFPoller:w}),he=me;\n\n      ${[...this.#Pt].map((e=>`(${e})(module.exports.default);`)).join("")}\n      return module.exports.default;\n    })()`}};
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const wi=new class{#i=new Map;get(e){const t=this.#i.get(e);return t?t[1]:void 0}register(e,t){nt(!this.#i.has(e),`Cannot register over existing handler: ${e}`),nt(/^[a-zA-Z]+$/.test(e),"Custom query handler names may only contain [a-zA-Z]"),nt(t.queryAll||t.queryOne,"At least one query method must be implemented.");const r=class extends ui{static querySelectorAll=ri(((e,t,r)=>r.customQuerySelectors.get(PLACEHOLDER("name")).querySelectorAll(e,t)),{name:JSON.stringify(e)});static querySelector=ri(((e,t,r)=>r.customQuerySelectors.get(PLACEHOLDER("name")).querySelector(e,t)),{name:JSON.stringify(e)})},i=ri((e=>{e.customQuerySelectors.register(PLACEHOLDER("name"),{queryAll:PLACEHOLDER("queryAll"),queryOne:PLACEHOLDER("queryOne")})}),{name:JSON.stringify(e),queryAll:t.queryAll?ti(t.queryAll):String(void 0),queryOne:t.queryOne?ti(t.queryOne):String(void 0)}).toString();this.#i.set(e,[i,r]),gi.append(i)}unregister(e){const t=this.#i.get(e);if(!t)throw new Error(`Cannot unregister unknown handler: ${e}`);gi.pop(t[0]),this.#i.delete(e)}names(){return[...this.#i.keys()]}clear(){for(const[e]of this.#i)gi.pop(e);this.#i.clear()}};
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class vi extends ui{static querySelectorAll=(e,t,{pQuerySelectorAll:r})=>r(e,t);static querySelector=(e,t,{pQuerySelector:r})=>r(e,t)}var bi={attribute:/\[\s*(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?(?<name>[-\w\P{ASCII}]+)\s*(?:(?<operator>\W?=)\s*(?<value>.+?)\s*(\s(?<caseSensitive>[iIsS]))?\s*)?\]/gu,id:/#(?<name>[-\w\P{ASCII}]+)/gu,class:/\.(?<name>[-\w\P{ASCII}]+)/gu,comma:/\s*,\s*/g,combinator:/\s*[\s>+~]\s*/g,"pseudo-element":/::(?<name>[-\w\P{ASCII}]+)(?:\((?<argument>¶*)\))?/gu,"pseudo-class":/:(?<name>[-\w\P{ASCII}]+)(?:\((?<argument>¶*)\))?/gu,universal:/(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?\*/gu,type:/(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?(?<name>[-\w\P{ASCII}]+)/gu},ki=new Set(["combinator","comma"]),Si=e=>{switch(e){case"pseudo-element":case"pseudo-class":return new RegExp(bi[e].source.replace("(?<argument>¶*)","(?<argument>.*)"),"gu");default:return bi[e]}};function Ci(e,t){let r=0,i="";for(;t<e.length;t++){const n=e[t];switch(n){case"(":++r;break;case")":--r}if(i+=n,0===r)return i}return i}var Ti=/(['"])([^\\\n]+?)\1/g,Ei=/\\./g;function Ii(e,t=bi){if(""===(e=e.trim()))return[];const r=[];e=(e=e.replace(Ei,((e,t)=>(r.push({value:e,offset:t}),"".repeat(e.length))))).replace(Ti,((e,t,i,n)=>(r.push({value:e,offset:n}),`${t}${"".repeat(i.length)}${t}`)));{let t,i=0;for(;(t=e.indexOf("(",i))>-1;){const n=Ci(e,t);r.push({value:n,offset:t}),e=`${e.substring(0,t)}(${"¶".repeat(n.length-2)})${e.substring(t+n.length)}`,i=t+n.length}}const i=function(e,t=bi){if(!e)return[];const r=[e];for(const[e,i]of Object.entries(t))for(let t=0;t<r.length;t++){const n=r[t];if("string"!=typeof n)continue;i.lastIndex=0;const s=i.exec(n);if(!s)continue;const a=s.index-1,o=[],c=s[0],l=n.slice(0,a+1);l&&o.push(l),o.push({...s.groups,type:e,content:c});const d=n.slice(a+c.length+1);d&&o.push(d),r.splice(t,1,...o)}let i=0;for(const e of r)switch(typeof e){case"string":throw new Error(`Unexpected sequence ${e} found at index ${i}`);case"object":i+=e.content.length,e.pos=[i-e.content.length,i],ki.has(e.type)&&(e.content=e.content.trim()||" ")}return r}(e,t),n=new Set;for(const e of r.reverse())for(const t of i){const{offset:r,value:i}=e;if(!(t.pos[0]<=r&&r+i.length<=t.pos[1]))continue;const{content:s}=t,a=r-t.pos[0];t.content=s.slice(0,a)+i+s.slice(a+i.length),t.content!==s&&n.add(t)}for(const e of n){const t=Si(e.type);if(!t)throw new Error(`Unknown token type: ${e.type}`);t.lastIndex=0;const r=t.exec(e.content);if(!r)throw new Error(`Unable to parse content for ${e.type}: ${e.content}`);Object.assign(e,r.groups)}return i}function*xi(e,t){switch(e.type){case"list":for(let t of e.list)yield*xi(t,e);break;case"complex":yield*xi(e.left,e),yield*xi(e.right,e);break;case"compound":yield*e.list.map((t=>[t,e]));break;default:yield[e,t]}}function Fi(e){let t;return t=Array.isArray(e)?e:[...xi(e)].map((([e])=>e)),t.map((e=>e.content)).join("")}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */bi.nesting=/&/g,bi.combinator=/\s*(>>>>?|[\s>+~])\s*/g;const _i=/\\[\s\S]/g,Di=e=>e.length<=1?e:('"'!==e[0]&&"'"!==e[0]||!e.endsWith(e[0])||(e=e.slice(1,-1)),e.replace(_i,(e=>e[1])));
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const Pi={aria:mi,pierce:
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class extends ui{static querySelector=(e,t,{pierceQuerySelector:r})=>r(e,t);static querySelectorAll=(e,t,{pierceQuerySelectorAll:r})=>r(e,t)},xpath:
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class extends ui{static querySelectorAll=(e,t,{xpathQuerySelectorAll:r})=>r(e,t);static querySelector=(e,t,{xpathQuerySelectorAll:r})=>{for(const i of r(e,t,1))return i;return null}},text:
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class extends ui{static querySelectorAll=(e,t,{textQuerySelectorAll:r})=>r(e,t)}},Ri=["=","/"];function Mi(e){for(const t of[wi.names().map((e=>[e,wi.get(e)])),Object.entries(Pi)])for(const[r,i]of t)for(const t of Ri){const n=`${r}${t}`;if(e.startsWith(n))return{updatedSelector:e=e.slice(n.length),polling:"aria"===r?"raf":"mutation",QueryHandler:i}}try{const[t,r,i,n]=function(e){let t=!0,r=!1,i=!1;const n=Ii(e);if(0===n.length)return[[],t,i,!1];let s=[],a=[s];const o=[a],c=[];for(const e of n){switch(e.type){case"combinator":switch(e.content){case">>>":t=!1,c.length&&(s.push(Fi(c)),c.splice(0)),s=[],a.push(">>>"),a.push(s);continue;case">>>>":t=!1,c.length&&(s.push(Fi(c)),c.splice(0)),s=[],a.push(">>>>"),a.push(s);continue}break;case"pseudo-element":if(!e.name.startsWith("-p-"))break;t=!1,c.length&&(s.push(Fi(c)),c.splice(0));const n=e.name.slice(3);"aria"===n&&(r=!0),s.push({name:n,value:Di(e.argument??"")});continue;case"pseudo-class":i=!0;break;case"comma":c.length&&(s.push(Fi(c)),c.splice(0)),s=[],a=[s],o.push(a);continue}c.push(e)}return c.length&&s.push(Fi(c)),[o,t,i,r]}(e);return r?{updatedSelector:e,polling:i?"raf":"mutation",QueryHandler:yi}:{updatedSelector:JSON.stringify(t),polling:n?"raf":"mutation",QueryHandler:vi}}catch{return{updatedSelector:e,polling:"mutation",QueryHandler:yi}}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var Ai,Oi=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},qi=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var m in i)f[m]="access"===m?{}:i[m];for(var m in i.access)f.access[m]=i.access[m];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var y=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===y)continue;if(null===y||"object"!=typeof y)throw new TypeError("Object expected");(o=a(y.get))&&(u.get=o),(o=a(y.set))&&(u.set=o),(o=a(y.init))&&n.unshift(o)}else(o=a(y))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0},Ni=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Bi=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});!function(e){e.FrameNavigated=Symbol("Frame.FrameNavigated"),e.FrameSwapped=Symbol("Frame.FrameSwapped"),e.LifecycleEvent=Symbol("Frame.LifecycleEvent"),e.FrameNavigatedWithinDocument=Symbol("Frame.FrameNavigatedWithinDocument"),e.FrameDetached=Symbol("Frame.FrameDetached"),e.FrameSwappedByActivation=Symbol("Frame.FrameSwappedByActivation")}(Ai||(Ai={}));const Li=sr((e=>`Attempted to use detached Frame '${e._id}'.`));let ji=(()=>{let e,t,r,i,n,s,a,o,c,l,d,u,h,p,f,m,y,g,w,v,b=it,k=[];return class extends b{static{const S="function"==typeof Symbol&&Symbol.metadata?Object.create(b[Symbol.metadata]??null):void 0;e=[Li],t=[Li],r=[Li],i=[Li],n=[Li],s=[Li],a=[Li],o=[Li],c=[Li],l=[Li],d=[Li],u=[Li],h=[Li],p=[Li],f=[Li],m=[Li],y=[Li],g=[Li],w=[Li],v=[Li],qi(this,null,e,{kind:"method",name:"frameElement",static:!1,private:!1,access:{has:e=>"frameElement"in e,get:e=>e.frameElement},metadata:S},null,k),qi(this,null,t,{kind:"method",name:"evaluateHandle",static:!1,private:!1,access:{has:e=>"evaluateHandle"in e,get:e=>e.evaluateHandle},metadata:S},null,k),qi(this,null,r,{kind:"method",name:"evaluate",static:!1,private:!1,access:{has:e=>"evaluate"in e,get:e=>e.evaluate},metadata:S},null,k),qi(this,null,i,{kind:"method",name:"locator",static:!1,private:!1,access:{has:e=>"locator"in e,get:e=>e.locator},metadata:S},null,k),qi(this,null,n,{kind:"method",name:"$",static:!1,private:!1,access:{has:e=>"$"in e,get:e=>e.$},metadata:S},null,k),qi(this,null,s,{kind:"method",name:"$$",static:!1,private:!1,access:{has:e=>"$$"in e,get:e=>e.$$},metadata:S},null,k),qi(this,null,a,{kind:"method",name:"$eval",static:!1,private:!1,access:{has:e=>"$eval"in e,get:e=>e.$eval},metadata:S},null,k),qi(this,null,o,{kind:"method",name:"$$eval",static:!1,private:!1,access:{has:e=>"$$eval"in e,get:e=>e.$$eval},metadata:S},null,k),qi(this,null,c,{kind:"method",name:"waitForSelector",static:!1,private:!1,access:{has:e=>"waitForSelector"in e,get:e=>e.waitForSelector},metadata:S},null,k),qi(this,null,l,{kind:"method",name:"waitForFunction",static:!1,private:!1,access:{has:e=>"waitForFunction"in e,get:e=>e.waitForFunction},metadata:S},null,k),qi(this,null,d,{kind:"method",name:"content",static:!1,private:!1,access:{has:e=>"content"in e,get:e=>e.content},metadata:S},null,k),qi(this,null,u,{kind:"method",name:"addScriptTag",static:!1,private:!1,access:{has:e=>"addScriptTag"in e,get:e=>e.addScriptTag},metadata:S},null,k),qi(this,null,h,{kind:"method",name:"addStyleTag",static:!1,private:!1,access:{has:e=>"addStyleTag"in e,get:e=>e.addStyleTag},metadata:S},null,k),qi(this,null,p,{kind:"method",name:"click",static:!1,private:!1,access:{has:e=>"click"in e,get:e=>e.click},metadata:S},null,k),qi(this,null,f,{kind:"method",name:"focus",static:!1,private:!1,access:{has:e=>"focus"in e,get:e=>e.focus},metadata:S},null,k),qi(this,null,m,{kind:"method",name:"hover",static:!1,private:!1,access:{has:e=>"hover"in e,get:e=>e.hover},metadata:S},null,k),qi(this,null,y,{kind:"method",name:"select",static:!1,private:!1,access:{has:e=>"select"in e,get:e=>e.select},metadata:S},null,k),qi(this,null,g,{kind:"method",name:"tap",static:!1,private:!1,access:{has:e=>"tap"in e,get:e=>e.tap},metadata:S},null,k),qi(this,null,w,{kind:"method",name:"type",static:!1,private:!1,access:{has:e=>"type"in e,get:e=>e.type},metadata:S},null,k),qi(this,null,v,{kind:"method",name:"title",static:!1,private:!1,access:{has:e=>"title"in e,get:e=>e.title},metadata:S},null,k),S&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:S})}_id=Oi(this,k);_parentId;_name;_hasStartedLoading=!1;constructor(){super()}#Mt;#At(){return this.#Mt||(this.#Mt=this.mainRealm().evaluateHandle((()=>document))),this.#Mt}clearDocumentHandle(){this.#Mt=void 0}async frameElement(){const e={stack:[],error:void 0,hasError:!1};try{const t=this.parentFrame();if(!t)return null;const r=Ni(e,await t.isolatedRealm().evaluateHandle((()=>document.querySelectorAll("iframe,frame"))),!1);for await(const e of oi(r)){const t={stack:[],error:void 0,hasError:!1};try{const r=Ni(t,e,!1),i=await r.contentFrame();if(i?._id===this._id)return r.move()}catch(e){t.error=e,t.hasError=!0}finally{Bi(t)}}return null}catch(t){e.error=t,e.hasError=!0}finally{Bi(e)}}async evaluateHandle(e,...t){return e=vt(this.evaluateHandle.name,e),await this.mainRealm().evaluateHandle(e,...t)}async evaluate(e,...t){return e=vt(this.evaluate.name,e),await this.mainRealm().evaluate(e,...t)}locator(e){return"string"==typeof e?yr.create(this,e):hr.create(this,e)}async $(e){const t=await this.#At();return await t.$(e)}async $$(e,t){const r=await this.#At();return await r.$$(e,t)}async $eval(e,t,...r){t=vt(this.$eval.name,t);const i=await this.#At();return await i.$eval(e,t,...r)}async $$eval(e,t,...r){t=vt(this.$$eval.name,t);const i=await this.#At();return await i.$$eval(e,t,...r)}async waitForSelector(e,t={}){const{updatedSelector:r,QueryHandler:i,polling:n}=Mi(e);return await i.waitFor(this,r,{polling:n,...t})}async waitForFunction(e,t={},...r){return await this.mainRealm().waitForFunction(e,t,...r)}async content(){return await this.evaluate((()=>{let e="";for(const t of document.childNodes)if(t===document.documentElement)e+=document.documentElement.outerHTML;else e+=(new XMLSerializer).serializeToString(t);return e}))}async setFrameContent(e){return await this.evaluate((e=>{document.open(),document.write(e),document.close()}),e)}name(){return this._name||""}isDetached(){return this.detached}get disposed(){return this.detached}async addScriptTag(e){let{content:t="",type:r}=e;const{path:i}=e;if(+!!e.url+ +!!i+ +!!t!=1)throw new Error("Exactly one of `url`, `path`, or `content` must be specified.");if(i){const e=await Tt();t=await e.readFile(i,"utf8"),t+=`//# sourceURL=${i.replace(/\n/g,"")}`}return r=r??"text/javascript",await this.mainRealm().transferHandle(await this.isolatedRealm().evaluateHandle((async({url:e,id:t,type:r,content:i})=>await new Promise(((n,s)=>{const a=document.createElement("script");a.type=r,a.text=i,a.addEventListener("error",(e=>{s(new Error(e.message??"Could not load script"))}),{once:!0}),t&&(a.id=t),e?(a.src=e,a.addEventListener("load",(()=>{n(a)}),{once:!0}),document.head.appendChild(a)):(document.head.appendChild(a),n(a))}))),{...e,type:r,content:t}))}async addStyleTag(e){let{content:t=""}=e;const{path:r}=e;if(+!!e.url+ +!!r+ +!!t!=1)throw new Error("Exactly one of `url`, `path`, or `content` must be specified.");if(r){const i=await Tt();t=await i.readFile(r,"utf8"),t+="/*# sourceURL="+r.replace(/\n/g,"")+"*/",e.content=t}return await this.mainRealm().transferHandle(await this.isolatedRealm().evaluateHandle((async({url:e,content:t})=>await new Promise(((r,i)=>{let n;if(e){const t=document.createElement("link");t.rel="stylesheet",t.href=e,n=t}else n=document.createElement("style"),n.appendChild(document.createTextNode(t));return n.addEventListener("load",(()=>{r(n)}),{once:!0}),n.addEventListener("error",(e=>{i(new Error(e.message??"Could not load style"))}),{once:!0}),document.head.appendChild(n),n}))),e))}async click(e,t={}){const r={stack:[],error:void 0,hasError:!1};try{const i=Ni(r,await this.$(e),!1);nt(i,`No element found for selector: ${e}`),await i.click(t),await i.dispose()}catch(e){r.error=e,r.hasError=!0}finally{Bi(r)}}async focus(e){const t={stack:[],error:void 0,hasError:!1};try{const r=Ni(t,await this.$(e),!1);nt(r,`No element found for selector: ${e}`),await r.focus()}catch(e){t.error=e,t.hasError=!0}finally{Bi(t)}}async hover(e){const t={stack:[],error:void 0,hasError:!1};try{const r=Ni(t,await this.$(e),!1);nt(r,`No element found for selector: ${e}`),await r.hover()}catch(e){t.error=e,t.hasError=!0}finally{Bi(t)}}async select(e,...t){const r={stack:[],error:void 0,hasError:!1};try{const i=Ni(r,await this.$(e),!1);return nt(i,`No element found for selector: ${e}`),await i.select(...t)}catch(e){r.error=e,r.hasError=!0}finally{Bi(r)}}async tap(e){const t={stack:[],error:void 0,hasError:!1};try{const r=Ni(t,await this.$(e),!1);nt(r,`No element found for selector: ${e}`),await r.tap()}catch(e){t.error=e,t.hasError=!0}finally{Bi(t)}}async type(e,t,r){const i={stack:[],error:void 0,hasError:!1};try{const n=Ni(i,await this.$(e),!1);nt(n,`No element found for selector: ${e}`),await n.type(t,r)}catch(e){i.error=e,i.hasError=!0}finally{Bi(i)}}async title(){return await this.isolatedRealm().evaluate((()=>document.title))}}})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Hi{#E;#Ot;#qt=new WeakMap;constructor(e,t,r){this.#E=t,this.#Ot=r,this.#qt.set(e,t)}get id(){return this.#E}get source(){return this.#Ot}getIdForFrame(e){return this.#qt.get(e)}setIdForFrame(e,t){this.#qt.set(e,t)}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class $i{id;name;constructor(e,t){this.id=e,this.name=t}}class Ki{#xe;#Nt;#E;#pe=!1;#Bt=this.#Lt.bind(this);#jt=new Set;devices=[];constructor(e,t,r){this.#xe=e,this.#Nt=t,this.#E=r.id,this.#xe.on("DeviceAccess.deviceRequestPrompted",this.#Bt),this.#xe.on("Target.detachedFromTarget",(()=>{this.#xe=null})),this.#Lt(r)}#Lt(e){if(e.id===this.#E)for(const t of e.devices){if(this.devices.some((e=>e.id===t.id)))continue;const e=new $i(t.id,t.name);this.devices.push(e);for(const t of this.#jt)t.filter(e)&&t.promise.resolve(e)}}async waitForDevice(e,t={}){for(const t of this.devices)if(e(t))return t;const{timeout:r=this.#Nt.timeout()}=t,i=jt.create({message:`Waiting for \`DeviceRequestPromptDevice\` failed: ${r}ms exceeded`,timeout:r}),n={filter:e,promise:i};this.#jt.add(n);try{return await i.valueOrThrow()}finally{this.#jt.delete(n)}}async select(e){return nt(null!==this.#xe,"Cannot select device through detached session!"),nt(this.devices.includes(e),"Cannot select unknown device!"),nt(!this.#pe,"Cannot select DeviceRequestPrompt which is already handled!"),this.#xe.off("DeviceAccess.deviceRequestPrompted",this.#Bt),this.#pe=!0,await this.#xe.send("DeviceAccess.selectPrompt",{id:this.#E,deviceId:e.id})}async cancel(){return nt(null!==this.#xe,"Cannot cancel prompt through detached session!"),nt(!this.#pe,"Cannot cancel DeviceRequestPrompt which is already handled!"),this.#xe.off("DeviceAccess.deviceRequestPrompted",this.#Bt),this.#pe=!0,await this.#xe.send("DeviceAccess.cancelPrompt",{id:this.#E})}}class Vi{#xe;#Nt;#Ht=new Set;constructor(e,t){this.#xe=e,this.#Nt=t,this.#xe.on("DeviceAccess.deviceRequestPrompted",(e=>{this.#$t(e)})),this.#xe.on("Target.detachedFromTarget",(()=>{this.#xe=null}))}async waitForDevicePrompt(e={}){nt(null!==this.#xe,"Cannot wait for device prompt through detached session!");let t;0===this.#Ht.size&&(t=this.#xe.send("DeviceAccess.enable"));const{timeout:r=this.#Nt.timeout()}=e,i=jt.create({message:`Waiting for \`DeviceRequestPrompt\` failed: ${r}ms exceeded`,timeout:r});this.#Ht.add(i);try{const[e]=await Promise.all([i.valueOrThrow(),t]);return e}finally{this.#Ht.delete(i)}}#$t(e){if(!this.#Ht.size)return;nt(null!==this.#xe);const t=new Ki(this.#xe,this.#Nt,e);for(const e of this.#Ht)e.resolve(t);this.#Ht.clear()}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var Wi=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},zi=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var m in i)f[m]="access"===m?{}:i[m];for(var m in i.access)f.access[m]=i.access[m];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var y=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===y)continue;if(null===y||"object"!=typeof y)throw new TypeError("Object expected");(o=a(y.get))&&(u.get=o),(o=a(y.set))&&(u.set=o),(o=a(y.init))&&n.unshift(o)}else(o=a(y))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0},Ui=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},Gi=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i}),Qi=self&&self.__setFunctionName||function(e,t,r){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})};let Ji=(()=>{var e,t,r,i,n,s,a,o,c,l,d,u,h,p,f,m,y,g,w,v,b,k,S,C,T,E,I,x,F,_;let D,P,R,M,A,O,q,N,B,L,j,H,$,K,V,W,z,U,G,Q,J,X,Y,Z,ee,te,re,ie,ne,se,ae,oe,ce=Mr,le=[];return class de extends ce{static{const ue="function"==typeof Symbol&&Symbol.metadata?Object.create(ce[Symbol.metadata]??null):void 0;D=[sr(),(e=de).bindIsolatedHandle.bind(e)],P=[sr(),(t=de).bindIsolatedHandle.bind(t)],R=[sr(),(r=de).bindIsolatedHandle.bind(r)],M=[sr(),(i=de).bindIsolatedHandle.bind(i)],A=[sr()],O=[(n=de).bindIsolatedHandle.bind(n)],N=[sr(),(s=de).bindIsolatedHandle.bind(s)],B=[sr(),(a=de).bindIsolatedHandle.bind(a)],L=[sr(),(o=de).bindIsolatedHandle.bind(o)],j=[sr(),(c=de).bindIsolatedHandle.bind(c)],H=[sr(),(l=de).bindIsolatedHandle.bind(l)],$=[sr(),(d=de).bindIsolatedHandle.bind(d)],K=[sr(),(u=de).bindIsolatedHandle.bind(u)],V=[sr(),(h=de).bindIsolatedHandle.bind(h)],W=[sr(),(p=de).bindIsolatedHandle.bind(p)],z=[sr(),(f=de).bindIsolatedHandle.bind(f)],U=[sr(),(m=de).bindIsolatedHandle.bind(m)],G=[sr(),(y=de).bindIsolatedHandle.bind(y)],Q=[sr(),(g=de).bindIsolatedHandle.bind(g)],J=[sr(),(w=de).bindIsolatedHandle.bind(w)],X=[sr(),(v=de).bindIsolatedHandle.bind(v)],Y=[sr(),(b=de).bindIsolatedHandle.bind(b)],Z=[sr(),(k=de).bindIsolatedHandle.bind(k)],ee=[sr(),(S=de).bindIsolatedHandle.bind(S)],te=[sr(),(C=de).bindIsolatedHandle.bind(C)],re=[sr(),(T=de).bindIsolatedHandle.bind(T)],ie=[sr(),(E=de).bindIsolatedHandle.bind(E)],ne=[sr(),(I=de).bindIsolatedHandle.bind(I)],se=[sr(),(x=de).bindIsolatedHandle.bind(x)],ae=[sr(),(F=de).bindIsolatedHandle.bind(F)],oe=[sr(),(_=de).bindIsolatedHandle.bind(_)],zi(this,null,D,{kind:"method",name:"getProperty",static:!1,private:!1,access:{has:e=>"getProperty"in e,get:e=>e.getProperty},metadata:ue},null,le),zi(this,null,P,{kind:"method",name:"getProperties",static:!1,private:!1,access:{has:e=>"getProperties"in e,get:e=>e.getProperties},metadata:ue},null,le),zi(this,null,R,{kind:"method",name:"jsonValue",static:!1,private:!1,access:{has:e=>"jsonValue"in e,get:e=>e.jsonValue},metadata:ue},null,le),zi(this,null,M,{kind:"method",name:"$",static:!1,private:!1,access:{has:e=>"$"in e,get:e=>e.$},metadata:ue},null,le),zi(this,null,A,{kind:"method",name:"$$",static:!1,private:!1,access:{has:e=>"$$"in e,get:e=>e.$$},metadata:ue},null,le),zi(this,q={value:Qi((async function(e){return await this.#Kt(e)}),"#$$")},O,{kind:"method",name:"#$$",static:!1,private:!0,access:{has:e=>#Vt in e,get:e=>e.#Vt},metadata:ue},null,le),zi(this,null,N,{kind:"method",name:"waitForSelector",static:!1,private:!1,access:{has:e=>"waitForSelector"in e,get:e=>e.waitForSelector},metadata:ue},null,le),zi(this,null,B,{kind:"method",name:"isVisible",static:!1,private:!1,access:{has:e=>"isVisible"in e,get:e=>e.isVisible},metadata:ue},null,le),zi(this,null,L,{kind:"method",name:"isHidden",static:!1,private:!1,access:{has:e=>"isHidden"in e,get:e=>e.isHidden},metadata:ue},null,le),zi(this,null,j,{kind:"method",name:"toElement",static:!1,private:!1,access:{has:e=>"toElement"in e,get:e=>e.toElement},metadata:ue},null,le),zi(this,null,H,{kind:"method",name:"clickablePoint",static:!1,private:!1,access:{has:e=>"clickablePoint"in e,get:e=>e.clickablePoint},metadata:ue},null,le),zi(this,null,$,{kind:"method",name:"hover",static:!1,private:!1,access:{has:e=>"hover"in e,get:e=>e.hover},metadata:ue},null,le),zi(this,null,K,{kind:"method",name:"click",static:!1,private:!1,access:{has:e=>"click"in e,get:e=>e.click},metadata:ue},null,le),zi(this,null,V,{kind:"method",name:"drag",static:!1,private:!1,access:{has:e=>"drag"in e,get:e=>e.drag},metadata:ue},null,le),zi(this,null,W,{kind:"method",name:"dragEnter",static:!1,private:!1,access:{has:e=>"dragEnter"in e,get:e=>e.dragEnter},metadata:ue},null,le),zi(this,null,z,{kind:"method",name:"dragOver",static:!1,private:!1,access:{has:e=>"dragOver"in e,get:e=>e.dragOver},metadata:ue},null,le),zi(this,null,U,{kind:"method",name:"drop",static:!1,private:!1,access:{has:e=>"drop"in e,get:e=>e.drop},metadata:ue},null,le),zi(this,null,G,{kind:"method",name:"dragAndDrop",static:!1,private:!1,access:{has:e=>"dragAndDrop"in e,get:e=>e.dragAndDrop},metadata:ue},null,le),zi(this,null,Q,{kind:"method",name:"select",static:!1,private:!1,access:{has:e=>"select"in e,get:e=>e.select},metadata:ue},null,le),zi(this,null,J,{kind:"method",name:"tap",static:!1,private:!1,access:{has:e=>"tap"in e,get:e=>e.tap},metadata:ue},null,le),zi(this,null,X,{kind:"method",name:"touchStart",static:!1,private:!1,access:{has:e=>"touchStart"in e,get:e=>e.touchStart},metadata:ue},null,le),zi(this,null,Y,{kind:"method",name:"touchMove",static:!1,private:!1,access:{has:e=>"touchMove"in e,get:e=>e.touchMove},metadata:ue},null,le),zi(this,null,Z,{kind:"method",name:"touchEnd",static:!1,private:!1,access:{has:e=>"touchEnd"in e,get:e=>e.touchEnd},metadata:ue},null,le),zi(this,null,ee,{kind:"method",name:"focus",static:!1,private:!1,access:{has:e=>"focus"in e,get:e=>e.focus},metadata:ue},null,le),zi(this,null,te,{kind:"method",name:"type",static:!1,private:!1,access:{has:e=>"type"in e,get:e=>e.type},metadata:ue},null,le),zi(this,null,re,{kind:"method",name:"press",static:!1,private:!1,access:{has:e=>"press"in e,get:e=>e.press},metadata:ue},null,le),zi(this,null,ie,{kind:"method",name:"boundingBox",static:!1,private:!1,access:{has:e=>"boundingBox"in e,get:e=>e.boundingBox},metadata:ue},null,le),zi(this,null,ne,{kind:"method",name:"boxModel",static:!1,private:!1,access:{has:e=>"boxModel"in e,get:e=>e.boxModel},metadata:ue},null,le),zi(this,null,se,{kind:"method",name:"screenshot",static:!1,private:!1,access:{has:e=>"screenshot"in e,get:e=>e.screenshot},metadata:ue},null,le),zi(this,null,ae,{kind:"method",name:"isIntersectingViewport",static:!1,private:!1,access:{has:e=>"isIntersectingViewport"in e,get:e=>e.isIntersectingViewport},metadata:ue},null,le),zi(this,null,oe,{kind:"method",name:"scrollIntoView",static:!1,private:!1,access:{has:e=>"scrollIntoView"in e,get:e=>e.scrollIntoView},metadata:ue},null,le),ue&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:ue})}isolatedHandle=Wi(this,le);static bindIsolatedHandle(e,t){return async function(...t){if(this.realm===this.frame.isolatedRealm())return await e.call(this,...t);let r;this.isolatedHandle?r=this.isolatedHandle:this.isolatedHandle=r=await this.frame.isolatedRealm().adoptHandle(this);const i=await e.call(r,...t);return i===r?this:i instanceof Mr?await this.realm.transferHandle(i):(Array.isArray(i)&&await Promise.all(i.map((async(e,t,r)=>{e instanceof Mr&&(r[t]=await this.realm.transferHandle(e))}))),i instanceof Map&&await Promise.all([...i.entries()].map((async([e,t])=>{t instanceof Mr&&i.set(e,await this.realm.transferHandle(t))}))),i)}}handle;constructor(e){super(),this.handle=e,this[Zr]=!0}get id(){return this.handle.id}get disposed(){return this.handle.disposed}async getProperty(e){return await this.handle.getProperty(e)}async getProperties(){return await this.handle.getProperties()}async evaluate(e,...t){return e=vt(this.evaluate.name,e),await this.handle.evaluate(e,...t)}async evaluateHandle(e,...t){return e=vt(this.evaluateHandle.name,e),await this.handle.evaluateHandle(e,...t)}async jsonValue(){return await this.handle.jsonValue()}toString(){return this.handle.toString()}remoteObject(){return this.handle.remoteObject()}dispose(){return this.handle.dispose()}asElement(){return this}async $(e){const{updatedSelector:t,QueryHandler:r}=Mi(e);return await r.queryOne(this,t)}async $$(e,t){return!1===t?.isolate?await this.#Kt(e):await this.#Vt(e)}get#Vt(){return q.value}async#Kt(e){const{updatedSelector:t,QueryHandler:r}=Mi(e);return await hi.collect(r.queryAll(this,t))}async $eval(e,t,...r){const i={stack:[],error:void 0,hasError:!1};try{t=vt(this.$eval.name,t);const n=Ui(i,await this.$(e),!1);if(!n)throw new Error(`Error: failed to find element matching selector "${e}"`);return await n.evaluate(t,...r)}catch(e){i.error=e,i.hasError=!0}finally{Gi(i)}}async $$eval(e,t,...r){const i={stack:[],error:void 0,hasError:!1};try{t=vt(this.$$eval.name,t);const n=await this.$$(e),s=Ui(i,await this.evaluateHandle(((e,...t)=>t),...n),!1),[a]=await Promise.all([s.evaluate(t,...r),...n.map((e=>e.dispose()))]);return a}catch(e){i.error=e,i.hasError=!0}finally{Gi(i)}}async waitForSelector(e,t={}){const{updatedSelector:r,QueryHandler:i,polling:n}=Mi(e);return await i.waitFor(this,r,{polling:n,...t})}async#Wt(e){return await this.evaluate((async(e,t,r)=>Boolean(t.checkVisibility(e,r))),ci.create((e=>e.puppeteerUtil)),e)}async isVisible(){return await this.#Wt(!0)}async isHidden(){return await this.#Wt(!1)}async toElement(e){const t=await this.evaluate(((e,t)=>e.nodeName===t.toUpperCase()),e);if(!t)throw new Error(`Element is not a(n) \`${e}\` element`);return this}async clickablePoint(e){const t=await this.#zt();if(!t)throw new Error("Node is either not clickable or not an Element");return void 0!==e?{x:t.x+e.x,y:t.y+e.y}:{x:t.x+t.width/2,y:t.y+t.height/2}}async hover(){await this.scrollIntoViewIfNeeded();const{x:e,y:t}=await this.clickablePoint();await this.frame.page().mouse.move(e,t)}async click(e={}){await this.scrollIntoViewIfNeeded();const{x:t,y:r}=await this.clickablePoint(e.offset);await this.frame.page().mouse.click(t,r,e)}async drag(e){await this.scrollIntoViewIfNeeded();const t=this.frame.page();if(t.isDragInterceptionEnabled()){const r=await this.clickablePoint();return e instanceof de&&(e=await e.clickablePoint()),await t.mouse.drag(r,e)}try{t._isDragging||(t._isDragging=!0,await this.hover(),await t.mouse.down()),e instanceof de?await e.hover():await t.mouse.move(e.x,e.y)}catch(e){throw t._isDragging=!1,e}}async dragEnter(e={items:[],dragOperationsMask:1}){const t=this.frame.page();await this.scrollIntoViewIfNeeded();const r=await this.clickablePoint();await t.mouse.dragEnter(r,e)}async dragOver(e={items:[],dragOperationsMask:1}){const t=this.frame.page();await this.scrollIntoViewIfNeeded();const r=await this.clickablePoint();await t.mouse.dragOver(r,e)}async drop(e={items:[],dragOperationsMask:1}){const t=this.frame.page();if("items"in e){await this.scrollIntoViewIfNeeded();const r=await this.clickablePoint();await t.mouse.drop(r,e)}else await e.drag(this),t._isDragging=!1,await t.mouse.up()}async dragAndDrop(e,t){const r=this.frame.page();nt(r.isDragInterceptionEnabled(),"Drag Interception is not enabled!"),await this.scrollIntoViewIfNeeded();const i=await this.clickablePoint(),n=await e.clickablePoint();await r.mouse.dragAndDrop(i,n,t)}async select(...e){for(const t of e)nt(bt(t),'Values must be strings. Found value "'+t+'" of type "'+typeof t+'"');return await this.evaluate(((e,t)=>{const r=new Set(t);if(!(e instanceof HTMLSelectElement))throw new Error("Element is not a <select> element.");const i=new Set;if(e.multiple)for(const t of e.options)t.selected=r.has(t.value),t.selected&&i.add(t.value);else{for(const t of e.options)t.selected=!1;for(const t of e.options)if(r.has(t.value)){t.selected=!0,i.add(t.value);break}}return e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0})),[...i.values()]}),e)}async tap(){await this.scrollIntoViewIfNeeded();const{x:e,y:t}=await this.clickablePoint();await this.frame.page().touchscreen.tap(e,t)}async touchStart(){await this.scrollIntoViewIfNeeded();const{x:e,y:t}=await this.clickablePoint();await this.frame.page().touchscreen.touchStart(e,t)}async touchMove(){await this.scrollIntoViewIfNeeded();const{x:e,y:t}=await this.clickablePoint();await this.frame.page().touchscreen.touchMove(e,t)}async touchEnd(){await this.scrollIntoViewIfNeeded(),await this.frame.page().touchscreen.touchEnd()}async focus(){await this.evaluate((e=>{if(!(e instanceof HTMLElement))throw new Error("Cannot focus non-HTMLElement");return e.focus()}))}async type(e,t){await this.focus(),await this.frame.page().keyboard.type(e,t)}async press(e,t){await this.focus(),await this.frame.page().keyboard.press(e,t)}async#zt(){const e=await this.evaluate((e=>e instanceof Element?[...e.getClientRects()].map((e=>({x:e.x,y:e.y,width:e.width,height:e.height}))):null));if(!e?.length)return null;await this.#Ut(e);let t,r=this.frame;for(;t=r?.parentFrame();){const i={stack:[],error:void 0,hasError:!1};try{const n=Ui(i,await r.frameElement(),!1);if(!n)throw new Error("Unsupported frame type");const s=await n.evaluate((e=>{if(0===e.getClientRects().length)return null;const t=e.getBoundingClientRect(),r=window.getComputedStyle(e);return{left:t.left+parseInt(r.paddingLeft,10)+parseInt(r.borderLeftWidth,10),top:t.top+parseInt(r.paddingTop,10)+parseInt(r.borderTopWidth,10)}}));if(!s)return null;for(const t of e)t.x+=s.left,t.y+=s.top;await n.#Ut(e),r=t}catch(e){i.error=e,i.hasError=!0}finally{Gi(i)}}const i=e.find((e=>e.width>=1&&e.height>=1));return i?{x:i.x,y:i.y,height:i.height,width:i.width}:null}async#Ut(e){const{documentWidth:t,documentHeight:r}=await this.frame.isolatedRealm().evaluate((()=>({documentWidth:document.documentElement.clientWidth,documentHeight:document.documentElement.clientHeight})));for(const i of e)Xi(i,t,r)}async boundingBox(){const e=await this.evaluate((e=>{if(!(e instanceof Element))return null;if(0===e.getClientRects().length)return null;const t=e.getBoundingClientRect();return{x:t.x,y:t.y,width:t.width,height:t.height}}));if(!e)return null;const t=await this.#Gt();return t?{x:e.x+t.x,y:e.y+t.y,height:e.height,width:e.width}:null}async boxModel(){const e=await this.evaluate((e=>{if(!(e instanceof Element))return null;if(0===e.getClientRects().length)return null;const t=e.getBoundingClientRect(),r=window.getComputedStyle(e),i={padding:{left:parseInt(r.paddingLeft,10),top:parseInt(r.paddingTop,10),right:parseInt(r.paddingRight,10),bottom:parseInt(r.paddingBottom,10)},margin:{left:-parseInt(r.marginLeft,10),top:-parseInt(r.marginTop,10),right:-parseInt(r.marginRight,10),bottom:-parseInt(r.marginBottom,10)},border:{left:parseInt(r.borderLeft,10),top:parseInt(r.borderTop,10),right:parseInt(r.borderRight,10),bottom:parseInt(r.borderBottom,10)}},n=[{x:t.left,y:t.top},{x:t.left+t.width,y:t.top},{x:t.left+t.width,y:t.top+t.bottom},{x:t.left,y:t.top+t.bottom}],s=a(n,i.border);return{content:a(s,i.padding),padding:s,border:n,margin:a(n,i.margin),width:t.width,height:t.height};function a(e,t){return[{x:e[0].x+t.left,y:e[0].y+t.top},{x:e[1].x-t.right,y:e[1].y+t.top},{x:e[2].x-t.right,y:e[2].y-t.bottom},{x:e[3].x+t.left,y:e[3].y-t.bottom}]}}));if(!e)return null;const t=await this.#Gt();if(!t)return null;for(const r of["content","padding","border","margin"])for(const i of e[r])i.x+=t.x,i.y+=t.y;return e}async#Gt(){const e={x:0,y:0};let t,r=this.frame;for(;t=r?.parentFrame();){const i={stack:[],error:void 0,hasError:!1};try{const n=Ui(i,await r.frameElement(),!1);if(!n)throw new Error("Unsupported frame type");const s=await n.evaluate((e=>{if(0===e.getClientRects().length)return null;const t=e.getBoundingClientRect(),r=window.getComputedStyle(e);return{left:t.left+parseInt(r.paddingLeft,10)+parseInt(r.borderLeftWidth,10),top:t.top+parseInt(r.paddingTop,10)+parseInt(r.borderTopWidth,10)}}));if(!s)return null;e.x+=s.left,e.y+=s.top,r=t}catch(e){i.error=e,i.hasError=!0}finally{Gi(i)}}return e}async screenshot(e={}){const{scrollIntoView:t=!0,clip:r}=e,i=this.frame.page();t&&await this.scrollIntoViewIfNeeded();const n=await this.#Qt(),[s,a]=await this.evaluate((()=>{if(!window.visualViewport)throw new Error("window.visualViewport is not supported.");return[window.visualViewport.pageLeft,window.visualViewport.pageTop]}));return n.x+=s,n.y+=a,r&&(n.x+=r.x,n.y+=r.y,n.height=r.height,n.width=r.width),await i.screenshot({...e,clip:n})}async#Qt(){const e=await this.boundingBox();return nt(e,"Node is either not visible or not an HTMLElement"),nt(0!==e.width,"Node has 0 width."),nt(0!==e.height,"Node has 0 height."),e}async assertConnectedElement(){const e=await this.evaluate((async e=>e.isConnected?e.nodeType!==Node.ELEMENT_NODE?"Node is not of type HTMLElement":void 0:"Node is detached from document"));if(e)throw new Error(e)}async scrollIntoViewIfNeeded(){await this.isIntersectingViewport({threshold:1})||await this.scrollIntoView()}async isIntersectingViewport(e={}){const t={stack:[],error:void 0,hasError:!1};try{await this.assertConnectedElement();const r=await this.#Jt(),i=Ui(t,r&&await r.#Xt(),!1);return await(i??this).evaluate((async(e,t)=>{const r=await new Promise((t=>{const r=new IntersectionObserver((e=>{t(e[0].intersectionRatio),r.disconnect()}));r.observe(e)}));return 1===t?1===r:r>t}),e.threshold??0)}catch(e){t.error=e,t.hasError=!0}finally{Gi(t)}}async scrollIntoView(){await this.assertConnectedElement(),await this.evaluate((async e=>{e.scrollIntoView({block:"center",inline:"center",behavior:"instant"})}))}async#Jt(){return await this.evaluate((e=>e instanceof SVGElement))?this:null}async#Xt(){return await this.evaluateHandle((e=>e instanceof SVGSVGElement?e:e.ownerSVGElement))}}})();function Xi(e,t,r){e.width=Math.max(e.x>=0?Math.min(t-e.x,e.width):Math.min(t,e.width+e.x),0),e.height=Math.max(e.y>=0?Math.min(r-e.y,e.height):Math.min(r,e.height+e.y),0)}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */function Yi(e){let t,r;if(e.exception){if(!("object"===e.exception.type&&"error"===e.exception.subtype||e.exception.objectId))return en(e.exception);{const i=Zi(e);t=i.name,r=i.message}}else t="Error",r=e.text;const i=r.split("\n").length,n=new Error(r);n.name=t;const s=n.stack.split("\n"),a=s.splice(0,i);if(s.shift(),e.stackTrace&&s.length<Error.stackTraceLimit)for(const t of e.stackTrace.callFrames.reverse()){if(wt.isPuppeteerURL(t.url)&&t.url!==wt.INTERNAL_URL){const e=wt.parse(t.url);s.unshift(`    at ${t.functionName||e.functionName} (${e.functionName} at ${e.siteString}, <anonymous>:${t.lineNumber}:${t.columnNumber})`)}else s.push(`    at ${t.functionName||"<anonymous>"} (${t.url}:${t.lineNumber}:${t.columnNumber})`);if(s.length>=Error.stackTraceLimit)break}return n.stack=[...a,...s].join("\n"),n}const Zi=e=>{let t,r="";const i=e.exception?.description?.split("\n    at ")??[],n=Math.min(e.stackTrace?.callFrames.length??0,i.length-1);return i.splice(-n,n),e.exception?.className&&(r=e.exception.className),t=i.join("\n"),r&&t.startsWith(`${r}: `)&&(t=t.slice(r.length+2)),{message:t,name:r}};function en(e){if(nt(!e.objectId,"Cannot extract value when objectId is given"),e.unserializableValue){if("bigint"===e.type)return BigInt(e.unserializableValue.replace("n",""));switch(e.unserializableValue){case"-0":return-0;case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error("Unsupported unserializable value: "+e.unserializableValue)}}return e.value}function tn(e,t,r){globalThis[t]||Object.assign(globalThis,{[t](...i){const n=globalThis[t];n.args??=new Map,n.callbacks??=new Map;const s=(n.lastSeq??0)+1;return n.lastSeq=s,n.args.set(s,i),globalThis[r+t](JSON.stringify({type:e,name:t,seq:s,args:i,isTrivial:!i.some((e=>e instanceof Node))})),new Promise(((e,t)=>{n.callbacks.set(s,{resolve(t){n.args.delete(s),e(t)},reject(e){n.args.delete(s),t(e)}})}))}})}const rn="puppeteer_";
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class nn extends Mr{#e=!1;#Yt;#Zt;constructor(e,t){super(),this.#Zt=e,this.#Yt=t}get disposed(){return this.#e}get realm(){return this.#Zt}get client(){return this.realm.environment.client}async jsonValue(){if(!this.#Yt.objectId)return en(this.#Yt);const e=await this.evaluate((e=>e));if(void 0===e)throw new Error("Could not serialize referenced object");return e}asElement(){return null}async dispose(){this.#e||(this.#e=!0,await sn(this.client,this.#Yt))}toString(){if(!this.#Yt.objectId)return"JSHandle:"+en(this.#Yt);return"JSHandle@"+(this.#Yt.subtype||this.#Yt.type)}get id(){return this.#Yt.objectId}remoteObject(){return this.#Yt}async getProperties(){const e=await this.client.send("Runtime.getProperties",{objectId:this.#Yt.objectId,ownProperties:!0}),t=new Map;for(const r of e.result)r.enumerable&&r.value&&t.set(r.name,this.#Zt.createCdpHandle(r.value));return t}}async function sn(e,t){t.objectId&&await e.send("Runtime.releaseObject",{objectId:t.objectId}).catch((e=>{yt(e)}))}
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var an=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},on=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var m in i)f[m]="access"===m?{}:i[m];for(var m in i.access)f.access[m]=i.access[m];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var y=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===y)continue;if(null===y||"object"!=typeof y)throw new TypeError("Object expected");(o=a(y.get))&&(u.get=o),(o=a(y.set))&&(u.set=o),(o=a(y.init))&&n.unshift(o)}else(o=a(y))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0};const cn=new Set(["StaticText","InlineTextBox"]);let ln=(()=>{var e,t;let r,i,n,s,a=Ji,o=[];return class extends a{static{const c="function"==typeof Symbol&&Symbol.metadata?Object.create(a[Symbol.metadata]??null):void 0;r=[sr()],i=[sr(),(e=Ji).bindIsolatedHandle.bind(e)],n=[sr(),(t=Ji).bindIsolatedHandle.bind(t)],s=[sr()],on(this,null,r,{kind:"method",name:"contentFrame",static:!1,private:!1,access:{has:e=>"contentFrame"in e,get:e=>e.contentFrame},metadata:c},null,o),on(this,null,i,{kind:"method",name:"scrollIntoView",static:!1,private:!1,access:{has:e=>"scrollIntoView"in e,get:e=>e.scrollIntoView},metadata:c},null,o),on(this,null,n,{kind:"method",name:"uploadFile",static:!1,private:!1,access:{has:e=>"uploadFile"in e,get:e=>e.uploadFile},metadata:c},null,o),on(this,null,s,{kind:"method",name:"autofill",static:!1,private:!1,access:{has:e=>"autofill"in e,get:e=>e.autofill},metadata:c},null,o),c&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:c})}constructor(e,t){super(new nn(e,t)),an(this,o)}get realm(){return this.handle.realm}get client(){return this.handle.client}remoteObject(){return this.handle.remoteObject()}get#er(){return this.frame._frameManager}get frame(){return this.realm.environment}async contentFrame(){const e=await this.client.send("DOM.describeNode",{objectId:this.id});return"string"!=typeof e.node.frameId?null:this.#er.frame(e.node.frameId)}async scrollIntoView(){await this.assertConnectedElement();try{await this.client.send("DOM.scrollIntoViewIfNeeded",{objectId:this.id})}catch(e){yt(e),await super.scrollIntoView()}}async uploadFile(...e){const t=await this.evaluate((e=>e.multiple));let r;nt(e.length<=1||t,"Multiple file uploads only work with <input type=file multiple>");try{r=await import("path")}catch(e){if(e instanceof TypeError)throw new Error("JSHandle#uploadFile can only be used in Node-like environments.");throw e}const i=e.map((e=>r.win32.isAbsolute(e)||r.posix.isAbsolute(e)?e:r.resolve(e)));if(0===i.length)return void await this.evaluate((e=>{e.files=(new DataTransfer).files,e.dispatchEvent(new Event("input",{bubbles:!0,composed:!0})),e.dispatchEvent(new Event("change",{bubbles:!0}))}));const{node:{backendNodeId:n}}=await this.client.send("DOM.describeNode",{objectId:this.id});await this.client.send("DOM.setFileInputFiles",{objectId:this.id,files:i,backendNodeId:n})}async autofill(e){const t=(await this.client.send("DOM.describeNode",{objectId:this.handle.id})).node.backendNodeId,r=this.frame._id;await this.client.send("Autofill.trigger",{fieldId:t,frameId:r,card:e.creditCard})}async*queryAXTree(e,t){const{nodes:r}=await this.client.send("Accessibility.queryAXTree",{objectId:this.id,accessibleName:e,role:t}),i=r.filter((e=>!e.ignored&&(!!e.role&&!cn.has(e.role.value))));return yield*hi.map(i,(e=>this.realm.adoptBackendNode(e.backendDOMNodeId)))}}})();
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var dn=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},un=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});const hn=new qr("__ariaQuerySelector",mi.queryOne,""),pn=new qr("__ariaQuerySelectorAll",(async(e,t)=>{const r=mi.queryAll(e,t);return await e.realm.evaluateHandle(((...e)=>e),...await hi.collect(r))}),"");class fn extends it{#xe;#Zt;#E;#fe;#tr=new tt;constructor(e,t,r){super(),this.#xe=e,this.#Zt=r,this.#E=t.id,t.name&&(this.#fe=t.name);const i=this.#tr.use(new it(this.#xe));i.on("Runtime.bindingCalled",this.#rr.bind(this)),i.on("Runtime.executionContextDestroyed",(async e=>{e.executionContextId===this.#E&&this[Ze]()})),i.on("Runtime.executionContextsCleared",(async()=>{this[Ze]()})),i.on("Runtime.consoleAPICalled",this.#ir.bind(this)),i.on(Bt.Disconnected,(()=>{this[Ze]()}))}#nr=new Map;#g=new Ht;async#sr(e){const t={stack:[],error:void 0,hasError:!1};try{if(this.#nr.has(e.name))return;dn(t,await this.#g.acquire(),!1);try{await this.#xe.send("Runtime.addBinding",this.#fe?{name:rn+e.name,executionContextName:this.#fe}:{name:rn+e.name,executionContextId:this.#E}),await this.evaluate(tn,"internal",e.name,rn),this.#nr.set(e.name,e)}catch(e){if(e instanceof Error){if(e.message.includes("Execution context was destroyed"))return;if(e.message.includes("Cannot find context with specified id"))return}yt(e)}}catch(e){t.error=e,t.hasError=!0}finally{un(t)}}async#rr(e){if(e.executionContextId!==this.#E)return;let t;try{t=JSON.parse(e.payload)}catch{return}const{type:r,name:i,seq:n,args:s,isTrivial:a}=t;if("internal"===r)if(this.#nr.has(i))try{const e=this.#nr.get(i);await(e?.run(this,n,s,a))}catch(e){yt(e)}else this.emit("bindingcalled",e);else this.emit("bindingcalled",e)}get id(){return this.#E}#ir(e){e.executionContextId===this.#E&&this.emit("consoleapicalled",e)}#ar=!1;#or;get puppeteerUtil(){let e=Promise.resolve();return this.#ar||(e=Promise.all([this.#cr(hn),this.#cr(pn)]),this.#ar=!0),gi.inject((t=>{this.#or&&this.#or.then((e=>{e.dispose()})),this.#or=e.then((()=>this.evaluateHandle(t)))}),!this.#or),this.#or}async#cr(e){try{await this.#sr(e)}catch(e){yt(e)}}async evaluate(e,...t){return await this.#lr(!0,e,...t)}async evaluateHandle(e,...t){return await this.#lr(!1,e,...t)}async#lr(e,t,...r){const i=`//# sourceURL=${(e=>{if(Object.prototype.hasOwnProperty.call(e,gt))return e[gt]})(t)?.toString()??wt.INTERNAL_URL}`;if(bt(t)){const r=this.#E,n=t,s=_t.test(n)?n:`${n}\n${i}\n`,{exceptionDetails:a,result:o}=await this.#xe.send("Runtime.evaluate",{expression:s,contextId:r,returnByValue:e,awaitPromise:!0,userGesture:!0}).catch(mn);if(a)throw Yi(a);return e?en(o):this.#Zt.createCdpHandle(o)}const n=ti(t),s=_t.test(n)?n:`${n}\n${i}\n`;let a;try{a=this.#xe.send("Runtime.callFunctionOn",{functionDeclaration:s,executionContextId:this.#E,arguments:r.length?await Promise.all(r.map(async function(e){e instanceof ci&&(e=await e.get(this));if("bigint"==typeof e)return{unserializableValue:`${e.toString()}n`};if(Object.is(e,-0))return{unserializableValue:"-0"};if(Object.is(e,1/0))return{unserializableValue:"Infinity"};if(Object.is(e,-1/0))return{unserializableValue:"-Infinity"};if(Object.is(e,NaN))return{unserializableValue:"NaN"};const t=e&&(e instanceof nn||e instanceof ln)?e:null;if(t){if(t.realm!==this.#Zt)throw new Error("JSHandles can be evaluated only in the context they were created!");if(t.disposed)throw new Error("JSHandle is disposed!");return t.remoteObject().unserializableValue?{unserializableValue:t.remoteObject().unserializableValue}:t.remoteObject().objectId?{objectId:t.remoteObject().objectId}:{value:t.remoteObject().value}}return{value:e}}.bind(this))):[],returnByValue:e,awaitPromise:!0,userGesture:!0})}catch(e){throw e instanceof TypeError&&e.message.startsWith("Converting circular structure to JSON")&&(e.message+=" Recursive objects are not allowed."),e}const{exceptionDetails:o,result:c}=await a.catch(mn);if(o)throw Yi(o);return e?en(c):this.#Zt.createCdpHandle(c)}[Ze](){this.#tr.dispose(),this.emit("disposed",void 0)}}const mn=e=>{if(e.message.includes("Object reference chain is too long"))return{result:{type:"undefined"}};if(e.message.includes("Object couldn't be returned by value"))return{result:{type:"undefined"}};if(e.message.endsWith("Cannot find context with specified id")||e.message.endsWith("Inspected target navigated or closed"))throw new Error("Execution context was destroyed, most likely because of a navigation.");throw e};
/**
 * @license
 * Copyright 2018 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class yn{#dr;constructor(e){this.#dr=e}async snapshot(e={}){const{interestingOnly:t=!0,root:r=null}=e,{nodes:i}=await this.#dr.environment.client.send("Accessibility.getFullAXTree");let n;if(r){const{node:e}=await this.#dr.environment.client.send("DOM.describeNode",{objectId:r.id});n=e.backendNodeId}const s=gn.createTree(this.#dr,i);let a=s;if(n&&(a=s.find((e=>e.payload.backendDOMNodeId===n)),!a))return null;if(!t)return this.serializeTree(a)[0]??null;const o=new Set;return this.collectInterestingNodes(o,s,!1),o.has(a)?this.serializeTree(a,o)[0]??null:null}serializeTree(e,t){const r=[];for(const i of e.children)r.push(...this.serializeTree(i,t));if(t&&!t.has(e))return r;const i=e.serialize();return r.length&&(i.children=r),[i]}collectInterestingNodes(e,t,r){if(t.isInteresting(r)&&e.add(t),!t.isLeafNode()){r=r||t.isControl();for(const i of t.children)this.collectInterestingNodes(e,i,r)}}}class gn{payload;children=[];#ur=!1;#hr=!1;#pr=!1;#fr=!1;#fe;#mr;#yr;#gr;#dr;constructor(e,t){this.payload=t,this.#fe=this.payload.name?this.payload.name.value:"",this.#mr=this.payload.role?this.payload.role.value:"Unknown",this.#yr=this.payload.ignored,this.#dr=e;for(const e of this.payload.properties||[])"editable"===e.name&&(this.#ur="richtext"===e.value.value,this.#hr=!0),"focusable"===e.name&&(this.#pr=e.value.value),"hidden"===e.name&&(this.#fr=e.value.value)}#wr(){return!this.#ur&&(!!this.#hr||("textbox"===this.#mr||"searchbox"===this.#mr))}#vr(){const e=this.#mr;return"LineBreak"===e||"text"===e||"InlineTextBox"===e||"StaticText"===e}#br(){if(void 0===this.#gr){this.#gr=!1;for(const e of this.children)if(e.#pr||e.#br()){this.#gr=!0;break}}return this.#gr}find(e){if(e(this))return this;for(const t of this.children){const r=t.find(e);if(r)return r}return null}isLeafNode(){if(!this.children.length)return!0;if(this.#wr()||this.#vr())return!0;switch(this.#mr){case"doc-cover":case"graphics-symbol":case"img":case"image":case"Meter":case"scrollbar":case"slider":case"separator":case"progressbar":return!0}return!this.#br()&&(!(!this.#pr||!this.#fe)||!("heading"!==this.#mr||!this.#fe))}isControl(){switch(this.#mr){case"button":case"checkbox":case"ColorWell":case"combobox":case"DisclosureTriangle":case"listbox":case"menu":case"menubar":case"menuitem":case"menuitemcheckbox":case"menuitemradio":case"radio":case"scrollbar":case"searchbox":case"slider":case"spinbutton":case"switch":case"tab":case"textbox":case"tree":case"treeitem":return!0;default:return!1}}isInteresting(e){return"Ignored"!==this.#mr&&!this.#fr&&!this.#yr&&(!(!this.#pr&&!this.#ur)||(!!this.isControl()||!e&&(this.isLeafNode()&&!!this.#fe)))}serialize(){const e=new Map;for(const t of this.payload.properties||[])e.set(t.name.toLowerCase(),t.value.value);this.payload.name&&e.set("name",this.payload.name.value),this.payload.value&&e.set("value",this.payload.value.value),this.payload.description&&e.set("description",this.payload.description.value);const t={role:this.#mr,elementHandle:async()=>this.payload.backendDOMNodeId?await this.#dr.adoptBackendNode(this.payload.backendDOMNodeId):null},r=["name","value","description","keyshortcuts","roledescription","valuetext"];for(const n of r)e.has(n)&&(t[n]=(i=n,e.get(i)));var i;const n=["disabled","expanded","focused","modal","multiline","multiselectable","readonly","required","selected"],s=t=>e.get(t);for(const e of n){if("focused"===e&&"RootWebArea"===this.#mr)continue;s(e)&&(t[e]=s(e))}const a=["checked","pressed"];for(const r of a){if(!e.has(r))continue;const i=e.get(r);t[r]="mixed"===i?"mixed":"true"===i}const o=["level","valuemax","valuemin"],c=t=>e.get(t);for(const r of o)e.has(r)&&(t[r]=c(r));const l=["autocomplete","haspopup","invalid","orientation"],d=t=>e.get(t);for(const e of l){const r=d(e);r&&"false"!==r&&(t[e]=d(e))}return t}static createTree(e,t){const r=new Map;for(const i of t)r.set(i.nodeId,new gn(e,i));for(const e of r.values())for(const t of e.payload.childIds||[]){const i=r.get(t);i&&e.children.push(i)}return r.values().next().value}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var wn;!function(e){e.FrameAttached=Symbol("FrameManager.FrameAttached"),e.FrameNavigated=Symbol("FrameManager.FrameNavigated"),e.FrameDetached=Symbol("FrameManager.FrameDetached"),e.FrameSwapped=Symbol("FrameManager.FrameSwapped"),e.LifecycleEvent=Symbol("FrameManager.LifecycleEvent"),e.FrameNavigatedWithinDocument=Symbol("FrameManager.FrameNavigatedWithinDocument"),e.ConsoleApiCalled=Symbol("FrameManager.ConsoleApiCalled"),e.BindingCalled=Symbol("FrameManager.BindingCalled")}(wn||(wn={}));
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class vn{#Zt;#kr;#Sr;#me;#le;#be;#f;#Cr=jt.create();#Tr;#Er;#Ir=[];constructor(e,t,r,...i){if(this.#Zt=e,this.#kr=t.polling,this.#Sr=t.root,this.#Er=t.signal,this.#Er?.addEventListener("abort",(()=>{this.terminate(this.#Er?.reason)}),{once:!0}),"string"==typeof r)this.#me=`() => {return (${r});}`;else this.#me=ti(r);this.#le=i,this.#Zt.taskManager.add(this),t.timeout&&(this.#f=new ut(`Waiting failed: ${t.timeout}ms exceeded`),this.#be=setTimeout((()=>{this.terminate(this.#f)}),t.timeout)),this.rerun()}get result(){return this.#Cr.valueOrThrow()}async rerun(){for(const e of this.#Ir)e.abort();this.#Ir.length=0;const e=new AbortController;this.#Ir.push(e);try{switch(this.#kr){case"raf":this.#Tr=await this.#Zt.evaluateHandle((({RAFPoller:e,createFunction:t},r,...i)=>{const n=t(r);return new e((()=>n(...i)))}),ci.create((e=>e.puppeteerUtil)),this.#me,...this.#le);break;case"mutation":this.#Tr=await this.#Zt.evaluateHandle((({MutationPoller:e,createFunction:t},r,i,...n)=>{const s=t(i);return new e((()=>s(...n)),r||document)}),ci.create((e=>e.puppeteerUtil)),this.#Sr,this.#me,...this.#le);break;default:this.#Tr=await this.#Zt.evaluateHandle((({IntervalPoller:e,createFunction:t},r,i,...n)=>{const s=t(i);return new e((()=>s(...n)),r)}),ci.create((e=>e.puppeteerUtil)),this.#kr,this.#me,...this.#le)}await this.#Tr.evaluate((e=>{e.start()}));const e=await this.#Tr.evaluateHandle((e=>e.result()));this.#Cr.resolve(e),await this.terminate()}catch(t){if(e.signal.aborted)return;const r=this.getBadError(t);r&&await this.terminate(r)}}async terminate(e){if(this.#Zt.taskManager.delete(this),clearTimeout(this.#be),e&&!this.#Cr.finished()&&this.#Cr.reject(e),this.#Tr)try{await this.#Tr.evaluateHandle((async e=>{await e.stop()})),this.#Tr&&(await this.#Tr.dispose(),this.#Tr=void 0)}catch{}}getBadError(e){if(Gt(e)){if(e.message.includes("Execution context is not available in detached frame"))return new Error("Waiting failed: Frame detached");if(e.message.includes("Execution context was destroyed"))return;if(e.message.includes("Cannot find context with specified id"))return;if(e.message.includes("AbortError: Actor 'MessageHandlerFrame' destroyed"))return;return e}return new Error("WaitTask failed with an error",{cause:e})}}class bn{#xr=new Set;add(e){this.#xr.add(e)}delete(e){this.#xr.delete(e)}terminateAll(e){for(const t of this.#xr)t.terminate(e);this.#xr.clear()}async rerunAll(){await Promise.all([...this.#xr].map((e=>e.rerun())))}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class kn{timeoutSettings;taskManager=new bn;constructor(e){this.timeoutSettings=e}async waitForFunction(e,t={},...r){const{polling:i="raf",timeout:n=this.timeoutSettings.timeout(),root:s,signal:a}=t;if("number"==typeof i&&i<0)throw new Error("Cannot poll with non-positive interval");const o=new vn(this,{polling:i,root:s,timeout:n,signal:a},e,...r);return await o.result}get disposed(){return this.#e}#e=!1;dispose(){this.#e=!0,this.taskManager.terminateAll(new Error("waitForFunction failed: frame got detached."))}[Ze](){this.dispose()}}
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Sn extends kn{#Fr;#r=new it;#_r;constructor(e,t){super(t),this.#_r=e}get environment(){return this.#_r}get client(){return this.#_r.client}get emitter(){return this.#r}setContext(e){this.#Fr?.[Ze](),e.once("disposed",this.#Dr.bind(this)),e.on("consoleapicalled",this.#Pr.bind(this)),e.on("bindingcalled",this.#Rr.bind(this)),this.#Fr=e,this.#r.emit("context",e),this.taskManager.rerunAll()}#Dr(){this.#Fr=void 0,"clearDocumentHandle"in this.#_r&&this.#_r.clearDocumentHandle()}#Pr(e){this.#r.emit("consoleapicalled",e)}#Rr(e){this.#r.emit("bindingcalled",e)}hasContext(){return!!this.#Fr}get context(){return this.#Fr}#Mr(){if(this.disposed)throw new Error(`Execution context is not available in detached frame or worker "${this.environment.url()}" (are you trying to evaluate?)`);return this.#Fr}async#Ar(){return await Se(Mt(this.#r,"context").pipe(Je(Mt(this.#r,"disposed").pipe(Ce((()=>{throw new Error("Execution context was destroyed")}))),xt(this.timeoutSettings.timeout()))))}async evaluateHandle(e,...t){e=vt(this.evaluateHandle.name,e);let r=this.#Mr();return r||(r=await this.#Ar()),await r.evaluateHandle(e,...t)}async evaluate(e,...t){e=vt(this.evaluate.name,e);let r=this.#Mr();return r||(r=await this.#Ar()),await r.evaluate(e,...t)}async adoptBackendNode(e){let t=this.#Mr();t||(t=await this.#Ar());const{object:r}=await this.client.send("DOM.resolveNode",{backendNodeId:e,executionContextId:t.id});return this.createCdpHandle(r)}async adoptHandle(e){if(e.realm===this)return await e.evaluateHandle((e=>e));const t=await this.client.send("DOM.describeNode",{objectId:e.id});return await this.adoptBackendNode(t.node.backendNodeId)}async transferHandle(e){if(e.realm===this)return e;if(void 0===e.remoteObject().objectId)return e;const t=await this.client.send("DOM.describeNode",{objectId:e.remoteObject().objectId}),r=await this.adoptBackendNode(t.node.backendNodeId);return await e.dispose(),r}createCdpHandle(e){return"node"===e.subtype?new ln(this,e):new nn(this,e)}[Ze](){this.#Fr?.[Ze](),this.#r.emit("disposed",void 0),super[Ze](),this.#r.removeAllListeners()}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const Cn=Symbol("mainWorld"),Tn=Symbol("puppeteerWorld"),En=new Map([["load","load"],["domcontentloaded","DOMContentLoaded"],["networkidle0","networkIdle"],["networkidle2","networkAlmostIdle"]]);class In{#Or;#qr;#be;#Nr=null;#Pe=new tt;#Br;#Lr;#jr=jt.create();#Hr=jt.create();#$r=jt.create();#Kr;#Vr;#Wr;constructor(e,t,r,i){Array.isArray(r)?r=r.slice():"string"==typeof r&&(r=[r]),this.#Br=t._loaderId,this.#Or=r.map((e=>{const t=En.get(e);return nt(t,"Unknown value for options.waitUntil: "+e),t})),this.#qr=t,this.#be=i;this.#Pe.use(new it(t._frameManager)).on(wn.LifecycleEvent,this.#zr.bind(this));const n=this.#Pe.use(new it(t));n.on(Ai.FrameNavigatedWithinDocument,this.#Ur.bind(this)),n.on(Ai.FrameNavigated,this.#Gr.bind(this)),n.on(Ai.FrameSwapped,this.#Qr.bind(this)),n.on(Ai.FrameSwappedByActivation,this.#Qr.bind(this)),n.on(Ai.FrameDetached,this.#Jr.bind(this));const s=this.#Pe.use(new it(e));s.on(Fr.Request,this.#Xr.bind(this)),s.on(Fr.Response,this.#Yr.bind(this)),s.on(Fr.RequestFailed,this.#Zr.bind(this)),this.#Lr=jt.create({timeout:this.#be,message:`Navigation timeout of ${this.#be} ms exceeded`}),this.#zr()}#Xr(e){e.frame()===this.#qr&&e.isNavigationRequest()&&(this.#Nr=e,this.#Wr?.resolve(),this.#Wr=jt.create(),null!==e.response()&&this.#Wr?.resolve())}#Zr(e){this.#Nr?.id===e.id&&this.#Wr?.resolve()}#Yr(e){this.#Nr?.id===e.request().id&&this.#Wr?.resolve()}#Jr(e){this.#qr!==e?this.#zr():this.#Lr.resolve(new Error("Navigating frame was detached"))}async navigationResponse(){return await(this.#Wr?.valueOrThrow()),this.#Nr?this.#Nr.response():null}sameDocumentNavigationPromise(){return this.#jr.valueOrThrow()}newDocumentNavigationPromise(){return this.#$r.valueOrThrow()}lifecyclePromise(){return this.#Hr.valueOrThrow()}terminationPromise(){return this.#Lr.valueOrThrow()}#Ur(){this.#Kr=!0,this.#zr()}#Gr(e){if("BackForwardCacheRestore"===e)return this.#Qr();this.#zr()}#Qr(){this.#Vr=!0,this.#zr()}#zr(){(function e(t,r){for(const e of r)if(!t._lifecycleEvents.has(e))return!1;for(const i of t.childFrames())if(i._hasStartedLoading&&!e(i,r))return!1;return!0})(this.#qr,this.#Or)&&(this.#Hr.resolve(),this.#Kr&&this.#jr.resolve(void 0),(this.#Vr||this.#qr._loaderId!==this.#Br)&&this.#$r.resolve(void 0))}dispose(){this.#Pe.dispose(),this.#Lr.resolve(new Error("LifecycleWatcher disposed"))}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var xn=self&&self.__runInitializers||function(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0},Fn=self&&self.__esDecorate||function(e,t,r,i,n,s){function a(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var o,c=i.kind,l="getter"===c?"get":"setter"===c?"set":"value",d=!t&&e?i.static?e:e.prototype:null,u=t||(d?Object.getOwnPropertyDescriptor(d,i.name):{}),h=!1,p=r.length-1;p>=0;p--){var f={};for(var m in i)f[m]="access"===m?{}:i[m];for(var m in i.access)f.access[m]=i.access[m];f.addInitializer=function(e){if(h)throw new TypeError("Cannot add initializers after decoration has completed");s.push(a(e||null))};var y=(0,r[p])("accessor"===c?{get:u.get,set:u.set}:u[l],f);if("accessor"===c){if(void 0===y)continue;if(null===y||"object"!=typeof y)throw new TypeError("Object expected");(o=a(y.get))&&(u.get=o),(o=a(y.set))&&(u.set=o),(o=a(y.init))&&n.unshift(o)}else(o=a(y))&&("field"===c?n.unshift(o):u[l]=o)}d&&Object.defineProperty(d,i.name,u),h=!0};let _n=(()=>{let e,t,r,i,n,s,a,o=ji,c=[];return class extends o{static{const l="function"==typeof Symbol&&Symbol.metadata?Object.create(o[Symbol.metadata]??null):void 0;Fn(this,null,e,{kind:"method",name:"goto",static:!1,private:!1,access:{has:e=>"goto"in e,get:e=>e.goto},metadata:l},null,c),Fn(this,null,t,{kind:"method",name:"waitForNavigation",static:!1,private:!1,access:{has:e=>"waitForNavigation"in e,get:e=>e.waitForNavigation},metadata:l},null,c),Fn(this,null,r,{kind:"method",name:"setContent",static:!1,private:!1,access:{has:e=>"setContent"in e,get:e=>e.setContent},metadata:l},null,c),Fn(this,null,i,{kind:"method",name:"addPreloadScript",static:!1,private:!1,access:{has:e=>"addPreloadScript"in e,get:e=>e.addPreloadScript},metadata:l},null,c),Fn(this,null,n,{kind:"method",name:"addExposedFunctionBinding",static:!1,private:!1,access:{has:e=>"addExposedFunctionBinding"in e,get:e=>e.addExposedFunctionBinding},metadata:l},null,c),Fn(this,null,s,{kind:"method",name:"removeExposedFunctionBinding",static:!1,private:!1,access:{has:e=>"removeExposedFunctionBinding"in e,get:e=>e.removeExposedFunctionBinding},metadata:l},null,c),Fn(this,null,a,{kind:"method",name:"waitForDevicePrompt",static:!1,private:!1,access:{has:e=>"waitForDevicePrompt"in e,get:e=>e.waitForDevicePrompt},metadata:l},null,c),l&&Object.defineProperty(this,Symbol.metadata,{enumerable:!0,configurable:!0,writable:!0,value:l})}#ge=(xn(this,c),"");#ei=!1;#xe;_frameManager;_loaderId="";_lifecycleEvents=new Set;_id;_parentId;accessibility;worlds;constructor(e,t,r,i){super(),this._frameManager=e,this.#ge="",this._id=t,this._parentId=r,this.#ei=!1,this.#xe=i,this._loaderId="",this.worlds={[Cn]:new Sn(this,this._frameManager.timeoutSettings),[Tn]:new Sn(this,this._frameManager.timeoutSettings)},this.accessibility=new yn(this.worlds[Cn]),this.on(Ai.FrameSwappedByActivation,(()=>{this._onLoadingStarted(),this._onLoadingStopped()})),this.worlds[Cn].emitter.on("consoleapicalled",this.#ti.bind(this)),this.worlds[Cn].emitter.on("bindingcalled",this.#ri.bind(this))}#ti(e){this._frameManager.emit(wn.ConsoleApiCalled,[this.worlds[Cn],e])}#ri(e){this._frameManager.emit(wn.BindingCalled,[this.worlds[Cn],e])}_client(){return this.#xe}updateId(e){this._id=e}updateClient(e){this.#xe=e}page(){return this._frameManager.page()}isOOPFrame(){return this.#xe!==this._frameManager.client}async goto(e,t={}){const{referer:r=this._frameManager.networkManager.extraHTTPHeaders().referer,referrerPolicy:i=this._frameManager.networkManager.extraHTTPHeaders()["referer-policy"],waitUntil:n=["load"],timeout:s=this._frameManager.timeoutSettings.navigationTimeout()}=t;let a=!1;const o=new In(this._frameManager.networkManager,this,n,s);let c=await jt.race([async function(e,t,r,i,n){try{const s=await e.send("Page.navigate",{url:t,referrer:r,frameId:n,referrerPolicy:i});return a=!!s.loaderId,"net::ERR_HTTP_RESPONSE_CODE_FAILURE"===s.errorText?null:s.errorText?new Error(`${s.errorText} at ${t}`):null}catch(e){if(Gt(e))return e;throw e}}(this.#xe,e,r,i,this._id),o.terminationPromise()]);c||(c=await jt.race([o.terminationPromise(),a?o.newDocumentNavigationPromise():o.sameDocumentNavigationPromise()]));try{if(c)throw c;return await o.navigationResponse()}finally{o.dispose()}}async waitForNavigation(e={}){const{waitUntil:t=["load"],timeout:r=this._frameManager.timeoutSettings.navigationTimeout()}=e,i=new In(this._frameManager.networkManager,this,t,r),n=await jt.race([i.terminationPromise(),...e.ignoreSameDocumentNavigation?[]:[i.sameDocumentNavigationPromise()],i.newDocumentNavigationPromise()]);try{if(n)throw n;const e=await jt.race([i.terminationPromise(),i.navigationResponse()]);if(e instanceof Error)throw n;return e||null}finally{i.dispose()}}get client(){return this.#xe}mainRealm(){return this.worlds[Cn]}isolatedRealm(){return this.worlds[Tn]}async setContent(e,t={}){const{waitUntil:r=["load"],timeout:i=this._frameManager.timeoutSettings.navigationTimeout()}=t;await this.setFrameContent(e);const n=new In(this._frameManager.networkManager,this,r,i),s=await jt.race([n.terminationPromise(),n.lifecyclePromise()]);if(n.dispose(),s)throw s}url(){return this.#ge}parentFrame(){return this._frameManager._frameTree.parentFrame(this._id)||null}childFrames(){return this._frameManager._frameTree.childFrames(this._id)}#ii(){const e=this.page().mainFrame();return this.isOOPFrame()||null===e?this._frameManager._deviceRequestPromptManager(this.#xe):e._frameManager._deviceRequestPromptManager(this.#xe)}async addPreloadScript(e){if(!this.isOOPFrame()&&this!==this._frameManager.mainFrame())return;if(e.getIdForFrame(this))return;const{identifier:t}=await this.#xe.send("Page.addScriptToEvaluateOnNewDocument",{source:e.source});e.setIdForFrame(this,t)}async addExposedFunctionBinding(e){(this===this._frameManager.mainFrame()||this._hasStartedLoading)&&await Promise.all([this.#xe.send("Runtime.addBinding",{name:rn+e.name}),this.evaluate(e.initSource).catch(yt)])}async removeExposedFunctionBinding(e){(this===this._frameManager.mainFrame()||this._hasStartedLoading)&&await Promise.all([this.#xe.send("Runtime.removeBinding",{name:rn+e.name}),this.evaluate((e=>{globalThis[e]=void 0}),e.name).catch(yt)])}async waitForDevicePrompt(e={}){return await this.#ii().waitForDevicePrompt(e)}_navigated(e){this._name=e.name,this.#ge=`${e.url}${e.urlFragment||""}`}_navigatedWithinDocument(e){this.#ge=e}_onLifecycleEvent(e,t){"init"===t&&(this._loaderId=e,this._lifecycleEvents.clear()),this._lifecycleEvents.add(t)}_onLoadingStopped(){this._lifecycleEvents.add("DOMContentLoaded"),this._lifecycleEvents.add("load")}_onLoadingStarted(){this._hasStartedLoading=!0}get detached(){return this.#ei}[(e=[Li],t=[Li],r=[Li],i=[Li],n=[Li],s=[Li],a=[Li],Ze)](){this.#ei||(this.#ei=!0,this.worlds[Cn][Ze](),this.worlds[Tn][Ze]())}exposeFunction(){throw new pt}}})();
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Dn{#ni=new Map;#si=new Map;#ai=new Map;#oi;#ci=!1;#li=new Map;getMainFrame(){return this.#oi}getById(e){return this.#ni.get(e)}waitForFrame(e){const t=this.getById(e);if(t)return Promise.resolve(t);const r=jt.create();return(this.#li.get(e)||new Set).add(r),r.valueOrThrow()}frames(){return Array.from(this.#ni.values())}addFrame(e){this.#ni.set(e._id,e),e._parentId?(this.#si.set(e._id,e._parentId),this.#ai.has(e._parentId)||this.#ai.set(e._parentId,new Set),this.#ai.get(e._parentId).add(e._id)):this.#oi&&!this.#ci||(this.#oi=e,this.#ci=!1),this.#li.get(e._id)?.forEach((t=>t.resolve(e)))}removeFrame(e){this.#ni.delete(e._id),this.#si.delete(e._id),e._parentId?this.#ai.get(e._parentId)?.delete(e._id):this.#ci=!0}childFrames(e){const t=this.#ai.get(e);return t?Array.from(t).map((e=>this.getById(e))).filter((e=>void 0!==e)):[]}parentFrame(e){const t=this.#si.get(e);return t?this.getById(t):void 0}}class Pn{_interceptionId;_failureText=null;_response=null;_fromMemoryCache=!1;_redirectChain=[];interception={enabled:!1,handled:!1,handlers:[],resolutionState:{action:Rn.None},requestOverrides:{},response:null,abortReason:null};constructor(){}continueRequestOverrides(){return nt(this.interception.enabled,"Request Interception is not enabled!"),this.interception.requestOverrides}responseForRequest(){return nt(this.interception.enabled,"Request Interception is not enabled!"),this.interception.response}abortErrorReason(){return nt(this.interception.enabled,"Request Interception is not enabled!"),this.interception.abortReason}interceptResolutionState(){return this.interception.enabled?this.interception.handled?{action:Rn.AlreadyHandled}:{...this.interception.resolutionState}:{action:Rn.Disabled}}isInterceptResolutionHandled(){return this.interception.handled}enqueueInterceptAction(e){this.interception.handlers.push(e)}async finalizeInterceptions(){await this.interception.handlers.reduce(((e,t)=>e.then(t)),Promise.resolve()),this.interception.handlers=[];const{action:e}=this.interceptResolutionState();switch(e){case"abort":return await this._abort(this.interception.abortReason);case"respond":if(null===this.interception.response)throw new Error("Response is missing for the interception");return await this._respond(this.interception.response);case"continue":return await this._continue(this.interception.requestOverrides)}}async continue(e={},t){if(!this.url().startsWith("data:")){if(nt(this.interception.enabled,"Request Interception is not enabled!"),nt(!this.interception.handled,"Request is already handled!"),void 0===t)return await this._continue(e);if(this.interception.requestOverrides=e,void 0===this.interception.resolutionState.priority||t>this.interception.resolutionState.priority)this.interception.resolutionState={action:Rn.Continue,priority:t};else if(t===this.interception.resolutionState.priority){if("abort"===this.interception.resolutionState.action||"respond"===this.interception.resolutionState.action)return;this.interception.resolutionState.action=Rn.Continue}}}async respond(e,t){if(!this.url().startsWith("data:")){if(nt(this.interception.enabled,"Request Interception is not enabled!"),nt(!this.interception.handled,"Request is already handled!"),void 0===t)return await this._respond(e);if(this.interception.response=e,void 0===this.interception.resolutionState.priority||t>this.interception.resolutionState.priority)this.interception.resolutionState={action:Rn.Respond,priority:t};else if(t===this.interception.resolutionState.priority){if("abort"===this.interception.resolutionState.action)return;this.interception.resolutionState.action=Rn.Respond}}}async abort(e="failed",t){if(this.url().startsWith("data:"))return;const r=On[e];if(nt(r,"Unknown error code: "+e),nt(this.interception.enabled,"Request Interception is not enabled!"),nt(!this.interception.handled,"Request is already handled!"),void 0===t)return await this._abort(r);this.interception.abortReason=r,(void 0===this.interception.resolutionState.priority||t>=this.interception.resolutionState.priority)&&(this.interception.resolutionState={action:Rn.Abort,priority:t})}static getResponse(e){const t=bt(e)?(new TextEncoder).encode(e):e,r=[];for(const e of t)r.push(String.fromCharCode(e));return{contentLength:t.byteLength,base64:btoa(r.join(""))}}}var Rn;function Mn(e){const t=[];for(const r in e){const i=e[r];if(!Object.is(i,void 0)){const e=Array.isArray(i)?i:[i];t.push(...e.map((e=>({name:r,value:e+""}))))}}return t}!function(e){e.Abort="abort",e.Respond="respond",e.Continue="continue",e.Disabled="disabled",e.None="none",e.AlreadyHandled="already-handled"}(Rn||(Rn={}));const An={100:"Continue",101:"Switching Protocols",102:"Processing",103:"Early Hints",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",306:"Switch Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Too Early",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",510:"Not Extended",511:"Network Authentication Required"},On={aborted:"Aborted",accessdenied:"AccessDenied",addressunreachable:"AddressUnreachable",blockedbyclient:"BlockedByClient",blockedbyresponse:"BlockedByResponse",connectionaborted:"ConnectionAborted",connectionclosed:"ConnectionClosed",connectionfailed:"ConnectionFailed",connectionrefused:"ConnectionRefused",connectionreset:"ConnectionReset",internetdisconnected:"InternetDisconnected",namenotresolved:"NameNotResolved",timedout:"TimedOut",failed:"Failed"};function qn(e){if(e.originalMessage.includes("Invalid header")||e.originalMessage.includes('Expected "header"')||e.originalMessage.includes("invalid argument"))throw e;yt(e)}class Nn extends Pn{id;#xe;#di;#ge;#ui;#hi;#pi=!1;#fi;#mi={};#qr;#yi;get client(){return this.#xe}constructor(e,t,r,i,n,s){super(),this.#xe=e,this.id=n.requestId,this.#di=n.requestId===n.loaderId&&"Document"===n.type,this._interceptionId=r,this.#ge=n.request.url,this.#ui=(n.type||"other").toLowerCase(),this.#hi=n.request.method,this.#fi=n.request.postData,this.#pi=n.request.hasPostData??!1,this.#qr=t,this._redirectChain=s,this.#yi=n.initiator,this.interception.enabled=i;for(const[e,t]of Object.entries(n.request.headers))this.#mi[e.toLowerCase()]=t}url(){return this.#ge}resourceType(){return this.#ui}method(){return this.#hi}postData(){return this.#fi}hasPostData(){return this.#pi}async fetchPostData(){try{return(await this.#xe.send("Network.getRequestPostData",{requestId:this.id})).postData}catch(e){return void yt(e)}}headers(){return this.#mi}response(){return this._response}frame(){return this.#qr}isNavigationRequest(){return this.#di}initiator(){return this.#yi}redirectChain(){return this._redirectChain.slice()}failure(){return this._failureText?{errorText:this._failureText}:null}async _continue(e={}){const{url:t,method:r,postData:i,headers:n}=e;this.interception.handled=!0;const s=i?btoa(i):void 0;if(void 0===this._interceptionId)throw new Error("HTTPRequest is missing _interceptionId needed for Fetch.continueRequest");await this.#xe.send("Fetch.continueRequest",{requestId:this._interceptionId,url:t,method:r,postData:s,headers:n?Mn(n):void 0}).catch((e=>(this.interception.handled=!1,qn(e))))}async _respond(e){let t;this.interception.handled=!0,e.body&&(t=Pn.getResponse(e.body));const r={};if(e.headers)for(const t of Object.keys(e.headers)){const i=e.headers[t];r[t.toLowerCase()]=Array.isArray(i)?i.map((e=>String(e))):String(i)}e.contentType&&(r["content-type"]=e.contentType),t?.contentLength&&!("content-length"in r)&&(r["content-length"]=String(t.contentLength));const i=e.status||200;if(void 0===this._interceptionId)throw new Error("HTTPRequest is missing _interceptionId needed for Fetch.fulfillRequest");await this.#xe.send("Fetch.fulfillRequest",{requestId:this._interceptionId,responseCode:i,responsePhrase:An[i],responseHeaders:Mn(r),body:t?.base64}).catch((e=>(this.interception.handled=!1,qn(e))))}async _abort(e){if(this.interception.handled=!0,void 0===this._interceptionId)throw new Error("HTTPRequest is missing _interceptionId needed for Fetch.failRequest");await this.#xe.send("Fetch.failRequest",{requestId:this._interceptionId,errorReason:e||"Failed"}).catch(qn)}}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Bn{constructor(){}ok(){const e=this.status();return 0===e||e>=200&&e<=299}async text(){return(await this.buffer()).toString("utf8")}async json(){const e=await this.text();return JSON.parse(e)}}
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Ln{#gi;#wi;#vi;#bi;#ki;#Si;constructor(e){this.#gi=e.subjectName,this.#wi=e.issuer,this.#vi=e.validFrom,this.#bi=e.validTo,this.#ki=e.protocol,this.#Si=e.sanList}issuer(){return this.#wi}validFrom(){return this.#vi}validTo(){return this.#bi}protocol(){return this.#ki}subjectName(){return this.#gi}subjectAlternativeNames(){return this.#Si}}class jn extends Bn{#xe;#Ci;#Ti=null;#Ei=jt.create();#Ii;#xi;#Fi;#ge;#_i;#Di;#mi={};#Pi;#Ri;constructor(e,t,r,i){super(),this.#xe=e,this.#Ci=t,this.#Ii={ip:r.remoteIPAddress,port:r.remotePort},this.#Fi=this.#Mi(i)||r.statusText,this.#ge=t.url(),this.#_i=!!r.fromDiskCache,this.#Di=!!r.fromServiceWorker,this.#xi=i?i.statusCode:r.status;const n=i?i.headers:r.headers;for(const[e,t]of Object.entries(n))this.#mi[e.toLowerCase()]=t;this.#Pi=r.securityDetails?new Ln(r.securityDetails):null,this.#Ri=r.timing||null}#Mi(e){if(!e||!e.headersText)return;const t=e.headersText.split("\r",1)[0];if(!t)return;const r=t.match(/[^ ]* [^ ]* (.*)/);if(!r)return;const i=r[1];return i||void 0}_resolveBody(e){return e?this.#Ei.reject(e):this.#Ei.resolve()}remoteAddress(){return this.#Ii}url(){return this.#ge}status(){return this.#xi}statusText(){return this.#Fi}headers(){return this.#mi}securityDetails(){return this.#Pi}timing(){return this.#Ri}buffer(){return this.#Ti||(this.#Ti=this.#Ei.valueOrThrow().then((async()=>{try{const e=await this.#xe.send("Network.getResponseBody",{requestId:this.#Ci.id});return Buffer.from(e.body,e.base64Encoded?"base64":"utf8")}catch(e){if(e instanceof ht&&"No resource with given identifier found"===e.originalMessage)throw new ht("Could not load body for this request. This might happen if the request is a preflight request.");throw e}}))),this.#Ti}request(){return this.#Ci}fromCache(){return this.#_i||this.#Ci._fromMemoryCache}fromServiceWorker(){return this.#Di}frame(){return this.#Ci.frame()}}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Hn{#Ai=new Map;#Oi=new Map;#qi=new Map;#Ni=new Map;#Bi=new Map;#Li=new Map;forget(e){this.#Ai.delete(e),this.#Oi.delete(e),this.#Li.delete(e),this.#Bi.delete(e),this.#Ni.delete(e)}responseExtraInfo(e){return this.#Ni.has(e)||this.#Ni.set(e,[]),this.#Ni.get(e)}queuedRedirectInfo(e){return this.#Bi.has(e)||this.#Bi.set(e,[]),this.#Bi.get(e)}queueRedirectInfo(e,t){this.queuedRedirectInfo(e).push(t)}takeQueuedRedirectInfo(e){return this.queuedRedirectInfo(e).shift()}inFlightRequestsCount(){let e=0;for(const t of this.#qi.values())t.response()||e++;return e}storeRequestWillBeSent(e,t){this.#Ai.set(e,t)}getRequestWillBeSent(e){return this.#Ai.get(e)}forgetRequestWillBeSent(e){this.#Ai.delete(e)}getRequestPaused(e){return this.#Oi.get(e)}forgetRequestPaused(e){this.#Oi.delete(e)}storeRequestPaused(e,t){this.#Oi.set(e,t)}getRequest(e){return this.#qi.get(e)}storeRequest(e,t){this.#qi.set(e,t)}forgetRequest(e){this.#qi.delete(e)}getQueuedEventGroup(e){return this.#Li.get(e)}queueEventGroup(e,t){this.#Li.set(e,t)}forgetQueuedEventGroup(e){this.#Li.delete(e)}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class $n extends it{#er;#ji=new Hn;#Hi;#$i=null;#Ki=new Set;#Vi=!1;#Wi=!1;#zi;#Ui;#Gi;#Qi;#i=[["Fetch.requestPaused",this.#Ji],["Fetch.authRequired",this.#Xi],["Network.requestWillBeSent",this.#Yi],["Network.requestServedFromCache",this.#Zi],["Network.responseReceived",this.#en],["Network.loadingFinished",this.#tn],["Network.loadingFailed",this.#rn],["Network.responseReceivedExtraInfo",this.#in],[Bt.Disconnected,this.#nn]];#sn=new Map;constructor(e){super(),this.#er=e}async addClient(e){if(this.#sn.has(e))return;const t=new tt;this.#sn.set(e,t);const r=t.use(new it(e));for(const[t,i]of this.#i)r.on(t,(t=>i.bind(this)(e,t)));await Promise.all([e.send("Network.enable"),this.#an(e),this.#on(e),this.#cn(e),this.#ln(e),this.#dn(e)])}async#nn(e){this.#sn.get(e)?.dispose(),this.#sn.delete(e)}async authenticate(e){this.#$i=e;const t=this.#Vi||!!this.#$i;t!==this.#Wi&&(this.#Wi=t,await this.#un(this.#ln.bind(this)))}async setExtraHTTPHeaders(e){const t={};for(const[r,i]of Object.entries(e))nt(bt(i),`Expected value of header "${r}" to be String, but "${typeof i}" is found.`),t[r.toLowerCase()]=i;this.#Hi=t,await this.#un(this.#an.bind(this))}async#an(e){void 0!==this.#Hi&&await e.send("Network.setExtraHTTPHeaders",{headers:this.#Hi})}extraHTTPHeaders(){return Object.assign({},this.#Hi)}inFlightRequestsCount(){return this.#ji.inFlightRequestsCount()}async setOfflineMode(e){this.#Ui||(this.#Ui={offline:!1,upload:-1,download:-1,latency:0}),this.#Ui.offline=e,await this.#un(this.#on.bind(this))}async emulateNetworkConditions(e){this.#Ui||(this.#Ui={offline:!1,upload:-1,download:-1,latency:0}),this.#Ui.upload=e?e.upload:-1,this.#Ui.download=e?e.download:-1,this.#Ui.latency=e?e.latency:0,await this.#un(this.#on.bind(this))}async#un(e){await Promise.all(Array.from(this.#sn.keys()).map((t=>e(t))))}async#on(e){void 0!==this.#Ui&&await e.send("Network.emulateNetworkConditions",{offline:this.#Ui.offline,latency:this.#Ui.latency,uploadThroughput:this.#Ui.upload,downloadThroughput:this.#Ui.download})}async setUserAgent(e,t){this.#Gi=e,this.#Qi=t,await this.#un(this.#dn.bind(this))}async#dn(e){void 0!==this.#Gi&&await e.send("Network.setUserAgentOverride",{userAgent:this.#Gi,userAgentMetadata:this.#Qi})}async setCacheEnabled(e){this.#zi=!e,await this.#un(this.#cn.bind(this))}async setRequestInterception(e){this.#Vi=e;const t=this.#Vi||!!this.#$i;t!==this.#Wi&&(this.#Wi=t,await this.#un(this.#ln.bind(this)))}async#ln(e){void 0===this.#zi&&(this.#zi=!1),this.#Wi?await Promise.all([this.#cn(e),e.send("Fetch.enable",{handleAuthRequests:!0,patterns:[{urlPattern:"*"}]})]):await Promise.all([this.#cn(e),e.send("Fetch.disable")])}async#cn(e){void 0!==this.#zi&&await e.send("Network.setCacheDisabled",{cacheDisabled:this.#zi})}#Yi(e,t){if(!this.#Vi||t.request.url.startsWith("data:"))this.#Xr(e,t,void 0);else{const{requestId:r}=t;this.#ji.storeRequestWillBeSent(r,t);const i=this.#ji.getRequestPaused(r);if(i){const{requestId:n}=i;this.#hn(t,i),this.#Xr(e,t,n),this.#ji.forgetRequestPaused(r)}}}#Xi(e,t){let r="Default";this.#Ki.has(t.requestId)?r="CancelAuth":this.#$i&&(r="ProvideCredentials",this.#Ki.add(t.requestId));const{username:i,password:n}=this.#$i||{username:void 0,password:void 0};e.send("Fetch.continueWithAuth",{requestId:t.requestId,authChallengeResponse:{response:r,username:i,password:n}}).catch(yt)}#Ji(e,t){!this.#Vi&&this.#Wi&&e.send("Fetch.continueRequest",{requestId:t.requestId}).catch(yt);const{networkId:r,requestId:i}=t;if(!r)return void this.#pn(e,t);const n=(()=>{const e=this.#ji.getRequestWillBeSent(r);if(!e||e.request.url===t.request.url&&e.request.method===t.request.method)return e;this.#ji.forgetRequestWillBeSent(r)})();n?(this.#hn(n,t),this.#Xr(e,n,i)):this.#ji.storeRequestPaused(r,t)}#hn(e,t){e.request.headers={...e.request.headers,...t.request.headers}}#pn(e,t){const r=t.frameId?this.#er.frame(t.frameId):null,i=new Nn(e,r,t.requestId,this.#Vi,t,[]);this.emit(Fr.Request,i),i.finalizeInterceptions()}#Xr(e,t,r){let i=[];if(t.redirectResponse){let n=null;if(t.redirectHasExtraInfo&&(n=this.#ji.responseExtraInfo(t.requestId).shift(),!n))return void this.#ji.queueRedirectInfo(t.requestId,{event:t,fetchRequestId:r});const s=this.#ji.getRequest(t.requestId);s&&(this.#fn(e,s,t.redirectResponse,n),i=s._redirectChain)}const n=t.frameId?this.#er.frame(t.frameId):null,s=new Nn(e,n,r,this.#Vi,t,i);this.#ji.storeRequest(t.requestId,s),this.emit(Fr.Request,s),s.finalizeInterceptions()}#Zi(e,t){const r=this.#ji.getRequest(t.requestId);r&&(r._fromMemoryCache=!0),this.emit(Fr.RequestServedFromCache,r)}#fn(e,t,r,i){const n=new jn(e,t,r,i);t._response=n,t._redirectChain.push(t),n._resolveBody(new Error("Response body is unavailable for redirect responses")),this.#mn(t,!1),this.emit(Fr.Response,n),this.emit(Fr.RequestFinished,t)}#yn(e,t,r){const i=this.#ji.getRequest(t.requestId);if(!i)return;this.#ji.responseExtraInfo(t.requestId).length&&yt(new Error("Unexpected extraInfo events for request "+t.requestId)),t.response.fromDiskCache&&(r=null);const n=new jn(e,i,t.response,r);i._response=n,this.emit(Fr.Response,n)}#en(e,t){const r=this.#ji.getRequest(t.requestId);let i=null;!r||r._fromMemoryCache||!t.hasExtraInfo||(i=this.#ji.responseExtraInfo(t.requestId).shift(),i)?this.#yn(e,t,i):this.#ji.queueEventGroup(t.requestId,{responseReceivedEvent:t})}#in(e,t){const r=this.#ji.takeQueuedRedirectInfo(t.requestId);if(r)return this.#ji.responseExtraInfo(t.requestId).push(t),void this.#Xr(e,r.event,r.fetchRequestId);const i=this.#ji.getQueuedEventGroup(t.requestId);if(i)return this.#ji.forgetQueuedEventGroup(t.requestId),this.#yn(e,i.responseReceivedEvent,t),i.loadingFinishedEvent&&this.#gn(i.loadingFinishedEvent),void(i.loadingFailedEvent&&this.#wn(i.loadingFailedEvent));this.#ji.responseExtraInfo(t.requestId).push(t)}#mn(e,t){const r=e.id,i=e._interceptionId;this.#ji.forgetRequest(r),void 0!==i&&this.#Ki.delete(i),t&&this.#ji.forget(r)}#tn(e,t){const r=this.#ji.getQueuedEventGroup(t.requestId);r?r.loadingFinishedEvent=t:this.#gn(t)}#gn(e){const t=this.#ji.getRequest(e.requestId);t&&(t.response()&&t.response()?._resolveBody(),this.#mn(t,!0),this.emit(Fr.RequestFinished,t))}#rn(e,t){const r=this.#ji.getQueuedEventGroup(t.requestId);r?r.loadingFailedEvent=t:this.#wn(t)}#wn(e){const t=this.#ji.getRequest(e.requestId);if(!t)return;t._failureText=e.errorText;const r=t.response();r&&r._resolveBody(),this.#mn(t,!0),this.emit(Fr.RequestFailed,t)}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Kn extends it{#vn;#bn;#Nt;#kn=new Set;#xe;#Sn=new Map;#nr=new Set;_frameTree=new Dn;#Cn=new Set;#Tn=new WeakMap;#En;get timeoutSettings(){return this.#Nt}get networkManager(){return this.#bn}get client(){return this.#xe}constructor(e,t,r){super(),this.#xe=e,this.#vn=t,this.#bn=new $n(this),this.#Nt=r,this.setupEventListeners(this.#xe),e.once(Bt.Disconnected,(()=>{this.#In().catch(yt)}))}async#In(){const e=this._frameTree.getMainFrame();if(!e)return;for(const t of e.childFrames())this.#xn(t);const t=jt.create({timeout:100,message:"Frame was not swapped"});e.once(Ai.FrameSwappedByActivation,(()=>{t.resolve()}));try{await t.valueOrThrow()}catch(t){this.#xn(e)}}async swapFrameTree(e){this.#xe=e,nt(this.#xe instanceof Zt,"CDPSession is not an instance of CDPSessionImpl.");const t=this._frameTree.getMainFrame();t&&(this.#Cn.add(this.#xe._target()._targetId),this._frameTree.removeFrame(t),t.updateId(this.#xe._target()._targetId),this._frameTree.addFrame(t),t.updateClient(e)),this.setupEventListeners(e),e.once(Bt.Disconnected,(()=>{this.#In().catch(yt)})),await this.initialize(e,t),await this.#bn.addClient(e),t&&t.emit(Ai.FrameSwappedByActivation,void 0)}async registerSpeculativeSession(e){await this.#bn.addClient(e)}setupEventListeners(e){e.on("Page.frameAttached",(async t=>{await(this.#En?.valueOrThrow()),this.#Fn(e,t.frameId,t.parentFrameId)})),e.on("Page.frameNavigated",(async e=>{this.#Cn.add(e.frame.id),await(this.#En?.valueOrThrow()),this.#_n(e.frame,e.type)})),e.on("Page.navigatedWithinDocument",(async e=>{await(this.#En?.valueOrThrow()),this.#Dn(e.frameId,e.url)})),e.on("Page.frameDetached",(async e=>{await(this.#En?.valueOrThrow()),this.#Jr(e.frameId,e.reason)})),e.on("Page.frameStartedLoading",(async e=>{await(this.#En?.valueOrThrow()),this.#Pn(e.frameId)})),e.on("Page.frameStoppedLoading",(async e=>{await(this.#En?.valueOrThrow()),this.#Rn(e.frameId)})),e.on("Runtime.executionContextCreated",(async t=>{await(this.#En?.valueOrThrow()),this.#Mn(t.context,e)})),e.on("Page.lifecycleEvent",(async e=>{await(this.#En?.valueOrThrow()),this.#An(e)}))}async initialize(e,t){try{this.#En?.resolve(),this.#En=jt.create(),await Promise.all([this.#bn.addClient(e),e.send("Page.enable"),e.send("Page.getFrameTree").then((({frameTree:t})=>{this.#On(e,t),this.#En?.resolve()})),e.send("Page.setLifecycleEventsEnabled",{enabled:!0}),e.send("Runtime.enable").then((()=>this.#qn(e,Ft))),...(t?Array.from(this.#Sn.values()):[]).map((e=>t?.addPreloadScript(e))),...(t?Array.from(this.#nr.values()):[]).map((e=>t?.addExposedFunctionBinding(e)))])}catch(e){if(this.#En?.resolve(),Gt(e)&&jr(e))return;throw e}}page(){return this.#vn}mainFrame(){const e=this._frameTree.getMainFrame();return nt(e,"Requesting main frame too early!"),e}frames(){return Array.from(this._frameTree.frames())}frame(e){return this._frameTree.getById(e)||null}async addExposedFunctionBinding(e){this.#nr.add(e),await Promise.all(this.frames().map((async t=>await t.addExposedFunctionBinding(e))))}async removeExposedFunctionBinding(e){this.#nr.delete(e),await Promise.all(this.frames().map((async t=>await t.removeExposedFunctionBinding(e))))}async evaluateOnNewDocument(e){const{identifier:t}=await this.mainFrame()._client().send("Page.addScriptToEvaluateOnNewDocument",{source:e}),r=new Hi(this.mainFrame(),t,e);return this.#Sn.set(t,r),await Promise.all(this.frames().map((async e=>await e.addPreloadScript(r)))),{identifier:t}}async removeScriptToEvaluateOnNewDocument(e){const t=this.#Sn.get(e);if(!t)throw new Error(`Script to evaluate on new document with id ${e} not found`);this.#Sn.delete(e),await Promise.all(this.frames().map((e=>{const r=t.getIdForFrame(e);if(r)return e._client().send("Page.removeScriptToEvaluateOnNewDocument",{identifier:r}).catch(yt)})))}onAttachedToTarget(e){if("iframe"!==e._getTargetInfo().type)return;const t=this.frame(e._getTargetInfo().targetId);t&&t.updateClient(e._session()),this.setupEventListeners(e._session()),this.initialize(e._session(),t)}_deviceRequestPromptManager(e){let t=this.#Tn.get(e);return void 0===t&&(t=new Vi(e,this.#Nt),this.#Tn.set(e,t)),t}#An(e){const t=this.frame(e.frameId);t&&(t._onLifecycleEvent(e.loaderId,e.name),this.emit(wn.LifecycleEvent,t),t.emit(Ai.LifecycleEvent,void 0))}#Pn(e){const t=this.frame(e);t&&t._onLoadingStarted()}#Rn(e){const t=this.frame(e);t&&(t._onLoadingStopped(),this.emit(wn.LifecycleEvent,t),t.emit(Ai.LifecycleEvent,void 0))}#On(e,t){if(t.frame.parentId&&this.#Fn(e,t.frame.id,t.frame.parentId),this.#Cn.has(t.frame.id)?this.#Cn.delete(t.frame.id):this.#_n(t.frame,"Navigation"),t.childFrames)for(const r of t.childFrames)this.#On(e,r)}#Fn(e,t,r){let i=this.frame(t);i?e&&i.isOOPFrame()&&i.updateClient(e):(i=new _n(this,t,r,e),this._frameTree.addFrame(i),this.emit(wn.FrameAttached,i))}async#_n(e,t){const r=e.id,i=!e.parentId;let n=this._frameTree.getById(r);if(n)for(const e of n.childFrames())this.#xn(e);i&&(n?(this._frameTree.removeFrame(n),n._id=r):n=new _n(this,r,void 0,this.#xe),this._frameTree.addFrame(n)),n=await this._frameTree.waitForFrame(r),n._navigated(e),this.emit(wn.FrameNavigated,n),n.emit(Ai.FrameNavigated,t)}async#qn(e,t){const r=`${e.id()}:${t}`;this.#kn.has(r)||(await e.send("Page.addScriptToEvaluateOnNewDocument",{source:`//# sourceURL=${wt.INTERNAL_URL}`,worldName:t}),await Promise.all(this.frames().filter((t=>t.client===e)).map((r=>e.send("Page.createIsolatedWorld",{frameId:r._id,worldName:t,grantUniveralAccess:!0}).catch(yt)))),this.#kn.add(r))}#Dn(e,t){const r=this.frame(e);r&&(r._navigatedWithinDocument(t),this.emit(wn.FrameNavigatedWithinDocument,r),r.emit(Ai.FrameNavigatedWithinDocument,void 0),this.emit(wn.FrameNavigated,r),r.emit(Ai.FrameNavigated,"Navigation"))}#Jr(e,t){const r=this.frame(e);if(r)switch(t){case"remove":this.#xn(r);break;case"swap":this.emit(wn.FrameSwapped,r),r.emit(Ai.FrameSwapped,void 0)}}#Mn(e,t){const r=e.auxData,i=r&&r.frameId,n="string"==typeof i?this.frame(i):void 0;let s;if(n){if(n.client!==t)return;e.auxData&&e.auxData.isDefault?s=n.worlds[Cn]:e.name===Ft&&(s=n.worlds[Tn])}if(!s)return;const a=new fn(n?.client||this.#xe,e,s);s.setContext(a)}#xn(e){for(const t of e.childFrames())this.#xn(t);e[Ze](),this._frameTree.removeFrame(e),this.emit(wn.FrameDetached,e),e.emit(Ai.FrameDetached,e)}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Vn{constructor(){}}const Wn=Object.freeze({Left:"left",Right:"right",Middle:"middle",Back:"back",Forward:"forward"});class zn{constructor(){}}class Un{constructor(){}async tap(e,t){await this.touchStart(e,t),await this.touchEnd()}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */const Gn={0:{keyCode:48,key:"0",code:"Digit0"},1:{keyCode:49,key:"1",code:"Digit1"},2:{keyCode:50,key:"2",code:"Digit2"},3:{keyCode:51,key:"3",code:"Digit3"},4:{keyCode:52,key:"4",code:"Digit4"},5:{keyCode:53,key:"5",code:"Digit5"},6:{keyCode:54,key:"6",code:"Digit6"},7:{keyCode:55,key:"7",code:"Digit7"},8:{keyCode:56,key:"8",code:"Digit8"},9:{keyCode:57,key:"9",code:"Digit9"},Power:{key:"Power",code:"Power"},Eject:{key:"Eject",code:"Eject"},Abort:{keyCode:3,code:"Abort",key:"Cancel"},Help:{keyCode:6,code:"Help",key:"Help"},Backspace:{keyCode:8,code:"Backspace",key:"Backspace"},Tab:{keyCode:9,code:"Tab",key:"Tab"},Numpad5:{keyCode:12,shiftKeyCode:101,key:"Clear",code:"Numpad5",shiftKey:"5",location:3},NumpadEnter:{keyCode:13,code:"NumpadEnter",key:"Enter",text:"\r",location:3},Enter:{keyCode:13,code:"Enter",key:"Enter",text:"\r"},"\r":{keyCode:13,code:"Enter",key:"Enter",text:"\r"},"\n":{keyCode:13,code:"Enter",key:"Enter",text:"\r"},ShiftLeft:{keyCode:16,code:"ShiftLeft",key:"Shift",location:1},ShiftRight:{keyCode:16,code:"ShiftRight",key:"Shift",location:2},ControlLeft:{keyCode:17,code:"ControlLeft",key:"Control",location:1},ControlRight:{keyCode:17,code:"ControlRight",key:"Control",location:2},AltLeft:{keyCode:18,code:"AltLeft",key:"Alt",location:1},AltRight:{keyCode:18,code:"AltRight",key:"Alt",location:2},Pause:{keyCode:19,code:"Pause",key:"Pause"},CapsLock:{keyCode:20,code:"CapsLock",key:"CapsLock"},Escape:{keyCode:27,code:"Escape",key:"Escape"},Convert:{keyCode:28,code:"Convert",key:"Convert"},NonConvert:{keyCode:29,code:"NonConvert",key:"NonConvert"},Space:{keyCode:32,code:"Space",key:" "},Numpad9:{keyCode:33,shiftKeyCode:105,key:"PageUp",code:"Numpad9",shiftKey:"9",location:3},PageUp:{keyCode:33,code:"PageUp",key:"PageUp"},Numpad3:{keyCode:34,shiftKeyCode:99,key:"PageDown",code:"Numpad3",shiftKey:"3",location:3},PageDown:{keyCode:34,code:"PageDown",key:"PageDown"},End:{keyCode:35,code:"End",key:"End"},Numpad1:{keyCode:35,shiftKeyCode:97,key:"End",code:"Numpad1",shiftKey:"1",location:3},Home:{keyCode:36,code:"Home",key:"Home"},Numpad7:{keyCode:36,shiftKeyCode:103,key:"Home",code:"Numpad7",shiftKey:"7",location:3},ArrowLeft:{keyCode:37,code:"ArrowLeft",key:"ArrowLeft"},Numpad4:{keyCode:37,shiftKeyCode:100,key:"ArrowLeft",code:"Numpad4",shiftKey:"4",location:3},Numpad8:{keyCode:38,shiftKeyCode:104,key:"ArrowUp",code:"Numpad8",shiftKey:"8",location:3},ArrowUp:{keyCode:38,code:"ArrowUp",key:"ArrowUp"},ArrowRight:{keyCode:39,code:"ArrowRight",key:"ArrowRight"},Numpad6:{keyCode:39,shiftKeyCode:102,key:"ArrowRight",code:"Numpad6",shiftKey:"6",location:3},Numpad2:{keyCode:40,shiftKeyCode:98,key:"ArrowDown",code:"Numpad2",shiftKey:"2",location:3},ArrowDown:{keyCode:40,code:"ArrowDown",key:"ArrowDown"},Select:{keyCode:41,code:"Select",key:"Select"},Open:{keyCode:43,code:"Open",key:"Execute"},PrintScreen:{keyCode:44,code:"PrintScreen",key:"PrintScreen"},Insert:{keyCode:45,code:"Insert",key:"Insert"},Numpad0:{keyCode:45,shiftKeyCode:96,key:"Insert",code:"Numpad0",shiftKey:"0",location:3},Delete:{keyCode:46,code:"Delete",key:"Delete"},NumpadDecimal:{keyCode:46,shiftKeyCode:110,code:"NumpadDecimal",key:"\0",shiftKey:".",location:3},Digit0:{keyCode:48,code:"Digit0",shiftKey:")",key:"0"},Digit1:{keyCode:49,code:"Digit1",shiftKey:"!",key:"1"},Digit2:{keyCode:50,code:"Digit2",shiftKey:"@",key:"2"},Digit3:{keyCode:51,code:"Digit3",shiftKey:"#",key:"3"},Digit4:{keyCode:52,code:"Digit4",shiftKey:"$",key:"4"},Digit5:{keyCode:53,code:"Digit5",shiftKey:"%",key:"5"},Digit6:{keyCode:54,code:"Digit6",shiftKey:"^",key:"6"},Digit7:{keyCode:55,code:"Digit7",shiftKey:"&",key:"7"},Digit8:{keyCode:56,code:"Digit8",shiftKey:"*",key:"8"},Digit9:{keyCode:57,code:"Digit9",shiftKey:"(",key:"9"},KeyA:{keyCode:65,code:"KeyA",shiftKey:"A",key:"a"},KeyB:{keyCode:66,code:"KeyB",shiftKey:"B",key:"b"},KeyC:{keyCode:67,code:"KeyC",shiftKey:"C",key:"c"},KeyD:{keyCode:68,code:"KeyD",shiftKey:"D",key:"d"},KeyE:{keyCode:69,code:"KeyE",shiftKey:"E",key:"e"},KeyF:{keyCode:70,code:"KeyF",shiftKey:"F",key:"f"},KeyG:{keyCode:71,code:"KeyG",shiftKey:"G",key:"g"},KeyH:{keyCode:72,code:"KeyH",shiftKey:"H",key:"h"},KeyI:{keyCode:73,code:"KeyI",shiftKey:"I",key:"i"},KeyJ:{keyCode:74,code:"KeyJ",shiftKey:"J",key:"j"},KeyK:{keyCode:75,code:"KeyK",shiftKey:"K",key:"k"},KeyL:{keyCode:76,code:"KeyL",shiftKey:"L",key:"l"},KeyM:{keyCode:77,code:"KeyM",shiftKey:"M",key:"m"},KeyN:{keyCode:78,code:"KeyN",shiftKey:"N",key:"n"},KeyO:{keyCode:79,code:"KeyO",shiftKey:"O",key:"o"},KeyP:{keyCode:80,code:"KeyP",shiftKey:"P",key:"p"},KeyQ:{keyCode:81,code:"KeyQ",shiftKey:"Q",key:"q"},KeyR:{keyCode:82,code:"KeyR",shiftKey:"R",key:"r"},KeyS:{keyCode:83,code:"KeyS",shiftKey:"S",key:"s"},KeyT:{keyCode:84,code:"KeyT",shiftKey:"T",key:"t"},KeyU:{keyCode:85,code:"KeyU",shiftKey:"U",key:"u"},KeyV:{keyCode:86,code:"KeyV",shiftKey:"V",key:"v"},KeyW:{keyCode:87,code:"KeyW",shiftKey:"W",key:"w"},KeyX:{keyCode:88,code:"KeyX",shiftKey:"X",key:"x"},KeyY:{keyCode:89,code:"KeyY",shiftKey:"Y",key:"y"},KeyZ:{keyCode:90,code:"KeyZ",shiftKey:"Z",key:"z"},MetaLeft:{keyCode:91,code:"MetaLeft",key:"Meta",location:1},MetaRight:{keyCode:92,code:"MetaRight",key:"Meta",location:2},ContextMenu:{keyCode:93,code:"ContextMenu",key:"ContextMenu"},NumpadMultiply:{keyCode:106,code:"NumpadMultiply",key:"*",location:3},NumpadAdd:{keyCode:107,code:"NumpadAdd",key:"+",location:3},NumpadSubtract:{keyCode:109,code:"NumpadSubtract",key:"-",location:3},NumpadDivide:{keyCode:111,code:"NumpadDivide",key:"/",location:3},F1:{keyCode:112,code:"F1",key:"F1"},F2:{keyCode:113,code:"F2",key:"F2"},F3:{keyCode:114,code:"F3",key:"F3"},F4:{keyCode:115,code:"F4",key:"F4"},F5:{keyCode:116,code:"F5",key:"F5"},F6:{keyCode:117,code:"F6",key:"F6"},F7:{keyCode:118,code:"F7",key:"F7"},F8:{keyCode:119,code:"F8",key:"F8"},F9:{keyCode:120,code:"F9",key:"F9"},F10:{keyCode:121,code:"F10",key:"F10"},F11:{keyCode:122,code:"F11",key:"F11"},F12:{keyCode:123,code:"F12",key:"F12"},F13:{keyCode:124,code:"F13",key:"F13"},F14:{keyCode:125,code:"F14",key:"F14"},F15:{keyCode:126,code:"F15",key:"F15"},F16:{keyCode:127,code:"F16",key:"F16"},F17:{keyCode:128,code:"F17",key:"F17"},F18:{keyCode:129,code:"F18",key:"F18"},F19:{keyCode:130,code:"F19",key:"F19"},F20:{keyCode:131,code:"F20",key:"F20"},F21:{keyCode:132,code:"F21",key:"F21"},F22:{keyCode:133,code:"F22",key:"F22"},F23:{keyCode:134,code:"F23",key:"F23"},F24:{keyCode:135,code:"F24",key:"F24"},NumLock:{keyCode:144,code:"NumLock",key:"NumLock"},ScrollLock:{keyCode:145,code:"ScrollLock",key:"ScrollLock"},AudioVolumeMute:{keyCode:173,code:"AudioVolumeMute",key:"AudioVolumeMute"},AudioVolumeDown:{keyCode:174,code:"AudioVolumeDown",key:"AudioVolumeDown"},AudioVolumeUp:{keyCode:175,code:"AudioVolumeUp",key:"AudioVolumeUp"},MediaTrackNext:{keyCode:176,code:"MediaTrackNext",key:"MediaTrackNext"},MediaTrackPrevious:{keyCode:177,code:"MediaTrackPrevious",key:"MediaTrackPrevious"},MediaStop:{keyCode:178,code:"MediaStop",key:"MediaStop"},MediaPlayPause:{keyCode:179,code:"MediaPlayPause",key:"MediaPlayPause"},Semicolon:{keyCode:186,code:"Semicolon",shiftKey:":",key:";"},Equal:{keyCode:187,code:"Equal",shiftKey:"+",key:"="},NumpadEqual:{keyCode:187,code:"NumpadEqual",key:"=",location:3},Comma:{keyCode:188,code:"Comma",shiftKey:"<",key:","},Minus:{keyCode:189,code:"Minus",shiftKey:"_",key:"-"},Period:{keyCode:190,code:"Period",shiftKey:">",key:"."},Slash:{keyCode:191,code:"Slash",shiftKey:"?",key:"/"},Backquote:{keyCode:192,code:"Backquote",shiftKey:"~",key:"`"},BracketLeft:{keyCode:219,code:"BracketLeft",shiftKey:"{",key:"["},Backslash:{keyCode:220,code:"Backslash",shiftKey:"|",key:"\\"},BracketRight:{keyCode:221,code:"BracketRight",shiftKey:"}",key:"]"},Quote:{keyCode:222,code:"Quote",shiftKey:'"',key:"'"},AltGraph:{keyCode:225,code:"AltGraph",key:"AltGraph"},Props:{keyCode:247,code:"Props",key:"CrSel"},Cancel:{keyCode:3,key:"Cancel",code:"Abort"},Clear:{keyCode:12,key:"Clear",code:"Numpad5",location:3},Shift:{keyCode:16,key:"Shift",code:"ShiftLeft",location:1},Control:{keyCode:17,key:"Control",code:"ControlLeft",location:1},Alt:{keyCode:18,key:"Alt",code:"AltLeft",location:1},Accept:{keyCode:30,key:"Accept"},ModeChange:{keyCode:31,key:"ModeChange"}," ":{keyCode:32,key:" ",code:"Space"},Print:{keyCode:42,key:"Print"},Execute:{keyCode:43,key:"Execute",code:"Open"},"\0":{keyCode:46,key:"\0",code:"NumpadDecimal",location:3},a:{keyCode:65,key:"a",code:"KeyA"},b:{keyCode:66,key:"b",code:"KeyB"},c:{keyCode:67,key:"c",code:"KeyC"},d:{keyCode:68,key:"d",code:"KeyD"},e:{keyCode:69,key:"e",code:"KeyE"},f:{keyCode:70,key:"f",code:"KeyF"},g:{keyCode:71,key:"g",code:"KeyG"},h:{keyCode:72,key:"h",code:"KeyH"},i:{keyCode:73,key:"i",code:"KeyI"},j:{keyCode:74,key:"j",code:"KeyJ"},k:{keyCode:75,key:"k",code:"KeyK"},l:{keyCode:76,key:"l",code:"KeyL"},m:{keyCode:77,key:"m",code:"KeyM"},n:{keyCode:78,key:"n",code:"KeyN"},o:{keyCode:79,key:"o",code:"KeyO"},p:{keyCode:80,key:"p",code:"KeyP"},q:{keyCode:81,key:"q",code:"KeyQ"},r:{keyCode:82,key:"r",code:"KeyR"},s:{keyCode:83,key:"s",code:"KeyS"},t:{keyCode:84,key:"t",code:"KeyT"},u:{keyCode:85,key:"u",code:"KeyU"},v:{keyCode:86,key:"v",code:"KeyV"},w:{keyCode:87,key:"w",code:"KeyW"},x:{keyCode:88,key:"x",code:"KeyX"},y:{keyCode:89,key:"y",code:"KeyY"},z:{keyCode:90,key:"z",code:"KeyZ"},Meta:{keyCode:91,key:"Meta",code:"MetaLeft",location:1},"*":{keyCode:106,key:"*",code:"NumpadMultiply",location:3},"+":{keyCode:107,key:"+",code:"NumpadAdd",location:3},"-":{keyCode:109,key:"-",code:"NumpadSubtract",location:3},"/":{keyCode:111,key:"/",code:"NumpadDivide",location:3},";":{keyCode:186,key:";",code:"Semicolon"},"=":{keyCode:187,key:"=",code:"Equal"},",":{keyCode:188,key:",",code:"Comma"},".":{keyCode:190,key:".",code:"Period"},"`":{keyCode:192,key:"`",code:"Backquote"},"[":{keyCode:219,key:"[",code:"BracketLeft"},"\\":{keyCode:220,key:"\\",code:"Backslash"},"]":{keyCode:221,key:"]",code:"BracketRight"},"'":{keyCode:222,key:"'",code:"Quote"},Attn:{keyCode:246,key:"Attn"},CrSel:{keyCode:247,key:"CrSel",code:"Props"},ExSel:{keyCode:248,key:"ExSel"},EraseEof:{keyCode:249,key:"EraseEof"},Play:{keyCode:250,key:"Play"},ZoomOut:{keyCode:251,key:"ZoomOut"},")":{keyCode:48,key:")",code:"Digit0"},"!":{keyCode:49,key:"!",code:"Digit1"},"@":{keyCode:50,key:"@",code:"Digit2"},"#":{keyCode:51,key:"#",code:"Digit3"},$:{keyCode:52,key:"$",code:"Digit4"},"%":{keyCode:53,key:"%",code:"Digit5"},"^":{keyCode:54,key:"^",code:"Digit6"},"&":{keyCode:55,key:"&",code:"Digit7"},"(":{keyCode:57,key:"(",code:"Digit9"},A:{keyCode:65,key:"A",code:"KeyA"},B:{keyCode:66,key:"B",code:"KeyB"},C:{keyCode:67,key:"C",code:"KeyC"},D:{keyCode:68,key:"D",code:"KeyD"},E:{keyCode:69,key:"E",code:"KeyE"},F:{keyCode:70,key:"F",code:"KeyF"},G:{keyCode:71,key:"G",code:"KeyG"},H:{keyCode:72,key:"H",code:"KeyH"},I:{keyCode:73,key:"I",code:"KeyI"},J:{keyCode:74,key:"J",code:"KeyJ"},K:{keyCode:75,key:"K",code:"KeyK"},L:{keyCode:76,key:"L",code:"KeyL"},M:{keyCode:77,key:"M",code:"KeyM"},N:{keyCode:78,key:"N",code:"KeyN"},O:{keyCode:79,key:"O",code:"KeyO"},P:{keyCode:80,key:"P",code:"KeyP"},Q:{keyCode:81,key:"Q",code:"KeyQ"},R:{keyCode:82,key:"R",code:"KeyR"},S:{keyCode:83,key:"S",code:"KeyS"},T:{keyCode:84,key:"T",code:"KeyT"},U:{keyCode:85,key:"U",code:"KeyU"},V:{keyCode:86,key:"V",code:"KeyV"},W:{keyCode:87,key:"W",code:"KeyW"},X:{keyCode:88,key:"X",code:"KeyX"},Y:{keyCode:89,key:"Y",code:"KeyY"},Z:{keyCode:90,key:"Z",code:"KeyZ"},":":{keyCode:186,key:":",code:"Semicolon"},"<":{keyCode:188,key:"<",code:"Comma"},_:{keyCode:189,key:"_",code:"Minus"},">":{keyCode:190,key:">",code:"Period"},"?":{keyCode:191,key:"?",code:"Slash"},"~":{keyCode:192,key:"~",code:"Backquote"},"{":{keyCode:219,key:"{",code:"BracketLeft"},"|":{keyCode:220,key:"|",code:"Backslash"},"}":{keyCode:221,key:"}",code:"BracketRight"},'"':{keyCode:222,key:'"',code:"Quote"},SoftLeft:{key:"SoftLeft",code:"SoftLeft",location:4},SoftRight:{key:"SoftRight",code:"SoftRight",location:4},Camera:{keyCode:44,key:"Camera",code:"Camera",location:4},Call:{key:"Call",code:"Call",location:4},EndCall:{keyCode:95,key:"EndCall",code:"EndCall",location:4},VolumeDown:{keyCode:182,key:"VolumeDown",code:"VolumeDown",location:4},VolumeUp:{keyCode:183,key:"VolumeUp",code:"VolumeUp",location:4}};
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class Qn extends Vn{#xe;#Nn=new Set;_modifiers=0;constructor(e){super(),this.#xe=e}updateClient(e){this.#xe=e}async down(e,t={text:void 0,commands:[]}){const r=this.#Bn(e),i=this.#Nn.has(r.code);this.#Nn.add(r.code),this._modifiers|=this.#Ln(r.key);const n=void 0===t.text?r.text:t.text;await this.#xe.send("Input.dispatchKeyEvent",{type:n?"keyDown":"rawKeyDown",modifiers:this._modifiers,windowsVirtualKeyCode:r.keyCode,code:r.code,key:r.key,text:n,unmodifiedText:n,autoRepeat:i,location:r.location,isKeypad:3===r.location,commands:t.commands})}#Ln(e){return"Alt"===e?1:"Control"===e?2:"Meta"===e?4:"Shift"===e?8:0}#Bn(e){const t=8&this._modifiers,r={key:"",keyCode:0,code:"",text:"",location:0},i=Gn[e];return nt(i,`Unknown key: "${e}"`),i.key&&(r.key=i.key),t&&i.shiftKey&&(r.key=i.shiftKey),i.keyCode&&(r.keyCode=i.keyCode),t&&i.shiftKeyCode&&(r.keyCode=i.shiftKeyCode),i.code&&(r.code=i.code),i.location&&(r.location=i.location),1===r.key.length&&(r.text=r.key),i.text&&(r.text=i.text),t&&i.shiftText&&(r.text=i.shiftText),-9&this._modifiers&&(r.text=""),r}async up(e){const t=this.#Bn(e);this._modifiers&=~this.#Ln(t.key),this.#Nn.delete(t.code),await this.#xe.send("Input.dispatchKeyEvent",{type:"keyUp",modifiers:this._modifiers,key:t.key,windowsVirtualKeyCode:t.keyCode,code:t.code,location:t.location})}async sendCharacter(e){await this.#xe.send("Input.insertText",{text:e})}charIsKey(e){return!!Gn[e]}async type(e,t={}){const r=t.delay||void 0;for(const t of e)this.charIsKey(t)?await this.press(t,{delay:r}):(r&&await new Promise((e=>setTimeout(e,r))),await this.sendCharacter(t))}async press(e,t={}){const{delay:r=null}=t;await this.down(e,t),r&&await new Promise((e=>setTimeout(e,t.delay))),await this.up(e)}}const Jn=e=>{switch(e){case Wn.Left:return 1;case Wn.Right:return 2;case Wn.Middle:return 4;case Wn.Back:return 8;case Wn.Forward:return 16}},Xn=e=>1&e?Wn.Left:2&e?Wn.Right:4&e?Wn.Middle:8&e?Wn.Back:16&e?Wn.Forward:"none";class Yn extends zn{#xe;#jn;constructor(e,t){super(),this.#xe=e,this.#jn=t}updateClient(e){this.#xe=e}#Hn={position:{x:0,y:0},buttons:0};get#Ke(){return Object.assign({...this.#Hn},...this.#$n)}#$n=[];#Kn(){const e={};this.#$n.push(e);const t=()=>{this.#$n.splice(this.#$n.indexOf(e),1)};return{update:t=>{Object.assign(e,t)},commit:()=>{this.#Hn={...this.#Hn,...e},t()},rollback:t}}async#Vn(e){const{update:t,commit:r,rollback:i}=this.#Kn();try{await e(t),r()}catch(e){throw i(),e}}async reset(){const e=[];for(const[t,r]of[[1,Wn.Left],[4,Wn.Middle],[2,Wn.Right],[16,Wn.Forward],[8,Wn.Back]])this.#Ke.buttons&t&&e.push(this.up({button:r}));0===this.#Ke.position.x&&0===this.#Ke.position.y||e.push(this.move(0,0)),await Promise.all(e)}async move(e,t,r={}){const{steps:i=1}=r,n=this.#Ke.position,s=e,a=t;for(let e=1;e<=i;e++)await this.#Vn((t=>{t({position:{x:n.x+(s-n.x)*(e/i),y:n.y+(a-n.y)*(e/i)}});const{buttons:r,position:o}=this.#Ke;return this.#xe.send("Input.dispatchMouseEvent",{type:"mouseMoved",modifiers:this.#jn._modifiers,buttons:r,button:Xn(r),...o})}))}async down(e={}){const{button:t=Wn.Left,clickCount:r=1}=e,i=Jn(t);if(!i)throw new Error(`Unsupported mouse button: ${t}`);if(this.#Ke.buttons&i)throw new Error(`'${t}' is already pressed.`);await this.#Vn((e=>{e({buttons:this.#Ke.buttons|i});const{buttons:n,position:s}=this.#Ke;return this.#xe.send("Input.dispatchMouseEvent",{type:"mousePressed",modifiers:this.#jn._modifiers,clickCount:r,buttons:n,button:t,...s})}))}async up(e={}){const{button:t=Wn.Left,clickCount:r=1}=e,i=Jn(t);if(!i)throw new Error(`Unsupported mouse button: ${t}`);if(!(this.#Ke.buttons&i))throw new Error(`'${t}' is not pressed.`);await this.#Vn((e=>{e({buttons:this.#Ke.buttons&~i});const{buttons:n,position:s}=this.#Ke;return this.#xe.send("Input.dispatchMouseEvent",{type:"mouseReleased",modifiers:this.#jn._modifiers,clickCount:r,buttons:n,button:t,...s})}))}async click(e,t,r={}){const{delay:i,count:n=1,clickCount:s=n}=r;if(n<1)throw new Error("Click must occur a positive number of times.");const a=[this.move(e,t)];if(s===n)for(let e=1;e<n;++e)a.push(this.down({...r,clickCount:e}),this.up({...r,clickCount:e}));a.push(this.down({...r,clickCount:s})),"number"==typeof i&&(await Promise.all(a),a.length=0,await new Promise((e=>{setTimeout(e,i)}))),a.push(this.up({...r,clickCount:s})),await Promise.all(a)}async wheel(e={}){const{deltaX:t=0,deltaY:r=0}=e,{position:i,buttons:n}=this.#Ke;await this.#xe.send("Input.dispatchMouseEvent",{type:"mouseWheel",pointerType:"mouse",modifiers:this.#jn._modifiers,deltaY:r,deltaX:t,buttons:n,...i})}async drag(e,t){const r=new Promise((e=>{this.#xe.once("Input.dragIntercepted",(t=>e(t.data)))}));return await this.move(e.x,e.y),await this.down(),await this.move(t.x,t.y),await r}async dragEnter(e,t){await this.#xe.send("Input.dispatchDragEvent",{type:"dragEnter",x:e.x,y:e.y,modifiers:this.#jn._modifiers,data:t})}async dragOver(e,t){await this.#xe.send("Input.dispatchDragEvent",{type:"dragOver",x:e.x,y:e.y,modifiers:this.#jn._modifiers,data:t})}async drop(e,t){await this.#xe.send("Input.dispatchDragEvent",{type:"drop",x:e.x,y:e.y,modifiers:this.#jn._modifiers,data:t})}async dragAndDrop(e,t,r={}){const{delay:i=null}=r,n=await this.drag(e,t);await this.dragEnter(t,n),await this.dragOver(t,n),i&&await new Promise((e=>setTimeout(e,i))),await this.drop(t,n),await this.up()}}class Zn extends Un{#xe;#jn;constructor(e,t){super(),this.#xe=e,this.#jn=t}updateClient(e){this.#xe=e}async touchStart(e,t){await this.#xe.send("Input.dispatchTouchEvent",{type:"touchStart",touchPoints:[{x:Math.round(e),y:Math.round(t),radiusX:.5,radiusY:.5,force:.5}],modifiers:this.#jn._modifiers})}async touchMove(e,t){await this.#xe.send("Input.dispatchTouchEvent",{type:"touchMove",touchPoints:[{x:Math.round(e),y:Math.round(t),radiusX:.5,radiusY:.5,force:.5}],modifiers:this.#jn._modifiers})}async touchEnd(){await this.#xe.send("Input.dispatchTouchEvent",{type:"touchEnd",touchPoints:[],modifiers:this.#jn._modifiers})}}class es{#xe;#Wn=!1;#zn;constructor(e){this.#xe=e}updateClient(e){this.#xe=e}async start(e={}){nt(!this.#Wn,"Cannot start recording trace while already recording trace.");const t=["-*","devtools.timeline","v8.execute","disabled-by-default-devtools.timeline","disabled-by-default-devtools.timeline.frame","toplevel","blink.console","blink.user_timing","latencyInfo","disabled-by-default-devtools.timeline.stack","disabled-by-default-v8.cpu_profiler"],{path:r,screenshots:i=!1,categories:n=t}=e;i&&n.push("disabled-by-default-devtools.screenshot");const s=n.filter((e=>e.startsWith("-"))).map((e=>e.slice(1))),a=n.filter((e=>!e.startsWith("-")));this.#zn=r,this.#Wn=!0,await this.#xe.send("Tracing.start",{transferMode:"ReturnAsStream",traceConfig:{excludedCategories:s,includedCategories:a}})}async stop(){const e=jt.create();return this.#xe.once("Tracing.tracingComplete",(async t=>{try{nt(t.stream,'Missing "stream"');const r=await It(this.#xe,t.stream),i=await Et(r,this.#zn);e.resolve(i??void 0)}catch(t){Gt(t)?e.reject(t):e.reject(new Error(`Unknown error: ${t}`))}})),await this.#xe.send("Tracing.end"),this.#Wn=!1,await e.valueOrThrow()}}
/**
 * @license
 * Copyright 2018 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class ts extends it{timeoutSettings=new er;#ge;constructor(e){super(),this.#ge=e}url(){return this.#ge}async evaluate(e,...t){return e=vt(this.evaluate.name,e),await this.mainRealm().evaluate(e,...t)}async evaluateHandle(e,...t){return e=vt(this.evaluateHandle.name,e),await this.mainRealm().evaluateHandle(e,...t)}async close(){throw new pt("WebWorker.close() is not supported")}}class rs extends ts{#Zt;#xe;#E;#M;constructor(e,t,r,i,n,s){super(t),this.#E=r,this.#xe=e,this.#M=i,this.#Zt=new Sn(this,new er),this.#xe.once("Runtime.executionContextCreated",(async t=>{this.#Zt.setContext(new fn(e,t.context,this.#Zt))})),this.#Zt.emitter.on("consoleapicalled",(async e=>{try{return n(e.type,e.args.map((e=>new nn(this.#Zt,e))),e.stackTrace)}catch(e){yt(e)}})),this.#xe.on("Runtime.exceptionThrown",s),this.#xe.once(Bt.Disconnected,(()=>{this.#Zt.dispose()})),this.#xe.send("Runtime.enable").catch(yt)}mainRealm(){return this.#Zt}get client(){return this.#xe}async close(){switch(this.#M){case Kt.SERVICE_WORKER:case Kt.SHARED_WORKER:await(this.client.connection()?.send("Target.closeTarget",{targetId:this.#E})),await(this.client.connection()?.send("Target.detachFromTarget",{sessionId:this.client.id()}));break;default:await this.evaluate((()=>{self.close()}))}}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */var is=self&&self.__addDisposableResource||function(e,t,r){if(null!=t){if("object"!=typeof t&&"function"!=typeof t)throw new TypeError("Object expected.");var i;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose]}if("function"!=typeof i)throw new TypeError("Object not disposable.");e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t},ns=self&&self.__disposeResources||function(e){return function(t){function r(r){t.error=t.hasError?new e(r,t.error,"An error was suppressed during disposal."):r,t.hasError=!0}return function e(){for(;t.stack.length;){var i=t.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(e,(function(t){return r(t),e()}))}catch(e){r(e)}}if(t.hasError)throw t.error}()}}("function"==typeof SuppressedError?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i});function ss(e){return"warning"===e?"warn":e}class as extends Cr{static async _create(e,t,r){const i=new as(e,t);if(await i.#Un(),r)try{await i.setViewport(r)}catch(e){if(!Gt(e)||!jr(e))throw e;yt(e)}return i}#Se=!1;#Gn;#Qn;#Jn;#Xn;#Yn;#jn;#Zn;#es;#er;#ts;#rs;#nr=new Map;#is=new Map;#ns;#ss;#as=new Map;#os=new Set;#cs=jt.create();#ls=!1;#ds=!1;#us=[[wn.FrameAttached,e=>{this.emit("frameattached",e)}],[wn.FrameDetached,e=>{this.emit("framedetached",e)}],[wn.FrameNavigated,e=>{this.emit("framenavigated",e)}]];#hs=[[Fr.Request,e=>{this.emit("request",e)}],[Fr.RequestServedFromCache,e=>{this.emit("requestservedfromcache",e)}],[Fr.Response,e=>{this.emit("response",e)}],[Fr.RequestFailed,e=>{this.emit("requestfailed",e)}],[Fr.RequestFinished,e=>{this.emit("requestfinished",e)}]];#ps=[[Bt.Disconnected,()=>{this.#cs.reject(new ft("Target closed"))}],["Page.domContentEventFired",()=>this.emit("domcontentloaded",void 0)],["Page.loadEventFired",()=>this.emit("load",void 0)],["Page.javascriptDialogOpening",this.#fs.bind(this)],["Runtime.exceptionThrown",this.#ms.bind(this)],["Inspector.targetCrashed",this.#ys.bind(this)],["Performance.metrics",this.#gs.bind(this)],["Log.entryAdded",this.#ws.bind(this)],["Page.fileChooserOpened",this.#vs.bind(this)]];constructor(e,t){super(),this.#Qn=e,this.#Xn=e.parentSession(),nt(this.#Xn,"Tab target session is not defined."),this.#Yn=this.#Xn._target(),nt(this.#Yn,"Tab target is not defined."),this.#Jn=t,this.#Gn=t._targetManager(),this.#jn=new Qn(e),this.#Zn=new Yn(e,this.#jn),this.#es=new Zn(e,this.#jn),this.#er=new Kn(e,this,this._timeoutSettings),this.#ts=new Xr(e),this.#rs=new es(e),this.#ns=new Hr(e),this.#ss=null;for(const[e,t]of this.#us)this.#er.on(e,t);this.#er.on(wn.ConsoleApiCalled,(([e,t])=>{this.#ir(e,t)})),this.#er.on(wn.BindingCalled,(([e,t])=>{this.#rr(e,t)}));for(const[e,t]of this.#hs)this.#er.networkManager.on(e,t);this.#Xn.on(Bt.Swapped,this.#bs.bind(this)),this.#Xn.on(Bt.Ready,this.#ks.bind(this)),this.#Gn.on("targetGone",this.#Ss),this.#Yn._isClosedDeferred.valueOrThrow().then((()=>{this.#Gn.off("targetGone",this.#Ss),this.emit("close",void 0),this.#Se=!0})).catch(yt),this.#Cs(),this.#Ts()}#Ts(){const e=[];for(const t of this.#Gn.getChildTargets(this.#Jn))e.push(t);let t=0;for(;t<e.length;){const r=e[t];t++;const i=r._session();i&&this.#xt(i);for(const t of this.#Gn.getChildTargets(r))e.push(t)}}async#bs(e){this.#Qn=e,nt(this.#Qn instanceof Zt,"CDPSession is not instance of CDPSessionImpl"),this.#Jn=this.#Qn._target(),nt(this.#Jn,"Missing target on swap"),this.#jn.updateClient(e),this.#Zn.updateClient(e),this.#es.updateClient(e),this.#ts.updateClient(e),this.#rs.updateClient(e),this.#ns.updateClient(e),await this.#er.swapFrameTree(e),this.#Cs()}async#ks(e){nt(e instanceof Zt),"prerender"===e._target()._subtype()&&(this.#er.registerSpeculativeSession(e).catch(yt),this.#ts.registerSpeculativeSession(e).catch(yt))}#Cs(){this.#Qn.on(Bt.Ready,this.#xt);for(const[e,t]of this.#ps)this.#Qn.on(e,t)}#Ss=e=>{const t=e._session()?.id(),r=this.#as.get(t);r&&(this.#as.delete(t),this.emit("workerdestroyed",r))};#xt=e=>{if(nt(e instanceof Zt),this.#er.onAttachedToTarget(e._target()),"worker"===e._target()._getTargetInfo().type){const t=new rs(e,e._target().url(),e._target()._targetId,e._target().type(),this.#Es.bind(this),this.#ms.bind(this));this.#as.set(e.id(),t),this.emit("workercreated",t)}e.on(Bt.Ready,this.#xt)};async#Un(){try{await Promise.all([this.#er.initialize(this.#Qn),this.#Qn.send("Performance.enable"),this.#Qn.send("Log.enable")])}catch(e){if(!Gt(e)||!jr(e))throw e;yt(e)}}async#vs(e){const t={stack:[],error:void 0,hasError:!1};try{if(!this.#os.size)return;const r=this.#er.frame(e.frameId);nt(r,"This should never happen.");const i=is(t,await r.worlds[Cn].adoptBackendNode(e.backendNodeId),!1),n=new xr(i.move(),e);for(const e of this.#os)e.resolve(n);this.#os.clear()}catch(e){t.error=e,t.hasError=!0}finally{ns(t)}}_client(){return this.#Qn}isServiceWorkerBypassed(){return this.#ls}isDragInterceptionEnabled(){return this.#ds}isJavaScriptEnabled(){return this.#ts.javascriptEnabled}async waitForFileChooser(e={}){const t=0===this.#os.size,{timeout:r=this._timeoutSettings.timeout()}=e,i=jt.create({message:`Waiting for \`FileChooser\` failed: ${r}ms exceeded`,timeout:r});let n;this.#os.add(i),t&&(n=this.#Qn.send("Page.setInterceptFileChooserDialog",{enabled:!0}));try{const[e]=await Promise.all([i.valueOrThrow(),n]);return e}catch(e){throw this.#os.delete(i),e}}async setGeolocation(e){return await this.#ts.setGeolocation(e)}target(){return this.#Jn}browser(){return this.#Jn.browser()}browserContext(){return this.#Jn.browserContext()}#ys(){this.emit("error",new Error("Page crashed!"))}#ws(e){const{level:t,text:r,args:i,source:n,url:s,lineNumber:a}=e.entry;i&&i.map((e=>{sn(this.#Qn,e)})),"worker"!==n&&this.emit("console",new Ir(ss(t),r,[],[{url:s,lineNumber:a}]))}mainFrame(){return this.#er.mainFrame()}get keyboard(){return this.#jn}get touchscreen(){return this.#es}get coverage(){return this.#ns}get tracing(){return this.#rs}frames(){return this.#er.frames()}workers(){return Array.from(this.#as.values())}async setRequestInterception(e){return await this.#er.networkManager.setRequestInterception(e)}async setBypassServiceWorker(e){return this.#ls=e,await this.#Qn.send("Network.setBypassServiceWorker",{bypass:e})}async setDragInterception(e){return this.#ds=e,await this.#Qn.send("Input.setInterceptDrags",{enabled:e})}async setOfflineMode(e){return await this.#er.networkManager.setOfflineMode(e)}async emulateNetworkConditions(e){return await this.#er.networkManager.emulateNetworkConditions(e)}setDefaultNavigationTimeout(e){this._timeoutSettings.setDefaultNavigationTimeout(e)}setDefaultTimeout(e){this._timeoutSettings.setDefaultTimeout(e)}getDefaultTimeout(){return this._timeoutSettings.timeout()}async queryObjects(e){nt(!e.disposed,"Prototype JSHandle is disposed!"),nt(e.id,"Prototype JSHandle must not be referencing primitive value");const t=await this.mainFrame().client.send("Runtime.queryObjects",{prototypeObjectId:e.id});return this.mainFrame().mainRealm().createCdpHandle(t.objects)}async cookies(...e){const t=(await this.#Qn.send("Network.getCookies",{urls:e.length?e:[this.url()]})).cookies,r=["sourcePort"];return t.map((e=>{for(const t of r)delete e[t];return e}))}async deleteCookie(...e){const t=this.url();for(const r of e){const e=Object.assign({},r);!r.url&&t.startsWith("http")&&(e.url=t),await this.#Qn.send("Network.deleteCookies",e)}}async setCookie(...e){const t=this.url(),r=t.startsWith("http"),i=e.map((e=>{const i=Object.assign({},e);return!i.url&&r&&(i.url=t),nt("about:blank"!==i.url,`Blank page can not have cookie "${i.name}"`),nt(!String.prototype.startsWith.call(i.url||"","data:"),`Data URL page can not have cookie "${i.name}"`),i}));await this.deleteCookie(...i),i.length&&await this.#Qn.send("Network.setCookies",{cookies:i})}async exposeFunction(e,t){if(this.#nr.has(e))throw new Error(`Failed to add page binding with name ${e}: window['${e}'] already exists!`);const r=function(e,t){return St(tn,e,t,rn)}("exposedFun",e);let i;if("function"==typeof t)i=new qr(e,t,r);else i=new qr(e,t.default,r);this.#nr.set(e,i);const[{identifier:n}]=await Promise.all([this.#er.evaluateOnNewDocument(r),this.#er.addExposedFunctionBinding(i)]);this.#is.set(e,n)}async removeExposedFunction(e){const t=this.#is.get(e);if(!t)throw new Error(`Function with name "${e}" does not exist`);const r=this.#nr.get(e);this.#is.delete(e),this.#nr.delete(e),await Promise.all([this.#er.removeScriptToEvaluateOnNewDocument(t),this.#er.removeExposedFunctionBinding(r)])}async authenticate(e){return await this.#er.networkManager.authenticate(e)}async setExtraHTTPHeaders(e){return await this.#er.networkManager.setExtraHTTPHeaders(e)}async setUserAgent(e,t){return await this.#er.networkManager.setUserAgent(e,t)}async metrics(){const e=await this.#Qn.send("Performance.getMetrics");return this.#Is(e.metrics)}#gs(e){this.emit("metrics",{title:e.title,metrics:this.#Is(e.metrics)})}#Is(e){const t={};for(const r of e||[])os.has(r.name)&&(t[r.name]=r.value);return t}#ms(e){this.emit("pageerror",function(e){let t,r;if(e.exception){if(!("object"===e.exception.type&&"error"===e.exception.subtype||e.exception.objectId))return en(e.exception);{const i=Zi(e);t=i.name,r=i.message}}else t="Error",r=e.text;const i=new Error(r);i.name=t;const n=i.message.split("\n").length,s=i.stack.split("\n").splice(0,n),a=[];if(e.stackTrace)for(const t of e.stackTrace.callFrames)if(a.push(`    at ${t.functionName||"<anonymous>"} (${t.url}:${t.lineNumber+1}:${t.columnNumber+1})`),a.length>=Error.stackTraceLimit)break;return i.stack=[...s,...a].join("\n"),i}(e.exceptionDetails))}#ir(e,t){const r=t.args.map((t=>e.createCdpHandle(t)));this.#Es(ss(t.type),r,t.stackTrace)}async#rr(e,t){let r;try{r=JSON.parse(t.payload)}catch{return}const{type:i,name:n,seq:s,args:a,isTrivial:o}=r;if("exposedFun"!==i)return;const c=e.context;if(!c)return;const l=this.#nr.get(n);await(l?.run(c,s,a,o))}#Es(e,t,r){if(!this.listenerCount("console"))return void t.forEach((e=>e.dispose()));const i=[];for(const e of t){const t=e.remoteObject();t.objectId?i.push(e.toString()):i.push(en(t))}const n=[];if(r)for(const e of r.callFrames)n.push({url:e.url,lineNumber:e.lineNumber,columnNumber:e.columnNumber});const s=new Ir(ss(e),i.join(" "),t,n);this.emit("console",s)}#fs(e){const t=function(e){let t=null;return new Set(["alert","confirm","prompt","beforeunload"]).has(e)&&(t=e),nt(t,`Unknown javascript dialog type: ${e}`),t}(e.type),r=new zr(this.#Qn,t,e.message,e.defaultPrompt);this.emit("dialog",r)}async reload(e){const[t]=await Promise.all([this.waitForNavigation({...e,ignoreSameDocumentNavigation:!0}),this.#Qn.send("Page.reload")]);return t}async createCDPSession(){return await this.target().createCDPSession()}async goBack(e={}){return await this.#xs(-1,e)}async goForward(e={}){return await this.#xs(1,e)}async#xs(e,t){const r=await this.#Qn.send("Page.getNavigationHistory"),i=r.entries[r.currentIndex+e];if(!i)return null;return(await Promise.all([this.waitForNavigation(t),this.#Qn.send("Page.navigateToHistoryEntry",{entryId:i.id})]))[0]}async bringToFront(){await this.#Qn.send("Page.bringToFront")}async setJavaScriptEnabled(e){return await this.#ts.setJavaScriptEnabled(e)}async setBypassCSP(e){await this.#Qn.send("Page.setBypassCSP",{enabled:e})}async emulateMediaType(e){return await this.#ts.emulateMediaType(e)}async emulateCPUThrottling(e){return await this.#ts.emulateCPUThrottling(e)}async emulateMediaFeatures(e){return await this.#ts.emulateMediaFeatures(e)}async emulateTimezone(e){return await this.#ts.emulateTimezone(e)}async emulateIdleState(e){return await this.#ts.emulateIdleState(e)}async emulateVisionDeficiency(e){return await this.#ts.emulateVisionDeficiency(e)}async setViewport(e){const t=await this.#ts.emulateViewport(e);this.#ss=e,t&&await this.reload()}viewport(){return this.#ss}async evaluateOnNewDocument(e,...t){const r=St(e,...t);return await this.#er.evaluateOnNewDocument(r)}async removeScriptToEvaluateOnNewDocument(e){return await this.#er.removeScriptToEvaluateOnNewDocument(e)}async setCacheEnabled(e=!0){await this.#er.networkManager.setCacheEnabled(e)}async _screenshot(e){const t={stack:[],error:void 0,hasError:!1};try{const{fromSurface:r,omitBackground:i,optimizeForSpeed:n,quality:s,clip:a,type:o,captureBeyondViewport:c}=e,l=this.target()._targetManager()instanceof Yr,d=is(t,new rt,!0);l||!i||"png"!==o&&"webp"!==o||(await this.#ts.setTransparentBackgroundColor(),d.defer((async()=>{await this.#ts.resetDefaultBackgroundColor().catch(yt)})));let u=a;if(u&&!c){u=function(e,t){const r=Math.max(e.x,t.x),i=Math.max(e.y,t.y);return{x:r,y:i,width:Math.max(Math.min(e.x+e.width,t.x+t.width)-r,0),height:Math.max(Math.min(e.y+e.height,t.y+t.height)-i,0)}}
/**
 * @license
 * Copyright 2019 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */(u,await this.mainFrame().isolatedRealm().evaluate((()=>{const{height:e,pageLeft:t,pageTop:r,width:i}=window.visualViewport;return{x:t,y:r,height:e,width:i}})))}const{data:h}=await this.#Qn.send("Page.captureScreenshot",{format:o,...n?{optimizeForSpeed:n}:{},...void 0!==s?{quality:Math.round(s)}:{},...u?{clip:{...u,scale:u.scale??1}}:{},...r?{}:{fromSurface:r},captureBeyondViewport:c});return h}catch(e){t.error=e,t.hasError=!0}finally{const e=ns(t);e&&await e}}async createPDFStream(e={}){const{timeout:t=this._timeoutSettings.timeout()}=e,{landscape:r,displayHeaderFooter:i,headerTemplate:n,footerTemplate:s,printBackground:a,scale:o,width:c,height:l,margin:d,pageRanges:u,preferCSSPageSize:h,omitBackground:p,tagged:f,outline:m,waitForFonts:y}=function(e={},t="in"){let r=8.5,i=11;if(e.format){const t=mt[e.format.toLowerCase()];nt(t,"Unknown paper format: "+e.format),r=t.width,i=t.height}else r=Rt(e.width,t)??r,i=Rt(e.height,t)??i;const n={top:Rt(e.margin?.top,t)||0,left:Rt(e.margin?.left,t)||0,bottom:Rt(e.margin?.bottom,t)||0,right:Rt(e.margin?.right,t)||0};return e.outline&&(e.tagged=!0),{scale:1,displayHeaderFooter:!1,headerTemplate:"",footerTemplate:"",printBackground:!1,landscape:!1,pageRanges:"",preferCSSPageSize:!1,omitBackground:!1,outline:!1,tagged:!0,waitForFonts:!0,...e,width:r,height:i,margin:n}}(e);p&&await this.#ts.setTransparentBackgroundColor(),y&&await Se(ve(this.mainFrame().isolatedRealm().evaluate((()=>document.fonts.ready))).pipe(Je(xt(t))));const g=this.#Qn.send("Page.printToPDF",{transferMode:"ReturnAsStream",landscape:r,displayHeaderFooter:i,headerTemplate:n,footerTemplate:s,printBackground:a,scale:o,paperWidth:c,paperHeight:l,marginTop:d.top,marginBottom:d.bottom,marginLeft:d.left,marginRight:d.right,pageRanges:u,preferCSSPageSize:h,generateTaggedPDF:f,generateDocumentOutline:m}),w=await Se(ve(g).pipe(Je(xt(t))));return p&&await this.#ts.resetDefaultBackgroundColor(),nt(w.stream,"`stream` is missing from `Page.printToPDF"),await It(this.#Qn,w.stream)}async pdf(e={}){const{path:t}=e,r=await this.createPDFStream(e),i=await Et(r,t);return nt(i,"Could not create buffer"),i}async close(e={runBeforeUnload:void 0}){const t={stack:[],error:void 0,hasError:!1};try{is(t,await this.browserContext().waitForScreenshotOperations(),!1);const r=this.#Qn.connection();nt(r,"Protocol error: Connection closed. Most likely the page has been closed.");!!e.runBeforeUnload?await this.#Qn.send("Page.close"):(await r.send("Target.closeTarget",{targetId:this.#Jn._targetId}),await this.#Yn._isClosedDeferred.valueOrThrow())}catch(e){t.error=e,t.hasError=!0}finally{ns(t)}}isClosed(){return this.#Se}get mouse(){return this.#Zn}async waitForDevicePrompt(e={}){return await this.mainFrame().waitForDevicePrompt(e)}}const os=new Set(["Timestamp","Documents","Frames","JSEventListeners","Nodes","LayoutCount","RecalcStyleCount","LayoutDuration","RecalcStyleDuration","ScriptDuration","TaskDuration","JSHeapUsedSize","JSHeapTotalSize"]);var cs;!function(e){e.SUCCESS="success",e.ABORTED="aborted"}(cs||(cs={}));class ls extends Ut{#Fs;#_s;#Ds;#Gn;#Ps;#Rs=new Set;_initializedDeferred=jt.create();_isClosedDeferred=jt.create();_targetId;constructor(e,t,r,i,n){super(),this.#_s=t,this.#Gn=i,this.#Ds=e,this.#Fs=r,this._targetId=e.targetId,this.#Ps=n,this.#_s&&this.#_s instanceof Zt&&this.#_s._setTarget(this)}async asPage(){const e=this._session();return e?await as._create(e,this,null):await this.createCDPSession().then((e=>as._create(e,this,null)))}_subtype(){return this.#Ds.subtype}_session(){return this.#_s}_addChildTarget(e){this.#Rs.add(e)}_removeChildTarget(e){this.#Rs.delete(e)}_childTargets(){return this.#Rs}_sessionFactory(){if(!this.#Ps)throw new Error("sessionFactory is not initialized");return this.#Ps}createCDPSession(){if(!this.#Ps)throw new Error("sessionFactory is not initialized");return this.#Ps(!1).then((e=>(e._setTarget(this),e)))}url(){return this.#Ds.url}type(){switch(this.#Ds.type){case"page":return Kt.PAGE;case"background_page":return Kt.BACKGROUND_PAGE;case"service_worker":return Kt.SERVICE_WORKER;case"shared_worker":return Kt.SHARED_WORKER;case"browser":return Kt.BROWSER;case"webview":return Kt.WEBVIEW;case"tab":return Kt.TAB;default:return Kt.OTHER}}_targetManager(){if(!this.#Gn)throw new Error("targetManager is not initialized");return this.#Gn}_getTargetInfo(){return this.#Ds}browser(){if(!this.#Fs)throw new Error("browserContext is not initialized");return this.#Fs.browser()}browserContext(){if(!this.#Fs)throw new Error("browserContext is not initialized");return this.#Fs}opener(){const{openerId:e}=this.#Ds;if(e)return this.browser().targets().find((t=>t._targetId===e))}_targetInfoChanged(e){this.#Ds=e,this._checkIfInitialized()}_initialize(){this._initializedDeferred.resolve(cs.SUCCESS)}_isTargetExposed(){return this.type()!==Kt.TAB&&!this._subtype()}_checkIfInitialized(){this._initializedDeferred.resolved()||this._initializedDeferred.resolve(cs.SUCCESS)}}class ds extends ls{#Ms;pagePromise;constructor(e,t,r,i,n,s){super(e,t,r,i,n),this.#Ms=s??void 0}_initialize(){this._initializedDeferred.valueOrThrow().then((async e=>{if(e===cs.ABORTED)return;const t=this.opener();if(!(t instanceof ds))return;if(!t||!t.pagePromise||"page"!==this.type())return!0;const r=await t.pagePromise;if(!r.listenerCount("popup"))return!0;const i=await this.page();return r.emit("popup",i),!0})).catch(yt),this._checkIfInitialized()}async page(){if(!this.pagePromise){const e=this._session();this.pagePromise=(e?Promise.resolve(e):this._sessionFactory()(!1)).then((e=>as._create(e,this,this.#Ms??null)))}return await this.pagePromise??null}_checkIfInitialized(){this._initializedDeferred.resolved()||""!==this._getTargetInfo().url&&this._initializedDeferred.resolve(cs.SUCCESS)}}class us extends ds{}class hs extends ls{#As;async worker(){if(!this.#As){const e=this._session();this.#As=(e?Promise.resolve(e):this._sessionFactory()(!1)).then((e=>new rs(e,this._getTargetInfo().url,this._targetId,this.type(),(()=>{}),(()=>{}))))}return await this.#As}}class ps extends ls{}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class fs extends it{#C;#yt=new Map;#Os=new Map;#qs=new Map;#Ns=new Set;#vt;#bt;#kt=new WeakMap;#Bs=new WeakMap;#St=jt.create();#Ct=new Set;#Ls=!0;#js=[{}];constructor(e,t,r,i=!0){super(),this.#C=e,this.#vt=r,this.#bt=t,this.#Ls=i,this.#C.on("Target.targetCreated",this.#Tt),this.#C.on("Target.targetDestroyed",this.#Et),this.#C.on("Target.targetInfoChanged",this.#Hs),this.#C.on(Bt.SessionDetached,this.#It),this.#$s(this.#C)}#Ks=()=>{if(this.#Ls)for(const[e,t]of this.#yt.entries()){const r=new ls(t,void 0,void 0,this,void 0),i="browser"===t.type||t.url.startsWith("chrome-extension://");this.#vt&&!this.#vt(r)||i||this.#Ct.add(e)}};async initialize(){await this.#C.send("Target.setDiscoverTargets",{discover:!0,filter:this.#js}),this.#Ks(),await this.#C.send("Target.setAutoAttach",{waitForDebuggerOnStart:!0,flatten:!0,autoAttach:!0,filter:[{type:"page",exclude:!0},...this.#js]}),this.#Ft(),await this.#St.valueOrThrow()}getChildTargets(e){return e._childTargets()}dispose(){this.#C.off("Target.targetCreated",this.#Tt),this.#C.off("Target.targetDestroyed",this.#Et),this.#C.off("Target.targetInfoChanged",this.#Hs),this.#C.off(Bt.SessionDetached,this.#It),this.#Vs(this.#C)}getAvailableTargets(){return this.#Os}#$s(e){const t=t=>{this.#xt(e,t)};nt(!this.#kt.has(e)),this.#kt.set(e,t),e.on("Target.attachedToTarget",t);const r=t=>this.#Ss(e,t);nt(!this.#Bs.has(e)),this.#Bs.set(e,r),e.on("Target.detachedFromTarget",r)}#Vs(e){const t=this.#kt.get(e);t&&(e.off("Target.attachedToTarget",t),this.#kt.delete(e)),this.#Bs.has(e)&&(e.off("Target.detachedFromTarget",this.#Bs.get(e)),this.#Bs.delete(e))}#It=e=>{this.#Vs(e)};#Tt=async e=>{if(this.#yt.set(e.targetInfo.targetId,e.targetInfo),this.emit("targetDiscovered",e.targetInfo),"browser"===e.targetInfo.type&&e.targetInfo.attached){if(this.#Os.has(e.targetInfo.targetId))return;const t=this.#bt(e.targetInfo,void 0);t._initialize(),this.#Os.set(e.targetInfo.targetId,t)}};#Et=e=>{const t=this.#yt.get(e.targetId);if(this.#yt.delete(e.targetId),this.#Ft(e.targetId),"service_worker"===t?.type&&this.#Os.has(e.targetId)){const t=this.#Os.get(e.targetId);t&&(this.emit("targetGone",t),this.#Os.delete(e.targetId))}};#Hs=e=>{if(this.#yt.set(e.targetInfo.targetId,e.targetInfo),this.#Ns.has(e.targetInfo.targetId)||!this.#Os.has(e.targetInfo.targetId)||!e.targetInfo.attached)return;const t=this.#Os.get(e.targetInfo.targetId);if(!t)return;const r=t.url(),i=t._initializedDeferred.value()===cs.SUCCESS;if(function(e,t){return Boolean(e._subtype())&&!t.subtype}(t,e.targetInfo)){const e=t?._session();nt(e,"Target that is being activated is missing a CDPSession."),e.parentSession()?.emit(Bt.Swapped,e)}t._targetInfoChanged(e.targetInfo),i&&r!==t.url()&&this.emit("targetChanged",{target:t,wasInitialized:i,previousURL:r})};#xt=async(e,t)=>{const r=t.targetInfo,i=this.#C.session(t.sessionId);if(!i)throw new Error(`Session ${t.sessionId} was not created.`);const n=async()=>{await i.send("Runtime.runIfWaitingForDebugger").catch(yt),await e.send("Target.detachFromTarget",{sessionId:i.id()}).catch(yt)};if(!this.#C.isAutoAttached(r.targetId))return;if("service_worker"===r.type){if(this.#Ft(r.targetId),await n(),this.#Os.has(r.targetId))return;const e=this.#bt(r);return e._initialize(),this.#Os.set(r.targetId,e),void this.emit("targetAvailable",e)}const s=this.#Os.has(r.targetId),a=s?this.#Os.get(r.targetId):this.#bt(r,i,e instanceof Lt?e:void 0);if(this.#vt&&!this.#vt(a))return this.#Ns.add(r.targetId),this.#Ft(r.targetId),void await n();this.#$s(i),s?(i._setTarget(a),this.#qs.set(i.id(),this.#Os.get(r.targetId))):(a._initialize(),this.#Os.set(r.targetId,a),this.#qs.set(i.id(),a));const o=e instanceof Lt?e._target():null;o?._addChildTarget(a),e.emit(Bt.Ready,i),this.#Ct.delete(a._targetId),s||this.emit("targetAvailable",a),this.#Ft(),await Promise.all([i.send("Target.setAutoAttach",{waitForDebuggerOnStart:!0,flatten:!0,autoAttach:!0,filter:this.#js}),i.send("Runtime.runIfWaitingForDebugger")]).catch(yt)};#Ft(e){void 0!==e&&this.#Ct.delete(e),0===this.#Ct.size&&this.#St.resolve()}#Ss=(e,t)=>{const r=this.#qs.get(t.sessionId);this.#qs.delete(t.sessionId),r&&(e instanceof Lt&&e._target()._removeChildTarget(r),this.#Os.delete(r._targetId),this.emit("targetGone",r))}}
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */class ms extends Nt{protocol="cdp";static async _create(e,t,r,i,n,s,a,o,c,l=!0){const d=new ms(e,t,r,n,s,a,o,c,l);return i&&await t.send("Security.setIgnoreCertificateErrors",{ignore:!0}),await d._attach(),d}#Ms;#Ws;#C;#zs;#vt;#Us;#Gs;#Qs=new Map;#Gn;constructor(e,t,r,i,n,s,a,o,c=!0){super(),e=e||"chrome",this.#Ms=i,this.#Ws=n,this.#C=t,this.#zs=s||(()=>{}),this.#vt=a||(()=>!0),this.#Js(o),this.#Gn="firefox"===e?new Yr(t,this.#Xs,this.#vt):new fs(t,this.#Xs,this.#vt,c),this.#Gs=new zt(this.#C,this);for(const e of r)this.#Qs.set(e,new zt(this.#C,this,e))}#Ys=()=>{this.emit("disconnected",void 0)};async _attach(){this.#C.on(Bt.Disconnected,this.#Ys),this.#Gn.on("targetAvailable",this.#xt),this.#Gn.on("targetGone",this.#Ss),this.#Gn.on("targetChanged",this.#Zs),this.#Gn.on("targetDiscovered",this.#ea),await this.#Gn.initialize()}_detach(){this.#C.off(Bt.Disconnected,this.#Ys),this.#Gn.off("targetAvailable",this.#xt),this.#Gn.off("targetGone",this.#Ss),this.#Gn.off("targetChanged",this.#Zs),this.#Gn.off("targetDiscovered",this.#ea)}process(){return this.#Ws??null}_targetManager(){return this.#Gn}#Js(e){this.#Us=e||(e=>"page"===e.type()||"background_page"===e.type()||"webview"===e.type())}_getIsPageTargetCallback(){return this.#Us}async createBrowserContext(e={}){const{proxyServer:t,proxyBypassList:r}=e,{browserContextId:i}=await this.#C.send("Target.createBrowserContext",{proxyServer:t,proxyBypassList:r&&r.join(",")}),n=new zt(this.#C,this,i);return this.#Qs.set(i,n),n}browserContexts(){return[this.#Gs,...Array.from(this.#Qs.values())]}defaultBrowserContext(){return this.#Gs}async _disposeContext(e){e&&(await this.#C.send("Target.disposeBrowserContext",{browserContextId:e}),this.#Qs.delete(e))}#Xs=(e,t)=>{const{browserContextId:r}=e,i=r&&this.#Qs.has(r)?this.#Qs.get(r):this.#Gs;if(!i)throw new Error("Missing browser context");const n=t=>this.#C._createSession(e,t),s=new ps(e,t,i,this.#Gn,n);return e.url?.startsWith("devtools://")?new us(e,t,i,this.#Gn,n,this.#Ms??null):this.#Us(s)?new ds(e,t,i,this.#Gn,n,this.#Ms??null):"service_worker"===e.type||"shared_worker"===e.type?new hs(e,t,i,this.#Gn,n):s};#xt=async e=>{e._isTargetExposed()&&await e._initializedDeferred.valueOrThrow()===cs.SUCCESS&&(this.emit("targetcreated",e),e.browserContext().emit("targetcreated",e))};#Ss=async e=>{e._initializedDeferred.resolve(cs.ABORTED),e._isClosedDeferred.resolve(),e._isTargetExposed()&&await e._initializedDeferred.valueOrThrow()===cs.SUCCESS&&(this.emit("targetdestroyed",e),e.browserContext().emit("targetdestroyed",e))};#Zs=({target:e})=>{this.emit("targetchanged",e),e.browserContext().emit("targetchanged",e)};#ea=e=>{this.emit("targetdiscovered",e)};wsEndpoint(){return this.#C.url()}async newPage(){return await this.#Gs.newPage()}async _createPageInContext(e){const{targetId:t}=await this.#C.send("Target.createTarget",{url:"about:blank",browserContextId:e||void 0}),r=await this.waitForTarget((e=>e._targetId===t));if(!r)throw new Error(`Missing target for page (id = ${t})`);if(!(await r._initializedDeferred.valueOrThrow()===cs.SUCCESS))throw new Error(`Failed to create target for page (id = ${t})`);const i=await r.page();if(!i)throw new Error(`Failed to create a page for context (id = ${e})`);return i}targets(){return Array.from(this.#Gn.getAvailableTargets().values()).filter((e=>e._isTargetExposed()&&e._initializedDeferred.value()===cs.SUCCESS))}target(){const e=this.targets().find((e=>"browser"===e.type()));if(!e)throw new Error("Browser target is not found");return e}async version(){return(await this.#ta()).product}async userAgent(){return(await this.#ta()).userAgent}async close(){await this.#zs.call(null),await this.disconnect()}disconnect(){return this.#Gn.dispose(),this.#C.dispose(),this._detach(),Promise.resolve()}get connected(){return!this.#C._closed}#ta(){return this.#C.send("Browser.getVersion")}get debugInfo(){return{pendingProtocolErrors:this.#C.getPendingProtocolErrors()}}}export{ms as Browser,Lr as Connection,ln as ElementHandle,_n as Frame,as as Page,ls as Target};
